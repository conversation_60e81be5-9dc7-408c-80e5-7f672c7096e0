export interface Todo {
  id: string;
  title: string;
  description?: string;
  completed: boolean;
  priority: 'low' | 'medium' | 'high';
  category: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  dueDate?: Date;
}

export interface Category {
  id: string;
  name: string;
  color: string;
  icon?: string;
}

export interface TodoFilter {
  search: string;
  category: string;
  priority: string;
  completed: boolean | null;
  tags: string[];
}

export interface TodoStats {
  total: number;
  completed: number;
  pending: number;
  overdue: number;
}

export type Theme = 'light' | 'dark';

export type SortBy = 'createdAt' | 'updatedAt' | 'priority' | 'dueDate' | 'title';
export type SortOrder = 'asc' | 'desc';
