{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/augment%E9%A1%B9%E7%9B%AE/todo/src/store/todoStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { Todo, Category, TodoFilter, SortBy, SortOrder } from '@/types';\n\ninterface TodoStore {\n  todos: Todo[];\n  categories: Category[];\n  filter: TodoFilter;\n  sortBy: SortBy;\n  sortOrder: SortOrder;\n  \n  // Todo actions\n  addTodo: (todo: Omit<Todo, 'id' | 'createdAt' | 'updatedAt'>) => void;\n  updateTodo: (id: string, updates: Partial<Todo>) => void;\n  deleteTodo: (id: string) => void;\n  toggleTodo: (id: string) => void;\n  reorderTodos: (todos: Todo[]) => void;\n  \n  // Category actions\n  addCategory: (category: Omit<Category, 'id'>) => void;\n  updateCategory: (id: string, updates: Partial<Category>) => void;\n  deleteCategory: (id: string) => void;\n  \n  // Filter actions\n  setFilter: (filter: Partial<TodoFilter>) => void;\n  clearFilter: () => void;\n  \n  // Sort actions\n  setSortBy: (sortBy: SortBy) => void;\n  setSortOrder: (sortOrder: SortOrder) => void;\n  \n  // Utility functions\n  getFilteredTodos: () => Todo[];\n  getTodoStats: () => { total: number; completed: number; pending: number; overdue: number };\n}\n\nconst defaultCategories: Category[] = [\n  { id: '1', name: 'Personal', color: '#3B82F6', icon: 'User' },\n  { id: '2', name: 'Work', color: '#EF4444', icon: 'Briefcase' },\n  { id: '3', name: 'Shopping', color: '#10B981', icon: 'ShoppingCart' },\n  { id: '4', name: 'Health', color: '#F59E0B', icon: 'Heart' },\n];\n\nconst defaultFilter: TodoFilter = {\n  search: '',\n  category: '',\n  priority: '',\n  completed: null,\n  tags: [],\n};\n\nexport const useTodoStore = create<TodoStore>()(\n  persist(\n    (set, get) => ({\n      todos: [],\n      categories: defaultCategories,\n      filter: defaultFilter,\n      sortBy: 'createdAt',\n      sortOrder: 'desc',\n      \n      addTodo: (todoData) => {\n        const newTodo: Todo = {\n          ...todoData,\n          id: crypto.randomUUID(),\n          createdAt: new Date(),\n          updatedAt: new Date(),\n        };\n        set((state) => ({ todos: [newTodo, ...state.todos] }));\n      },\n      \n      updateTodo: (id, updates) => {\n        set((state) => ({\n          todos: state.todos.map((todo) =>\n            todo.id === id\n              ? { ...todo, ...updates, updatedAt: new Date() }\n              : todo\n          ),\n        }));\n      },\n      \n      deleteTodo: (id) => {\n        set((state) => ({\n          todos: state.todos.filter((todo) => todo.id !== id),\n        }));\n      },\n      \n      toggleTodo: (id) => {\n        set((state) => ({\n          todos: state.todos.map((todo) =>\n            todo.id === id\n              ? { ...todo, completed: !todo.completed, updatedAt: new Date() }\n              : todo\n          ),\n        }));\n      },\n      \n      reorderTodos: (todos) => {\n        set({ todos });\n      },\n      \n      addCategory: (categoryData) => {\n        const newCategory: Category = {\n          ...categoryData,\n          id: crypto.randomUUID(),\n        };\n        set((state) => ({ categories: [...state.categories, newCategory] }));\n      },\n      \n      updateCategory: (id, updates) => {\n        set((state) => ({\n          categories: state.categories.map((category) =>\n            category.id === id ? { ...category, ...updates } : category\n          ),\n        }));\n      },\n      \n      deleteCategory: (id) => {\n        set((state) => ({\n          categories: state.categories.filter((category) => category.id !== id),\n          todos: state.todos.map((todo) =>\n            todo.category === id ? { ...todo, category: '' } : todo\n          ),\n        }));\n      },\n      \n      setFilter: (newFilter) => {\n        set((state) => ({ filter: { ...state.filter, ...newFilter } }));\n      },\n      \n      clearFilter: () => {\n        set({ filter: defaultFilter });\n      },\n      \n      setSortBy: (sortBy) => {\n        set({ sortBy });\n      },\n      \n      setSortOrder: (sortOrder) => {\n        set({ sortOrder });\n      },\n      \n      getFilteredTodos: () => {\n        const { todos, filter, sortBy, sortOrder } = get();\n        let filtered = todos;\n        \n        // Apply filters\n        if (filter.search) {\n          const searchLower = filter.search.toLowerCase();\n          filtered = filtered.filter(\n            (todo) =>\n              todo.title.toLowerCase().includes(searchLower) ||\n              todo.description?.toLowerCase().includes(searchLower)\n          );\n        }\n        \n        if (filter.category) {\n          filtered = filtered.filter((todo) => todo.category === filter.category);\n        }\n        \n        if (filter.priority) {\n          filtered = filtered.filter((todo) => todo.priority === filter.priority);\n        }\n        \n        if (filter.completed !== null) {\n          filtered = filtered.filter((todo) => todo.completed === filter.completed);\n        }\n        \n        if (filter.tags.length > 0) {\n          filtered = filtered.filter((todo) =>\n            filter.tags.some((tag) => todo.tags.includes(tag))\n          );\n        }\n        \n        // Apply sorting\n        filtered.sort((a, b) => {\n          let aValue: any = a[sortBy];\n          let bValue: any = b[sortBy];\n          \n          if (sortBy === 'priority') {\n            const priorityOrder = { high: 3, medium: 2, low: 1 };\n            aValue = priorityOrder[a.priority];\n            bValue = priorityOrder[b.priority];\n          }\n          \n          if (aValue instanceof Date) {\n            aValue = aValue.getTime();\n          }\n          if (bValue instanceof Date) {\n            bValue = bValue.getTime();\n          }\n          \n          if (typeof aValue === 'string') {\n            aValue = aValue.toLowerCase();\n          }\n          if (typeof bValue === 'string') {\n            bValue = bValue.toLowerCase();\n          }\n          \n          if (sortOrder === 'asc') {\n            return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;\n          } else {\n            return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;\n          }\n        });\n        \n        return filtered;\n      },\n      \n      getTodoStats: () => {\n        const { todos } = get();\n        const now = new Date();\n        \n        return {\n          total: todos.length,\n          completed: todos.filter((todo) => todo.completed).length,\n          pending: todos.filter((todo) => !todo.completed).length,\n          overdue: todos.filter(\n            (todo) => !todo.completed && todo.dueDate && todo.dueDate < now\n          ).length,\n        };\n      },\n    }),\n    {\n      name: 'todo-storage',\n      partialize: (state) => ({\n        todos: state.todos,\n        categories: state.categories,\n      }),\n    }\n  )\n);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAmCA,MAAM,oBAAgC;IACpC;QAAE,IAAI;QAAK,MAAM;QAAY,OAAO;QAAW,MAAM;IAAO;IAC5D;QAAE,IAAI;QAAK,MAAM;QAAQ,OAAO;QAAW,MAAM;IAAY;IAC7D;QAAE,IAAI;QAAK,MAAM;QAAY,OAAO;QAAW,MAAM;IAAe;IACpE;QAAE,IAAI;QAAK,MAAM;QAAU,OAAO;QAAW,MAAM;IAAQ;CAC5D;AAED,MAAM,gBAA4B;IAChC,QAAQ;IACR,UAAU;IACV,UAAU;IACV,WAAW;IACX,MAAM,EAAE;AACV;AAEO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,OAAO,EAAE;QACT,YAAY;QACZ,QAAQ;QACR,QAAQ;QACR,WAAW;QAEX,SAAS,CAAC;YACR,MAAM,UAAgB;gBACpB,GAAG,QAAQ;gBACX,IAAI,OAAO,UAAU;gBACrB,WAAW,IAAI;gBACf,WAAW,IAAI;YACjB;YACA,IAAI,CAAC,QAAU,CAAC;oBAAE,OAAO;wBAAC;2BAAY,MAAM,KAAK;qBAAC;gBAAC,CAAC;QACtD;QAEA,YAAY,CAAC,IAAI;YACf,IAAI,CAAC,QAAU,CAAC;oBACd,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,OACtB,KAAK,EAAE,KAAK,KACR;4BAAE,GAAG,IAAI;4BAAE,GAAG,OAAO;4BAAE,WAAW,IAAI;wBAAO,IAC7C;gBAER,CAAC;QACH;QAEA,YAAY,CAAC;YACX,IAAI,CAAC,QAAU,CAAC;oBACd,OAAO,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;gBAClD,CAAC;QACH;QAEA,YAAY,CAAC;YACX,IAAI,CAAC,QAAU,CAAC;oBACd,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,OACtB,KAAK,EAAE,KAAK,KACR;4BAAE,GAAG,IAAI;4BAAE,WAAW,CAAC,KAAK,SAAS;4BAAE,WAAW,IAAI;wBAAO,IAC7D;gBAER,CAAC;QACH;QAEA,cAAc,CAAC;YACb,IAAI;gBAAE;YAAM;QACd;QAEA,aAAa,CAAC;YACZ,MAAM,cAAwB;gBAC5B,GAAG,YAAY;gBACf,IAAI,OAAO,UAAU;YACvB;YACA,IAAI,CAAC,QAAU,CAAC;oBAAE,YAAY;2BAAI,MAAM,UAAU;wBAAE;qBAAY;gBAAC,CAAC;QACpE;QAEA,gBAAgB,CAAC,IAAI;YACnB,IAAI,CAAC,QAAU,CAAC;oBACd,YAAY,MAAM,UAAU,CAAC,GAAG,CAAC,CAAC,WAChC,SAAS,EAAE,KAAK,KAAK;4BAAE,GAAG,QAAQ;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAEvD,CAAC;QACH;QAEA,gBAAgB,CAAC;YACf,IAAI,CAAC,QAAU,CAAC;oBACd,YAAY,MAAM,UAAU,CAAC,MAAM,CAAC,CAAC,WAAa,SAAS,EAAE,KAAK;oBAClE,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,OACtB,KAAK,QAAQ,KAAK,KAAK;4BAAE,GAAG,IAAI;4BAAE,UAAU;wBAAG,IAAI;gBAEvD,CAAC;QACH;QAEA,WAAW,CAAC;YACV,IAAI,CAAC,QAAU,CAAC;oBAAE,QAAQ;wBAAE,GAAG,MAAM,MAAM;wBAAE,GAAG,SAAS;oBAAC;gBAAE,CAAC;QAC/D;QAEA,aAAa;YACX,IAAI;gBAAE,QAAQ;YAAc;QAC9B;QAEA,WAAW,CAAC;YACV,IAAI;gBAAE;YAAO;QACf;QAEA,cAAc,CAAC;YACb,IAAI;gBAAE;YAAU;QAClB;QAEA,kBAAkB;YAChB,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG;YAC7C,IAAI,WAAW;YAEf,gBAAgB;YAChB,IAAI,OAAO,MAAM,EAAE;gBACjB,MAAM,cAAc,OAAO,MAAM,CAAC,WAAW;gBAC7C,WAAW,SAAS,MAAM,CACxB,CAAC,OACC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAClC,KAAK,WAAW,EAAE,cAAc,SAAS;YAE/C;YAEA,IAAI,OAAO,QAAQ,EAAE;gBACnB,WAAW,SAAS,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,OAAO,QAAQ;YACxE;YAEA,IAAI,OAAO,QAAQ,EAAE;gBACnB,WAAW,SAAS,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,OAAO,QAAQ;YACxE;YAEA,IAAI,OAAO,SAAS,KAAK,MAAM;gBAC7B,WAAW,SAAS,MAAM,CAAC,CAAC,OAAS,KAAK,SAAS,KAAK,OAAO,SAAS;YAC1E;YAEA,IAAI,OAAO,IAAI,CAAC,MAAM,GAAG,GAAG;gBAC1B,WAAW,SAAS,MAAM,CAAC,CAAC,OAC1B,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,MAAQ,KAAK,IAAI,CAAC,QAAQ,CAAC;YAEjD;YAEA,gBAAgB;YAChB,SAAS,IAAI,CAAC,CAAC,GAAG;gBAChB,IAAI,SAAc,CAAC,CAAC,OAAO;gBAC3B,IAAI,SAAc,CAAC,CAAC,OAAO;gBAE3B,IAAI,WAAW,YAAY;oBACzB,MAAM,gBAAgB;wBAAE,MAAM;wBAAG,QAAQ;wBAAG,KAAK;oBAAE;oBACnD,SAAS,aAAa,CAAC,EAAE,QAAQ,CAAC;oBAClC,SAAS,aAAa,CAAC,EAAE,QAAQ,CAAC;gBACpC;gBAEA,IAAI,kBAAkB,MAAM;oBAC1B,SAAS,OAAO,OAAO;gBACzB;gBACA,IAAI,kBAAkB,MAAM;oBAC1B,SAAS,OAAO,OAAO;gBACzB;gBAEA,IAAI,OAAO,WAAW,UAAU;oBAC9B,SAAS,OAAO,WAAW;gBAC7B;gBACA,IAAI,OAAO,WAAW,UAAU;oBAC9B,SAAS,OAAO,WAAW;gBAC7B;gBAEA,IAAI,cAAc,OAAO;oBACvB,OAAO,SAAS,SAAS,CAAC,IAAI,SAAS,SAAS,IAAI;gBACtD,OAAO;oBACL,OAAO,SAAS,SAAS,CAAC,IAAI,SAAS,SAAS,IAAI;gBACtD;YACF;YAEA,OAAO;QACT;QAEA,cAAc;YACZ,MAAM,EAAE,KAAK,EAAE,GAAG;YAClB,MAAM,MAAM,IAAI;YAEhB,OAAO;gBACL,OAAO,MAAM,MAAM;gBACnB,WAAW,MAAM,MAAM,CAAC,CAAC,OAAS,KAAK,SAAS,EAAE,MAAM;gBACxD,SAAS,MAAM,MAAM,CAAC,CAAC,OAAS,CAAC,KAAK,SAAS,EAAE,MAAM;gBACvD,SAAS,MAAM,MAAM,CACnB,CAAC,OAAS,CAAC,KAAK,SAAS,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,GAAG,KAC5D,MAAM;YACV;QACF;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,OAAO,MAAM,KAAK;YAClB,YAAY,MAAM,UAAU;QAC9B,CAAC;AACH", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/augment%E9%A1%B9%E7%9B%AE/todo/src/utils/index.ts"], "sourcesContent": ["import { format, isToday, isTomorrow, isYesterday, isPast } from 'date-fns';\nimport { clsx, type ClassValue } from 'clsx';\n\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\nexport function formatDate(date: Date): string {\n  if (isToday(date)) {\n    return 'Today';\n  }\n  if (isTomorrow(date)) {\n    return 'Tomorrow';\n  }\n  if (isYesterday(date)) {\n    return 'Yesterday';\n  }\n  return format(date, 'MMM dd, yyyy');\n}\n\nexport function formatDateTime(date: Date): string {\n  return format(date, 'MMM dd, yyyy HH:mm');\n}\n\nexport function isOverdue(date: Date): boolean {\n  return isPast(date) && !isToday(date);\n}\n\nexport function getPriorityColor(priority: 'low' | 'medium' | 'high'): string {\n  switch (priority) {\n    case 'high':\n      return 'text-red-600 bg-red-50 border-red-200';\n    case 'medium':\n      return 'text-yellow-600 bg-yellow-50 border-yellow-200';\n    case 'low':\n      return 'text-green-600 bg-green-50 border-green-200';\n    default:\n      return 'text-gray-600 bg-gray-50 border-gray-200';\n  }\n}\n\nexport function getPriorityColorDark(priority: 'low' | 'medium' | 'high'): string {\n  switch (priority) {\n    case 'high':\n      return 'text-red-400 bg-red-900/20 border-red-800';\n    case 'medium':\n      return 'text-yellow-400 bg-yellow-900/20 border-yellow-800';\n    case 'low':\n      return 'text-green-400 bg-green-900/20 border-green-800';\n    default:\n      return 'text-gray-400 bg-gray-800/20 border-gray-700';\n  }\n}\n\nexport function generateId(): string {\n  return crypto.randomUUID();\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAEO,SAAS,WAAW,IAAU;IACnC,IAAI,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,OAAO;QACjB,OAAO;IACT;IACA,IAAI,CAAA,GAAA,yIAAA,CAAA,aAAU,AAAD,EAAE,OAAO;QACpB,OAAO;IACT;IACA,IAAI,CAAA,GAAA,0IAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACrB,OAAO;IACT;IACA,OAAO,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;AACtB;AAEO,SAAS,eAAe,IAAU;IACvC,OAAO,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;AACtB;AAEO,SAAS,UAAU,IAAU;IAClC,OAAO,CAAA,GAAA,qIAAA,CAAA,SAAM,AAAD,EAAE,SAAS,CAAC,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE;AAClC;AAEO,SAAS,iBAAiB,QAAmC;IAClE,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,qBAAqB,QAAmC;IACtE,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS;IACd,OAAO,OAAO,UAAU;AAC1B;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/augment%E9%A1%B9%E7%9B%AE/todo/src/components/AddTodoForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Plus, Calendar, Tag, X } from 'lucide-react';\nimport { useTodoStore } from '@/store/todoStore';\nimport { cn } from '@/utils';\n\nexport function AddTodoForm() {\n  const { addTodo, categories } = useTodoStore();\n  const [isExpanded, setIsExpanded] = useState(false);\n  const [title, setTitle] = useState('');\n  const [description, setDescription] = useState('');\n  const [priority, setPriority] = useState<'low' | 'medium' | 'high'>('medium');\n  const [category, setCategory] = useState('');\n  const [dueDate, setDueDate] = useState('');\n  const [tags, setTags] = useState<string[]>([]);\n  const [tagInput, setTagInput] = useState('');\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!title.trim()) return;\n\n    addTodo({\n      title: title.trim(),\n      description: description.trim() || undefined,\n      priority,\n      category,\n      tags,\n      completed: false,\n      dueDate: dueDate ? new Date(dueDate) : undefined,\n    });\n\n    // Reset form\n    setTitle('');\n    setDescription('');\n    setPriority('medium');\n    setCategory('');\n    setDueDate('');\n    setTags([]);\n    setTagInput('');\n    setIsExpanded(false);\n  };\n\n  const addTag = () => {\n    const tag = tagInput.trim();\n    if (tag && !tags.includes(tag)) {\n      setTags([...tags, tag]);\n      setTagInput('');\n    }\n  };\n\n  const removeTag = (tagToRemove: string) => {\n    setTags(tags.filter(tag => tag !== tagToRemove));\n  };\n\n  const handleTagKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter') {\n      e.preventDefault();\n      addTag();\n    }\n  };\n\n  return (\n    <motion.div\n      layout\n      className=\"bg-card border border-border rounded-lg shadow-sm\"\n    >\n      <form onSubmit={handleSubmit}>\n        {/* Quick Add Input */}\n        <div className=\"p-4\">\n          <div className=\"flex gap-3\">\n            <motion.button\n              type=\"button\"\n              onClick={() => setIsExpanded(!isExpanded)}\n              className=\"flex-shrink-0 w-10 h-10 rounded-lg bg-primary text-primary-foreground flex items-center justify-center hover:bg-primary/90 transition-colors\"\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <motion.div\n                animate={{ rotate: isExpanded ? 45 : 0 }}\n                transition={{ duration: 0.2 }}\n              >\n                <Plus className=\"h-5 w-5\" />\n              </motion.div>\n            </motion.button>\n            \n            <input\n              type=\"text\"\n              value={title}\n              onChange={(e) => setTitle(e.target.value)}\n              onFocus={() => setIsExpanded(true)}\n              placeholder=\"Add a new task...\"\n              className=\"flex-1 px-3 py-2 bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary text-sm\"\n            />\n          </div>\n        </div>\n\n        {/* Expanded Form */}\n        <AnimatePresence>\n          {isExpanded && (\n            <motion.div\n              initial={{ height: 0, opacity: 0 }}\n              animate={{ height: 'auto', opacity: 1 }}\n              exit={{ height: 0, opacity: 0 }}\n              transition={{ duration: 0.2 }}\n              className=\"overflow-hidden\"\n            >\n              <div className=\"px-4 pb-4 space-y-4 border-t border-border\">\n                {/* Description */}\n                <div className=\"pt-4\">\n                  <textarea\n                    value={description}\n                    onChange={(e) => setDescription(e.target.value)}\n                    placeholder=\"Description (optional)\"\n                    className=\"w-full px-3 py-2 bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary text-sm resize-none\"\n                    rows={2}\n                  />\n                </div>\n\n                {/* Priority and Category */}\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium mb-2\">Priority</label>\n                    <select\n                      value={priority}\n                      onChange={(e) => setPriority(e.target.value as 'low' | 'medium' | 'high')}\n                      className=\"w-full px-3 py-2 bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary text-sm\"\n                    >\n                      <option value=\"low\">Low</option>\n                      <option value=\"medium\">Medium</option>\n                      <option value=\"high\">High</option>\n                    </select>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium mb-2\">Category</label>\n                    <select\n                      value={category}\n                      onChange={(e) => setCategory(e.target.value)}\n                      className=\"w-full px-3 py-2 bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary text-sm\"\n                    >\n                      <option value=\"\">No category</option>\n                      {categories.map((cat) => (\n                        <option key={cat.id} value={cat.id}>\n                          {cat.name}\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n                </div>\n\n                {/* Due Date */}\n                <div>\n                  <label className=\"block text-sm font-medium mb-2\">\n                    <Calendar className=\"inline h-4 w-4 mr-1\" />\n                    Due Date\n                  </label>\n                  <input\n                    type=\"datetime-local\"\n                    value={dueDate}\n                    onChange={(e) => setDueDate(e.target.value)}\n                    className=\"w-full px-3 py-2 bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary text-sm\"\n                  />\n                </div>\n\n                {/* Tags */}\n                <div>\n                  <label className=\"block text-sm font-medium mb-2\">\n                    <Tag className=\"inline h-4 w-4 mr-1\" />\n                    Tags\n                  </label>\n                  <div className=\"flex flex-wrap gap-2 mb-2\">\n                    {tags.map((tag) => (\n                      <motion.span\n                        key={tag}\n                        initial={{ scale: 0 }}\n                        animate={{ scale: 1 }}\n                        className=\"inline-flex items-center gap-1 px-2 py-1 bg-accent text-accent-foreground rounded-full text-xs\"\n                      >\n                        {tag}\n                        <button\n                          type=\"button\"\n                          onClick={() => removeTag(tag)}\n                          className=\"hover:text-destructive\"\n                        >\n                          <X className=\"h-3 w-3\" />\n                        </button>\n                      </motion.span>\n                    ))}\n                  </div>\n                  <div className=\"flex gap-2\">\n                    <input\n                      type=\"text\"\n                      value={tagInput}\n                      onChange={(e) => setTagInput(e.target.value)}\n                      onKeyPress={handleTagKeyPress}\n                      placeholder=\"Add tag...\"\n                      className=\"flex-1 px-3 py-2 bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary text-sm\"\n                    />\n                    <button\n                      type=\"button\"\n                      onClick={addTag}\n                      className=\"px-3 py-2 bg-secondary text-secondary-foreground rounded-lg hover:bg-secondary/80 text-sm\"\n                    >\n                      Add\n                    </button>\n                  </div>\n                </div>\n\n                {/* Actions */}\n                <div className=\"flex gap-2 pt-2\">\n                  <button\n                    type=\"submit\"\n                    disabled={!title.trim()}\n                    className={cn(\n                      'px-4 py-2 rounded-lg text-sm font-medium transition-colors',\n                      title.trim()\n                        ? 'bg-primary text-primary-foreground hover:bg-primary/90'\n                        : 'bg-muted text-muted-foreground cursor-not-allowed'\n                    )}\n                  >\n                    Add Task\n                  </button>\n                  <button\n                    type=\"button\"\n                    onClick={() => setIsExpanded(false)}\n                    className=\"px-4 py-2 bg-secondary text-secondary-foreground rounded-lg hover:bg-secondary/80 text-sm\"\n                  >\n                    Cancel\n                  </button>\n                </div>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </form>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AAQO,SAAS;IACd,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B;IACpE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,CAAC,MAAM,IAAI,IAAI;QAEnB,QAAQ;YACN,OAAO,MAAM,IAAI;YACjB,aAAa,YAAY,IAAI,MAAM;YACnC;YACA;YACA;YACA,WAAW;YACX,SAAS,UAAU,IAAI,KAAK,WAAW;QACzC;QAEA,aAAa;QACb,SAAS;QACT,eAAe;QACf,YAAY;QACZ,YAAY;QACZ,WAAW;QACX,QAAQ,EAAE;QACV,YAAY;QACZ,cAAc;IAChB;IAEA,MAAM,SAAS;QACb,MAAM,MAAM,SAAS,IAAI;QACzB,IAAI,OAAO,CAAC,KAAK,QAAQ,CAAC,MAAM;YAC9B,QAAQ;mBAAI;gBAAM;aAAI;YACtB,YAAY;QACd;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,QAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,QAAQ;IACrC;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB,EAAE,cAAc;YAChB;QACF;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,MAAM;QACN,WAAU;kBAEV,cAAA,8OAAC;YAAK,UAAU;;8BAEd,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,MAAK;gCACL,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;gCACV,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;0CAExB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,QAAQ,aAAa,KAAK;oCAAE;oCACvC,YAAY;wCAAE,UAAU;oCAAI;8CAE5B,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAIpB,8OAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gCACxC,SAAS,IAAM,cAAc;gCAC7B,aAAY;gCACZ,WAAU;;;;;;;;;;;;;;;;;8BAMhB,8OAAC,yLAAA,CAAA,kBAAe;8BACb,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,QAAQ;4BAAG,SAAS;wBAAE;wBACjC,SAAS;4BAAE,QAAQ;4BAAQ,SAAS;wBAAE;wBACtC,MAAM;4BAAE,QAAQ;4BAAG,SAAS;wBAAE;wBAC9B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,aAAY;wCACZ,WAAU;wCACV,MAAM;;;;;;;;;;;8CAKV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAiC;;;;;;8DAClD,8OAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC3C,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAM;;;;;;sEACpB,8OAAC;4DAAO,OAAM;sEAAS;;;;;;sEACvB,8OAAC;4DAAO,OAAM;sEAAO;;;;;;;;;;;;;;;;;;sDAIzB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAiC;;;;;;8DAClD,8OAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC3C,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,WAAW,GAAG,CAAC,CAAC,oBACf,8OAAC;gEAAoB,OAAO,IAAI,EAAE;0EAC/B,IAAI,IAAI;+DADE,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;8CAS3B,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;;8DACf,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAwB;;;;;;;sDAG9C,8OAAC;4CACC,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;4CAC1C,WAAU;;;;;;;;;;;;8CAKd,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;;8DACf,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAwB;;;;;;;sDAGzC,8OAAC;4CAAI,WAAU;sDACZ,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oDAEV,SAAS;wDAAE,OAAO;oDAAE;oDACpB,SAAS;wDAAE,OAAO;oDAAE;oDACpB,WAAU;;wDAET;sEACD,8OAAC;4DACC,MAAK;4DACL,SAAS,IAAM,UAAU;4DACzB,WAAU;sEAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gEAAC,WAAU;;;;;;;;;;;;mDAXV;;;;;;;;;;sDAgBX,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC3C,YAAY;oDACZ,aAAY;oDACZ,WAAU;;;;;;8DAEZ,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;8CAOL,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,UAAU,CAAC,MAAM,IAAI;4CACrB,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,8DACA,MAAM,IAAI,KACN,2DACA;sDAEP;;;;;;sDAGD,8OAAC;4CACC,MAAK;4CACL,SAAS,IAAM,cAAc;4CAC7B,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}, {"offset": {"line": 776, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/augment%E9%A1%B9%E7%9B%AE/todo/src/components/TodoFilters.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { \n  Search, \n  Filter, \n  X, \n  ChevronDown,\n  SortAsc,\n  SortDesc\n} from 'lucide-react';\nimport { useTodoStore } from '@/store/todoStore';\nimport { SortBy, SortOrder } from '@/types';\nimport { cn } from '@/utils';\n\nexport function TodoFilters() {\n  const { \n    filter, \n    setFilter, \n    clearFilter, \n    categories,\n    sortBy,\n    sortOrder,\n    setSortBy,\n    setSortOrder\n  } = useTodoStore();\n  \n  const [isExpanded, setIsExpanded] = useState(false);\n\n  const handleSearchChange = (value: string) => {\n    setFilter({ search: value });\n  };\n\n  const handleClearFilters = () => {\n    clearFilter();\n    setIsExpanded(false);\n  };\n\n  const hasActiveFilters = filter.category || filter.priority || filter.completed !== null || filter.tags.length > 0;\n\n  const sortOptions: { value: SortBy; label: string }[] = [\n    { value: 'createdAt', label: 'Created Date' },\n    { value: 'updatedAt', label: 'Updated Date' },\n    { value: 'priority', label: 'Priority' },\n    { value: 'dueDate', label: 'Due Date' },\n    { value: 'title', label: 'Title' },\n  ];\n\n  return (\n    <motion.div\n      layout\n      className=\"bg-card border border-border rounded-lg shadow-sm\"\n    >\n      {/* Search Bar */}\n      <div className=\"p-4\">\n        <div className=\"flex gap-3\">\n          <div className=\"relative flex-1\">\n            <Search className=\"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n            <input\n              type=\"text\"\n              value={filter.search}\n              onChange={(e) => handleSearchChange(e.target.value)}\n              placeholder=\"Search tasks...\"\n              className=\"w-full pl-10 pr-4 py-2 bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary text-sm\"\n            />\n          </div>\n          \n          <motion.button\n            onClick={() => setIsExpanded(!isExpanded)}\n            className={cn(\n              'flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors',\n              hasActiveFilters || isExpanded\n                ? 'bg-primary text-primary-foreground'\n                : 'bg-secondary text-secondary-foreground hover:bg-secondary/80'\n            )}\n            whileHover={{ scale: 1.02 }}\n            whileTap={{ scale: 0.98 }}\n          >\n            <Filter className=\"h-4 w-4\" />\n            Filters\n            <motion.div\n              animate={{ rotate: isExpanded ? 180 : 0 }}\n              transition={{ duration: 0.2 }}\n            >\n              <ChevronDown className=\"h-4 w-4\" />\n            </motion.div>\n          </motion.button>\n        </div>\n      </div>\n\n      {/* Expanded Filters */}\n      <AnimatePresence>\n        {isExpanded && (\n          <motion.div\n            initial={{ height: 0, opacity: 0 }}\n            animate={{ height: 'auto', opacity: 1 }}\n            exit={{ height: 0, opacity: 0 }}\n            transition={{ duration: 0.2 }}\n            className=\"overflow-hidden border-t border-border\"\n          >\n            <div className=\"p-4 space-y-4\">\n              {/* Category and Priority Filters */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium mb-2\">Category</label>\n                  <select\n                    value={filter.category}\n                    onChange={(e) => setFilter({ category: e.target.value })}\n                    className=\"w-full px-3 py-2 bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary text-sm\"\n                  >\n                    <option value=\"\">All categories</option>\n                    {categories.map((category) => (\n                      <option key={category.id} value={category.id}>\n                        {category.name}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium mb-2\">Priority</label>\n                  <select\n                    value={filter.priority}\n                    onChange={(e) => setFilter({ priority: e.target.value })}\n                    className=\"w-full px-3 py-2 bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary text-sm\"\n                  >\n                    <option value=\"\">All priorities</option>\n                    <option value=\"high\">High</option>\n                    <option value=\"medium\">Medium</option>\n                    <option value=\"low\">Low</option>\n                  </select>\n                </div>\n              </div>\n\n              {/* Status Filter */}\n              <div>\n                <label className=\"block text-sm font-medium mb-2\">Status</label>\n                <div className=\"flex gap-2\">\n                  <button\n                    onClick={() => setFilter({ completed: null })}\n                    className={cn(\n                      'px-3 py-2 rounded-lg text-sm transition-colors',\n                      filter.completed === null\n                        ? 'bg-primary text-primary-foreground'\n                        : 'bg-secondary text-secondary-foreground hover:bg-secondary/80'\n                    )}\n                  >\n                    All\n                  </button>\n                  <button\n                    onClick={() => setFilter({ completed: false })}\n                    className={cn(\n                      'px-3 py-2 rounded-lg text-sm transition-colors',\n                      filter.completed === false\n                        ? 'bg-primary text-primary-foreground'\n                        : 'bg-secondary text-secondary-foreground hover:bg-secondary/80'\n                    )}\n                  >\n                    Pending\n                  </button>\n                  <button\n                    onClick={() => setFilter({ completed: true })}\n                    className={cn(\n                      'px-3 py-2 rounded-lg text-sm transition-colors',\n                      filter.completed === true\n                        ? 'bg-primary text-primary-foreground'\n                        : 'bg-secondary text-secondary-foreground hover:bg-secondary/80'\n                    )}\n                  >\n                    Completed\n                  </button>\n                </div>\n              </div>\n\n              {/* Sort Options */}\n              <div>\n                <label className=\"block text-sm font-medium mb-2\">Sort by</label>\n                <div className=\"flex gap-2\">\n                  <select\n                    value={sortBy}\n                    onChange={(e) => setSortBy(e.target.value as SortBy)}\n                    className=\"flex-1 px-3 py-2 bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary text-sm\"\n                  >\n                    {sortOptions.map((option) => (\n                      <option key={option.value} value={option.value}>\n                        {option.label}\n                      </option>\n                    ))}\n                  </select>\n                  <button\n                    onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}\n                    className=\"px-3 py-2 bg-secondary text-secondary-foreground rounded-lg hover:bg-secondary/80 transition-colors\"\n                  >\n                    {sortOrder === 'asc' ? <SortAsc className=\"h-4 w-4\" /> : <SortDesc className=\"h-4 w-4\" />}\n                  </button>\n                </div>\n              </div>\n\n              {/* Clear Filters */}\n              {hasActiveFilters && (\n                <motion.button\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  onClick={handleClearFilters}\n                  className=\"flex items-center gap-2 px-3 py-2 text-sm text-destructive hover:bg-destructive/10 rounded-lg transition-colors\"\n                >\n                  <X className=\"h-4 w-4\" />\n                  Clear all filters\n                </motion.button>\n              )}\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AAEA;AAdA;;;;;;;AAgBO,SAAS;IACd,MAAM,EACJ,MAAM,EACN,SAAS,EACT,WAAW,EACX,UAAU,EACV,MAAM,EACN,SAAS,EACT,SAAS,EACT,YAAY,EACb,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAEf,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,qBAAqB,CAAC;QAC1B,UAAU;YAAE,QAAQ;QAAM;IAC5B;IAEA,MAAM,qBAAqB;QACzB;QACA,cAAc;IAChB;IAEA,MAAM,mBAAmB,OAAO,QAAQ,IAAI,OAAO,QAAQ,IAAI,OAAO,SAAS,KAAK,QAAQ,OAAO,IAAI,CAAC,MAAM,GAAG;IAEjH,MAAM,cAAkD;QACtD;YAAE,OAAO;YAAa,OAAO;QAAe;QAC5C;YAAE,OAAO;YAAa,OAAO;QAAe;QAC5C;YAAE,OAAO;YAAY,OAAO;QAAW;QACvC;YAAE,OAAO;YAAW,OAAO;QAAW;QACtC;YAAE,OAAO;YAAS,OAAO;QAAQ;KAClC;IAED,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,MAAM;QACN,WAAU;;0BAGV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCACC,MAAK;oCACL,OAAO,OAAO,MAAM;oCACpB,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;oCAClD,aAAY;oCACZ,WAAU;;;;;;;;;;;;sCAId,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,SAAS,IAAM,cAAc,CAAC;4BAC9B,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,sFACA,oBAAoB,aAChB,uCACA;4BAEN,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;;8CAExB,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;8CAE9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,QAAQ,aAAa,MAAM;oCAAE;oCACxC,YAAY;wCAAE,UAAU;oCAAI;8CAE5B,cAAA,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO/B,8OAAC,yLAAA,CAAA,kBAAe;0BACb,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,QAAQ;wBAAG,SAAS;oBAAE;oBACjC,SAAS;wBAAE,QAAQ;wBAAQ,SAAS;oBAAE;oBACtC,MAAM;wBAAE,QAAQ;wBAAG,SAAS;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,8OAAC;gDACC,OAAO,OAAO,QAAQ;gDACtB,UAAU,CAAC,IAAM,UAAU;wDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACtD,WAAU;;kEAEV,8OAAC;wDAAO,OAAM;kEAAG;;;;;;oDAChB,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;4DAAyB,OAAO,SAAS,EAAE;sEACzC,SAAS,IAAI;2DADH,SAAS,EAAE;;;;;;;;;;;;;;;;;kDAO9B,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,8OAAC;gDACC,OAAO,OAAO,QAAQ;gDACtB,UAAU,CAAC,IAAM,UAAU;wDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACtD,WAAU;;kEAEV,8OAAC;wDAAO,OAAM;kEAAG;;;;;;kEACjB,8OAAC;wDAAO,OAAM;kEAAO;;;;;;kEACrB,8OAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,8OAAC;wDAAO,OAAM;kEAAM;;;;;;;;;;;;;;;;;;;;;;;;0CAM1B,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAiC;;;;;;kDAClD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,UAAU;wDAAE,WAAW;oDAAK;gDAC3C,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,kDACA,OAAO,SAAS,KAAK,OACjB,uCACA;0DAEP;;;;;;0DAGD,8OAAC;gDACC,SAAS,IAAM,UAAU;wDAAE,WAAW;oDAAM;gDAC5C,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,kDACA,OAAO,SAAS,KAAK,QACjB,uCACA;0DAEP;;;;;;0DAGD,8OAAC;gDACC,SAAS,IAAM,UAAU;wDAAE,WAAW;oDAAK;gDAC3C,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,kDACA,OAAO,SAAS,KAAK,OACjB,uCACA;0DAEP;;;;;;;;;;;;;;;;;;0CAOL,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAiC;;;;;;kDAClD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gDACzC,WAAU;0DAET,YAAY,GAAG,CAAC,CAAC,uBAChB,8OAAC;wDAA0B,OAAO,OAAO,KAAK;kEAC3C,OAAO,KAAK;uDADF,OAAO,KAAK;;;;;;;;;;0DAK7B,8OAAC;gDACC,SAAS,IAAM,aAAa,cAAc,QAAQ,SAAS;gDAC3D,WAAU;0DAET,cAAc,sBAAQ,8OAAC,8NAAA,CAAA,UAAO;oDAAC,WAAU;;;;;yEAAe,8OAAC,iOAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;4BAMlF,kCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,SAAS;gCACT,WAAU;;kDAEV,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU3C", "debugId": null}}, {"offset": {"line": 1235, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/augment%E9%A1%B9%E7%9B%AE/todo/src/components/TodoItem.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { \n  Check, \n  Edit2, \n  Trash2, \n  Calendar, \n  Tag, \n  AlertCircle,\n  Clock,\n  GripVertical\n} from 'lucide-react';\nimport { Todo, Category } from '@/types';\nimport { useTodoStore } from '@/store/todoStore';\nimport { formatDate, isOverdue, getPriorityColor, getPriorityColorDark, cn } from '@/utils';\nimport { useThemeStore } from '@/store/themeStore';\n\ninterface TodoItemProps {\n  todo: Todo;\n  isDragging?: boolean;\n}\n\nexport function TodoItem({ todo, isDragging }: TodoItemProps) {\n  const { updateTodo, deleteTodo, toggleTodo, categories } = useTodoStore();\n  const { theme } = useThemeStore();\n  const [isEditing, setIsEditing] = useState(false);\n  const [editTitle, setEditTitle] = useState(todo.title);\n  const [editDescription, setEditDescription] = useState(todo.description || '');\n\n  const category = categories.find(c => c.id === todo.category);\n  const priorityClass = theme === 'dark' ? getPriorityColorDark(todo.priority) : getPriorityColor(todo.priority);\n  const isTaskOverdue = todo.dueDate && !todo.completed && isOverdue(todo.dueDate);\n\n  const handleSave = () => {\n    if (editTitle.trim()) {\n      updateTodo(todo.id, {\n        title: editTitle.trim(),\n        description: editDescription.trim() || undefined,\n      });\n      setIsEditing(false);\n    }\n  };\n\n  const handleCancel = () => {\n    setEditTitle(todo.title);\n    setEditDescription(todo.description || '');\n    setIsEditing(false);\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSave();\n    } else if (e.key === 'Escape') {\n      handleCancel();\n    }\n  };\n\n  return (\n    <motion.div\n      layout\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      exit={{ opacity: 0, y: -20 }}\n      className={cn(\n        'group relative bg-card border border-border rounded-lg p-4 shadow-sm hover:shadow-md transition-all duration-200',\n        isDragging && 'shadow-lg scale-105 rotate-2',\n        todo.completed && 'opacity-75'\n      )}\n    >\n      {/* Drag Handle */}\n      <div className=\"absolute left-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity cursor-grab active:cursor-grabbing\">\n        <GripVertical className=\"h-4 w-4 text-muted-foreground\" />\n      </div>\n\n      <div className=\"flex items-start gap-3 pl-6\">\n        {/* Checkbox */}\n        <motion.button\n          onClick={() => toggleTodo(todo.id)}\n          className={cn(\n            'flex-shrink-0 w-5 h-5 rounded border-2 flex items-center justify-center transition-colors mt-0.5',\n            todo.completed\n              ? 'bg-primary border-primary text-primary-foreground'\n              : 'border-border hover:border-primary'\n          )}\n          whileHover={{ scale: 1.1 }}\n          whileTap={{ scale: 0.9 }}\n        >\n          <AnimatePresence>\n            {todo.completed && (\n              <motion.div\n                initial={{ scale: 0 }}\n                animate={{ scale: 1 }}\n                exit={{ scale: 0 }}\n              >\n                <Check className=\"h-3 w-3\" />\n              </motion.div>\n            )}\n          </AnimatePresence>\n        </motion.button>\n\n        {/* Content */}\n        <div className=\"flex-1 min-w-0\">\n          {isEditing ? (\n            <div className=\"space-y-2\">\n              <input\n                type=\"text\"\n                value={editTitle}\n                onChange={(e) => setEditTitle(e.target.value)}\n                onKeyDown={handleKeyPress}\n                className=\"w-full px-2 py-1 text-sm bg-background border border-border rounded focus:outline-none focus:ring-2 focus:ring-primary\"\n                autoFocus\n              />\n              <textarea\n                value={editDescription}\n                onChange={(e) => setEditDescription(e.target.value)}\n                onKeyDown={handleKeyPress}\n                placeholder=\"Description (optional)\"\n                className=\"w-full px-2 py-1 text-xs bg-background border border-border rounded focus:outline-none focus:ring-2 focus:ring-primary resize-none\"\n                rows={2}\n              />\n              <div className=\"flex gap-2\">\n                <button\n                  onClick={handleSave}\n                  className=\"px-2 py-1 text-xs bg-primary text-primary-foreground rounded hover:bg-primary/90\"\n                >\n                  Save\n                </button>\n                <button\n                  onClick={handleCancel}\n                  className=\"px-2 py-1 text-xs bg-secondary text-secondary-foreground rounded hover:bg-secondary/80\"\n                >\n                  Cancel\n                </button>\n              </div>\n            </div>\n          ) : (\n            <>\n              <h3 className={cn(\n                'font-medium text-sm',\n                todo.completed && 'line-through text-muted-foreground'\n              )}>\n                {todo.title}\n              </h3>\n              {todo.description && (\n                <p className={cn(\n                  'text-xs text-muted-foreground mt-1',\n                  todo.completed && 'line-through'\n                )}>\n                  {todo.description}\n                </p>\n              )}\n            </>\n          )}\n\n          {/* Metadata */}\n          {!isEditing && (\n            <div className=\"flex flex-wrap items-center gap-2 mt-2\">\n              {/* Priority */}\n              <span className={cn(\n                'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border',\n                priorityClass\n              )}>\n                {todo.priority}\n              </span>\n\n              {/* Category */}\n              {category && (\n                <span \n                  className=\"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium text-white\"\n                  style={{ backgroundColor: category.color }}\n                >\n                  {category.name}\n                </span>\n              )}\n\n              {/* Due Date */}\n              {todo.dueDate && (\n                <span className={cn(\n                  'inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs',\n                  isTaskOverdue \n                    ? 'text-red-600 bg-red-50 border border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800'\n                    : 'text-muted-foreground bg-muted'\n                )}>\n                  {isTaskOverdue ? <AlertCircle className=\"h-3 w-3\" /> : <Calendar className=\"h-3 w-3\" />}\n                  {formatDate(todo.dueDate)}\n                </span>\n              )}\n\n              {/* Tags */}\n              {todo.tags.map((tag) => (\n                <span\n                  key={tag}\n                  className=\"inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs bg-accent text-accent-foreground\"\n                >\n                  <Tag className=\"h-3 w-3\" />\n                  {tag}\n                </span>\n              ))}\n\n              {/* Created time */}\n              <span className=\"inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs text-muted-foreground bg-muted\">\n                <Clock className=\"h-3 w-3\" />\n                {formatDate(todo.createdAt)}\n              </span>\n            </div>\n          )}\n        </div>\n\n        {/* Actions */}\n        {!isEditing && (\n          <div className=\"flex-shrink-0 flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity\">\n            <motion.button\n              onClick={() => setIsEditing(true)}\n              className=\"p-1.5 rounded hover:bg-accent text-muted-foreground hover:text-accent-foreground\"\n              whileHover={{ scale: 1.1 }}\n              whileTap={{ scale: 0.9 }}\n            >\n              <Edit2 className=\"h-4 w-4\" />\n            </motion.button>\n            <motion.button\n              onClick={() => deleteTodo(todo.id)}\n              className=\"p-1.5 rounded hover:bg-destructive/10 text-muted-foreground hover:text-destructive\"\n              whileHover={{ scale: 1.1 }}\n              whileTap={{ scale: 0.9 }}\n            >\n              <Trash2 className=\"h-4 w-4\" />\n            </motion.button>\n          </div>\n        )}\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AAjBA;;;;;;;;AAwBO,SAAS,SAAS,EAAE,IAAI,EAAE,UAAU,EAAiB;IAC1D,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IACtE,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,gBAAa,AAAD;IAC9B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,KAAK;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,WAAW,IAAI;IAE3E,MAAM,WAAW,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,QAAQ;IAC5D,MAAM,gBAAgB,UAAU,SAAS,CAAA,GAAA,qHAAA,CAAA,uBAAoB,AAAD,EAAE,KAAK,QAAQ,IAAI,CAAA,GAAA,qHAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,QAAQ;IAC7G,MAAM,gBAAgB,KAAK,OAAO,IAAI,CAAC,KAAK,SAAS,IAAI,CAAA,GAAA,qHAAA,CAAA,YAAS,AAAD,EAAE,KAAK,OAAO;IAE/E,MAAM,aAAa;QACjB,IAAI,UAAU,IAAI,IAAI;YACpB,WAAW,KAAK,EAAE,EAAE;gBAClB,OAAO,UAAU,IAAI;gBACrB,aAAa,gBAAgB,IAAI,MAAM;YACzC;YACA,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,aAAa,KAAK,KAAK;QACvB,mBAAmB,KAAK,WAAW,IAAI;QACvC,aAAa;IACf;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF,OAAO,IAAI,EAAE,GAAG,KAAK,UAAU;YAC7B;QACF;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,MAAM;QACN,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,MAAM;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC3B,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,oHACA,cAAc,gCACd,KAAK,SAAS,IAAI;;0BAIpB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,sNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;;;;;;0BAG1B,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,SAAS,IAAM,WAAW,KAAK,EAAE;wBACjC,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,oGACA,KAAK,SAAS,GACV,sDACA;wBAEN,YAAY;4BAAE,OAAO;wBAAI;wBACzB,UAAU;4BAAE,OAAO;wBAAI;kCAEvB,cAAA,8OAAC,yLAAA,CAAA,kBAAe;sCACb,KAAK,SAAS,kBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,OAAO;gCAAE;gCACpB,SAAS;oCAAE,OAAO;gCAAE;gCACpB,MAAM;oCAAE,OAAO;gCAAE;0CAEjB,cAAA,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;kCAOzB,8OAAC;wBAAI,WAAU;;4BACZ,0BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wCAC5C,WAAW;wCACX,WAAU;wCACV,SAAS;;;;;;kDAEX,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wCAClD,WAAW;wCACX,aAAY;wCACZ,WAAU;wCACV,MAAM;;;;;;kDAER,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;;;;;;qDAML;;kDACE,8OAAC;wCAAG,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACd,uBACA,KAAK,SAAS,IAAI;kDAEjB,KAAK,KAAK;;;;;;oCAEZ,KAAK,WAAW,kBACf,8OAAC;wCAAE,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACb,sCACA,KAAK,SAAS,IAAI;kDAEjB,KAAK,WAAW;;;;;;;;4BAOxB,CAAC,2BACA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAK,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAChB,gFACA;kDAEC,KAAK,QAAQ;;;;;;oCAIf,0BACC,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB,SAAS,KAAK;wCAAC;kDAExC,SAAS,IAAI;;;;;;oCAKjB,KAAK,OAAO,kBACX,8OAAC;wCAAK,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAChB,mEACA,gBACI,0GACA;;4CAEH,8BAAgB,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;qEAAe,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAC1E,CAAA,GAAA,qHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,OAAO;;;;;;;oCAK3B,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,oBACd,8OAAC;4CAEC,WAAU;;8DAEV,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDACd;;2CAJI;;;;;kDAST,8OAAC;wCAAK,WAAU;;0DACd,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAChB,CAAA,GAAA,qHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,SAAS;;;;;;;;;;;;;;;;;;;oBAOjC,CAAC,2BACA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS,IAAM,aAAa;gCAC5B,WAAU;gCACV,YAAY;oCAAE,OAAO;gCAAI;gCACzB,UAAU;oCAAE,OAAO;gCAAI;0CAEvB,cAAA,8OAAC,kMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS,IAAM,WAAW,KAAK,EAAE;gCACjC,WAAU;gCACV,YAAY;oCAAE,OAAO;gCAAI;gCACzB,UAAU;oCAAE,OAAO;gCAAI;0CAEvB,cAAA,8OAAC,0MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhC", "debugId": null}}, {"offset": {"line": 1610, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/augment%E9%A1%B9%E7%9B%AE/todo/src/components/EmptyState.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { CheckCircle, Plus } from 'lucide-react';\nimport { useTodoStore } from '@/store/todoStore';\n\nexport function EmptyState() {\n  const { todos, filter } = useTodoStore();\n  const hasAnyTodos = todos.length > 0;\n  const hasActiveFilters = filter.search || filter.category || filter.priority || filter.completed !== null || filter.tags.length > 0;\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className=\"flex flex-col items-center justify-center py-12 text-center\"\n    >\n      <motion.div\n        initial={{ scale: 0 }}\n        animate={{ scale: 1 }}\n        transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}\n        className=\"w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4\"\n      >\n        <CheckCircle className=\"h-8 w-8 text-muted-foreground\" />\n      </motion.div>\n\n      <motion.h3\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ delay: 0.3 }}\n        className=\"text-lg font-semibold mb-2\"\n      >\n        {hasAnyTodos && hasActiveFilters\n          ? 'No tasks match your filters'\n          : hasAnyTodos\n          ? 'All tasks completed!'\n          : 'No tasks yet'}\n      </motion.h3>\n\n      <motion.p\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ delay: 0.4 }}\n        className=\"text-muted-foreground mb-6 max-w-md\"\n      >\n        {hasAnyTodos && hasActiveFilters\n          ? 'Try adjusting your search or filter criteria to find what you\\'re looking for.'\n          : hasAnyTodos\n          ? 'Great job! You\\'ve completed all your tasks. Time to add some new ones or take a well-deserved break.'\n          : 'Get started by adding your first task. Click the plus button above to create a new task.'}\n      </motion.p>\n\n      {!hasAnyTodos && (\n        <motion.div\n          initial={{ opacity: 0, y: 10 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.5 }}\n          className=\"flex items-center gap-2 text-sm text-muted-foreground bg-muted px-4 py-2 rounded-lg\"\n        >\n          <Plus className=\"h-4 w-4\" />\n          Click the plus button to add your first task\n        </motion.div>\n      )}\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAMO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IACrC,MAAM,cAAc,MAAM,MAAM,GAAG;IACnC,MAAM,mBAAmB,OAAO,MAAM,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,IAAI,OAAO,SAAS,KAAK,QAAQ,OAAO,IAAI,CAAC,MAAM,GAAG;IAElI,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,WAAU;;0BAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,OAAO;gBAAE;gBACpB,SAAS;oBAAE,OAAO;gBAAE;gBACpB,YAAY;oBAAE,OAAO;oBAAK,MAAM;oBAAU,WAAW;gBAAI;gBACzD,WAAU;0BAEV,cAAA,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;0BAGzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gBACR,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;0BAET,eAAe,mBACZ,gCACA,cACA,yBACA;;;;;;0BAGN,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gBACP,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;0BAET,eAAe,mBACZ,mFACA,cACA,0GACA;;;;;;YAGL,CAAC,6BACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;;kCAEV,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBAAY;;;;;;;;;;;;;AAMtC", "debugId": null}}, {"offset": {"line": 1738, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/augment%E9%A1%B9%E7%9B%AE/todo/src/components/TodoList.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useTodoStore } from '@/store/todoStore';\nimport { TodoItem } from './TodoItem';\nimport { EmptyState } from './EmptyState';\n\nexport function TodoList() {\n  const { getFilteredTodos, reorderTodos } = useTodoStore();\n  const todos = getFilteredTodos();\n  const [draggedId, setDraggedId] = useState<string | null>(null);\n\n  const handleDragStart = (start: any) => {\n    setDraggedId(start.draggableId);\n  };\n\n  const handleDragEnd = (result: DropResult) => {\n    setDraggedId(null);\n    \n    if (!result.destination) {\n      return;\n    }\n\n    const items = Array.from(todos);\n    const [reorderedItem] = items.splice(result.source.index, 1);\n    items.splice(result.destination.index, 0, reorderedItem);\n\n    reorderTodos(items);\n  };\n\n  if (todos.length === 0) {\n    return <EmptyState />;\n  }\n\n  return (\n    <DragDropContext onDragStart={handleDragStart} onDragEnd={handleDragEnd}>\n      <Droppable droppableId=\"todos\">\n        {(provided, snapshot) => (\n          <div\n            {...provided.droppableProps}\n            ref={provided.innerRef}\n            className={`space-y-3 transition-colors ${\n              snapshot.isDraggingOver ? 'bg-accent/20 rounded-lg p-2' : ''\n            }`}\n          >\n            <AnimatePresence mode=\"popLayout\">\n              {todos.map((todo, index) => (\n                <Draggable key={todo.id} draggableId={todo.id} index={index}>\n                  {(provided, snapshot) => (\n                    <motion.div\n                      ref={provided.innerRef}\n                      {...provided.draggableProps}\n                      {...provided.dragHandleProps}\n                      layout\n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      exit={{ opacity: 0, y: -20 }}\n                      transition={{ duration: 0.2 }}\n                      style={{\n                        ...provided.draggableProps.style,\n                        transform: snapshot.isDragging\n                          ? provided.draggableProps.style?.transform\n                          : 'none',\n                      }}\n                    >\n                      <TodoItem\n                        todo={todo}\n                        isDragging={snapshot.isDragging || draggedId === todo.id}\n                      />\n                    </motion.div>\n                  )}\n                </Draggable>\n              ))}\n            </AnimatePresence>\n            {provided.placeholder}\n          </div>\n        )}\n      </Droppable>\n    </DragDropContext>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;;AASO,SAAS;IACd,MAAM,EAAE,gBAAgB,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IACtD,MAAM,QAAQ;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1D,MAAM,kBAAkB,CAAC;QACvB,aAAa,MAAM,WAAW;IAChC;IAEA,MAAM,gBAAgB,CAAC;QACrB,aAAa;QAEb,IAAI,CAAC,OAAO,WAAW,EAAE;YACvB;QACF;QAEA,MAAM,QAAQ,MAAM,IAAI,CAAC;QACzB,MAAM,CAAC,cAAc,GAAG,MAAM,MAAM,CAAC,OAAO,MAAM,CAAC,KAAK,EAAE;QAC1D,MAAM,MAAM,CAAC,OAAO,WAAW,CAAC,KAAK,EAAE,GAAG;QAE1C,aAAa;IACf;IAEA,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,qBAAO,8OAAC,gIAAA,CAAA,aAAU;;;;;IACpB;IAEA,qBACE,8OAAC,8JAAA,CAAA,kBAAe;QAAC,aAAa;QAAiB,WAAW;kBACxD,cAAA,8OAAC,8JAAA,CAAA,YAAS;YAAC,aAAY;sBACpB,CAAC,UAAU,yBACV,8OAAC;oBACE,GAAG,SAAS,cAAc;oBAC3B,KAAK,SAAS,QAAQ;oBACtB,WAAW,CAAC,4BAA4B,EACtC,SAAS,cAAc,GAAG,gCAAgC,IAC1D;;sCAEF,8OAAC,yLAAA,CAAA,kBAAe;4BAAC,MAAK;sCACnB,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,8JAAA,CAAA,YAAS;oCAAe,aAAa,KAAK,EAAE;oCAAE,OAAO;8CACnD,CAAC,UAAU,yBACV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,KAAK,SAAS,QAAQ;4CACrB,GAAG,SAAS,cAAc;4CAC1B,GAAG,SAAS,eAAe;4CAC5B,MAAM;4CACN,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,MAAM;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC3B,YAAY;gDAAE,UAAU;4CAAI;4CAC5B,OAAO;gDACL,GAAG,SAAS,cAAc,CAAC,KAAK;gDAChC,WAAW,SAAS,UAAU,GAC1B,SAAS,cAAc,CAAC,KAAK,EAAE,YAC/B;4CACN;sDAEA,cAAA,8OAAC,8HAAA,CAAA,WAAQ;gDACP,MAAM;gDACN,YAAY,SAAS,UAAU,IAAI,cAAc,KAAK,EAAE;;;;;;;;;;;mCApBhD,KAAK,EAAE;;;;;;;;;;wBA2B1B,SAAS,WAAW;;;;;;;;;;;;;;;;;AAMjC", "debugId": null}}, {"offset": {"line": 1867, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/augment%E9%A1%B9%E7%9B%AE/todo/src/components/TodoStats.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Check<PERSON>ir<PERSON>, Clock, AlertTriangle, ListTodo } from 'lucide-react';\nimport { useTodoStore } from '@/store/todoStore';\n\nexport function TodoStats() {\n  const { getTodoStats } = useTodoStore();\n  const stats = getTodoStats();\n\n  const statItems = [\n    {\n      label: 'Total',\n      value: stats.total,\n      icon: ListTodo,\n      color: 'text-blue-600 bg-blue-50 dark:text-blue-400 dark:bg-blue-900/20',\n    },\n    {\n      label: 'Completed',\n      value: stats.completed,\n      icon: CheckCircle,\n      color: 'text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-900/20',\n    },\n    {\n      label: 'Pending',\n      value: stats.pending,\n      icon: Clock,\n      color: 'text-yellow-600 bg-yellow-50 dark:text-yellow-400 dark:bg-yellow-900/20',\n    },\n    {\n      label: 'Overdue',\n      value: stats.overdue,\n      icon: AlertTriangle,\n      color: 'text-red-600 bg-red-50 dark:text-red-400 dark:bg-red-900/20',\n    },\n  ];\n\n  const completionRate = stats.total > 0 ? (stats.completed / stats.total) * 100 : 0;\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className=\"bg-card border border-border rounded-lg shadow-sm p-6\"\n    >\n      <div className=\"flex items-center justify-between mb-4\">\n        <h2 className=\"text-lg font-semibold\">Overview</h2>\n        <div className=\"text-sm text-muted-foreground\">\n          {completionRate.toFixed(0)}% complete\n        </div>\n      </div>\n\n      {/* Progress Bar */}\n      <div className=\"mb-6\">\n        <div className=\"w-full bg-secondary rounded-full h-2\">\n          <motion.div\n            className=\"bg-primary h-2 rounded-full\"\n            initial={{ width: 0 }}\n            animate={{ width: `${completionRate}%` }}\n            transition={{ duration: 0.5, ease: 'easeOut' }}\n          />\n        </div>\n      </div>\n\n      {/* Stats Grid */}\n      <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4\">\n        {statItems.map((item, index) => (\n          <motion.div\n            key={item.label}\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ delay: index * 0.1 }}\n            className=\"text-center\"\n          >\n            <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg ${item.color} mb-2`}>\n              <item.icon className=\"h-6 w-6\" />\n            </div>\n            <div className=\"text-2xl font-bold\">{item.value}</div>\n            <div className=\"text-sm text-muted-foreground\">{item.label}</div>\n          </motion.div>\n        ))}\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMO,SAAS;IACd,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IACpC,MAAM,QAAQ;IAEd,MAAM,YAAY;QAChB;YACE,OAAO;YACP,OAAO,MAAM,KAAK;YAClB,MAAM,8MAAA,CAAA,WAAQ;YACd,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,MAAM,SAAS;YACtB,MAAM,2NAAA,CAAA,cAAW;YACjB,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,MAAM,OAAO;YACpB,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,MAAM,OAAO;YACpB,MAAM,wNAAA,CAAA,gBAAa;YACnB,OAAO;QACT;KACD;IAED,MAAM,iBAAiB,MAAM,KAAK,GAAG,IAAI,AAAC,MAAM,SAAS,GAAG,MAAM,KAAK,GAAI,MAAM;IAEjF,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,WAAU;;0BAEV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwB;;;;;;kCACtC,8OAAC;wBAAI,WAAU;;4BACZ,eAAe,OAAO,CAAC;4BAAG;;;;;;;;;;;;;0BAK/B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,OAAO;wBAAE;wBACpB,SAAS;4BAAE,OAAO,GAAG,eAAe,CAAC,CAAC;wBAAC;wBACvC,YAAY;4BAAE,UAAU;4BAAK,MAAM;wBAAU;;;;;;;;;;;;;;;;0BAMnD,8OAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAI;wBAClC,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAE;wBAChC,YAAY;4BAAE,OAAO,QAAQ;wBAAI;wBACjC,WAAU;;0CAEV,8OAAC;gCAAI,WAAW,CAAC,6DAA6D,EAAE,KAAK,KAAK,CAAC,KAAK,CAAC;0CAC/F,cAAA,8OAAC,KAAK,IAAI;oCAAC,WAAU;;;;;;;;;;;0CAEvB,8OAAC;gCAAI,WAAU;0CAAsB,KAAK,KAAK;;;;;;0CAC/C,8OAAC;gCAAI,WAAU;0CAAiC,KAAK,KAAK;;;;;;;uBAVrD,KAAK,KAAK;;;;;;;;;;;;;;;;AAgB3B", "debugId": null}}, {"offset": {"line": 2052, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/augment%E9%A1%B9%E7%9B%AE/todo/src/components/ThemeToggle.tsx"], "sourcesContent": ["'use client';\n\nimport { Moon, Sun } from 'lucide-react';\nimport { useThemeStore } from '@/store/themeStore';\nimport { motion } from 'framer-motion';\n\nexport function ThemeToggle() {\n  const { theme, toggleTheme } = useThemeStore();\n\n  return (\n    <motion.button\n      onClick={toggleTheme}\n      className=\"relative p-2 rounded-lg bg-secondary hover:bg-accent transition-colors\"\n      whileHover={{ scale: 1.05 }}\n      whileTap={{ scale: 0.95 }}\n      aria-label=\"Toggle theme\"\n    >\n      <motion.div\n        initial={false}\n        animate={{\n          rotate: theme === 'dark' ? 180 : 0,\n          scale: theme === 'dark' ? 0 : 1,\n        }}\n        transition={{ duration: 0.2 }}\n        className=\"absolute inset-0 flex items-center justify-center\"\n      >\n        <Sun className=\"h-5 w-5\" />\n      </motion.div>\n      <motion.div\n        initial={false}\n        animate={{\n          rotate: theme === 'light' ? 180 : 0,\n          scale: theme === 'light' ? 0 : 1,\n        }}\n        transition={{ duration: 0.2 }}\n        className=\"flex items-center justify-center\"\n      >\n        <Moon className=\"h-5 w-5\" />\n      </motion.div>\n    </motion.button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AAJA;;;;;AAMO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,gBAAa,AAAD;IAE3C,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,SAAS;QACT,WAAU;QACV,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;QACxB,cAAW;;0BAEX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;gBACT,SAAS;oBACP,QAAQ,UAAU,SAAS,MAAM;oBACjC,OAAO,UAAU,SAAS,IAAI;gBAChC;gBACA,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;0BAEV,cAAA,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;;;;;;0BAEjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;gBACT,SAAS;oBACP,QAAQ,UAAU,UAAU,MAAM;oBAClC,OAAO,UAAU,UAAU,IAAI;gBACjC;gBACA,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;0BAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAIxB", "debugId": null}}, {"offset": {"line": 2135, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/augment%E9%A1%B9%E7%9B%AE/todo/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { AddTodoForm } from '@/components/AddTodoForm';\nimport { TodoFilters } from '@/components/TodoFilters';\nimport { TodoList } from '@/components/TodoList';\nimport { TodoStats } from '@/components/TodoStats';\nimport { ThemeToggle } from '@/components/ThemeToggle';\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header */}\n      <header className=\"border-b border-border bg-card/50 backdrop-blur-sm sticky top-0 z-50\">\n        <div className=\"max-w-4xl mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <motion.div\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              className=\"flex items-center gap-3\"\n            >\n              <div className=\"w-8 h-8 bg-gradient-to-br from-primary to-primary/70 rounded-lg flex items-center justify-center\">\n                <span className=\"text-primary-foreground font-bold text-lg\">T</span>\n              </div>\n              <div>\n                <h1 className=\"text-xl font-bold\">Todo App</h1>\n                <p className=\"text-sm text-muted-foreground\">Stay organized, stay productive</p>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n            >\n              <ThemeToggle />\n            </motion.div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-4xl mx-auto px-4 py-8\">\n        <div className=\"space-y-6\">\n          {/* Stats */}\n          <TodoStats />\n\n          {/* Add Todo Form */}\n          <AddTodoForm />\n\n          {/* Filters */}\n          <TodoFilters />\n\n          {/* Todo List */}\n          <TodoList />\n        </div>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"border-t border-border bg-card/50 mt-16\">\n        <div className=\"max-w-4xl mx-auto px-4 py-6\">\n          <div className=\"text-center text-sm text-muted-foreground\">\n            <p>Built with Next.js, TypeScript, Tailwind CSS, and Framer Motion</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;kDAE9D,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAoB;;;;;;0DAClC,8OAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;0CAIjD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;0CAE5B,cAAA,8OAAC,iIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOpB,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,+HAAA,CAAA,YAAS;;;;;sCAGV,8OAAC,iIAAA,CAAA,cAAW;;;;;sCAGZ,8OAAC,iIAAA,CAAA,cAAW;;;;;sCAGZ,8OAAC,8HAAA,CAAA,WAAQ;;;;;;;;;;;;;;;;0BAKb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}]}