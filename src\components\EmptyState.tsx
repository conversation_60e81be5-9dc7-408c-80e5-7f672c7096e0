'use client';

import { motion } from 'framer-motion';
import { CheckCircle, Plus } from 'lucide-react';
import { useTodoStore } from '@/store/todoStore';

export function EmptyState() {
  const { todos, filter } = useTodoStore();
  const hasAnyTodos = todos.length > 0;
  const hasActiveFilters = filter.search || filter.category || filter.priority || filter.completed !== null || filter.tags.length > 0;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="flex flex-col items-center justify-center py-12 text-center"
    >
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
        className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4"
      >
        <CheckCircle className="h-8 w-8 text-muted-foreground" />
      </motion.div>

      <motion.h3
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
        className="text-lg font-semibold mb-2"
      >
        {hasAnyTodos && hasActiveFilters
          ? 'No tasks match your filters'
          : hasAnyTodos
          ? 'All tasks completed!'
          : 'No tasks yet'}
      </motion.h3>

      <motion.p
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.4 }}
        className="text-muted-foreground mb-6 max-w-md"
      >
        {hasAnyTodos && hasActiveFilters
          ? 'Try adjusting your search or filter criteria to find what you\'re looking for.'
          : hasAnyTodos
          ? 'Great job! You\'ve completed all your tasks. Time to add some new ones or take a well-deserved break.'
          : 'Get started by adding your first task. Click the plus button above to create a new task.'}
      </motion.p>

      {!hasAnyTodos && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="flex items-center gap-2 text-sm text-muted-foreground bg-muted px-4 py-2 rounded-lg"
        >
          <Plus className="h-4 w-4" />
          Click the plus button to add your first task
        </motion.div>
      )}
    </motion.div>
  );
}
