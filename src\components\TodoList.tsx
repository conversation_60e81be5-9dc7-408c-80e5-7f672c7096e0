'use client';

import { useState } from 'react';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { motion, AnimatePresence } from 'framer-motion';
import { useTodoStore } from '@/store/todoStore';
import { TodoItem } from './TodoItem';
import { EmptyState } from './EmptyState';

export function TodoList() {
  const { getFilteredTodos, reorderTodos } = useTodoStore();
  const todos = getFilteredTodos();
  const [draggedId, setDraggedId] = useState<string | null>(null);

  const handleDragStart = (start: any) => {
    setDraggedId(start.draggableId);
  };

  const handleDragEnd = (result: DropResult) => {
    setDraggedId(null);
    
    if (!result.destination) {
      return;
    }

    const items = Array.from(todos);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    reorderTodos(items);
  };

  if (todos.length === 0) {
    return <EmptyState />;
  }

  return (
    <DragDropContext onDragStart={handleDragStart} onDragEnd={handleDragEnd}>
      <Droppable droppableId="todos">
        {(provided, snapshot) => (
          <div
            {...provided.droppableProps}
            ref={provided.innerRef}
            className={`space-y-3 transition-colors ${
              snapshot.isDraggingOver ? 'bg-accent/20 rounded-lg p-2' : ''
            }`}
          >
            <AnimatePresence mode="popLayout">
              {todos.map((todo, index) => (
                <Draggable key={todo.id} draggableId={todo.id} index={index}>
                  {(provided, snapshot) => (
                    <motion.div
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      {...provided.dragHandleProps}
                      layout
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.2 }}
                      style={{
                        ...provided.draggableProps.style,
                        transform: snapshot.isDragging
                          ? provided.draggableProps.style?.transform
                          : 'none',
                      }}
                    >
                      <TodoItem
                        todo={todo}
                        isDragging={snapshot.isDragging || draggedId === todo.id}
                      />
                    </motion.div>
                  )}
                </Draggable>
              ))}
            </AnimatePresence>
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </DragDropContext>
  );
}
