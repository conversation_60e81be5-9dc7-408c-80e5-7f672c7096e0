import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Todo, Category, TodoFilter, SortBy, SortOrder } from '@/types';

interface TodoStore {
  todos: Todo[];
  categories: Category[];
  filter: TodoFilter;
  sortBy: SortBy;
  sortOrder: SortOrder;
  
  // Todo actions
  addTodo: (todo: Omit<Todo, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateTodo: (id: string, updates: Partial<Todo>) => void;
  deleteTodo: (id: string) => void;
  toggleTodo: (id: string) => void;
  reorderTodos: (todos: Todo[]) => void;
  
  // Category actions
  addCategory: (category: Omit<Category, 'id'>) => void;
  updateCategory: (id: string, updates: Partial<Category>) => void;
  deleteCategory: (id: string) => void;
  
  // Filter actions
  setFilter: (filter: Partial<TodoFilter>) => void;
  clearFilter: () => void;
  
  // Sort actions
  setSortBy: (sortBy: SortBy) => void;
  setSortOrder: (sortOrder: SortOrder) => void;
  
  // Utility functions
  getFilteredTodos: () => Todo[];
  getTodoStats: () => { total: number; completed: number; pending: number; overdue: number };
}

const defaultCategories: Category[] = [
  { id: '1', name: 'Personal', color: '#3B82F6', icon: 'User' },
  { id: '2', name: 'Work', color: '#EF4444', icon: 'Briefcase' },
  { id: '3', name: 'Shopping', color: '#10B981', icon: 'ShoppingCart' },
  { id: '4', name: 'Health', color: '#F59E0B', icon: 'Heart' },
];

const defaultFilter: TodoFilter = {
  search: '',
  category: '',
  priority: '',
  completed: null,
  tags: [],
};

export const useTodoStore = create<TodoStore>()(
  persist(
    (set, get) => ({
      todos: [],
      categories: defaultCategories,
      filter: defaultFilter,
      sortBy: 'createdAt',
      sortOrder: 'desc',
      
      addTodo: (todoData) => {
        const newTodo: Todo = {
          ...todoData,
          id: crypto.randomUUID(),
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        set((state) => ({ todos: [newTodo, ...state.todos] }));
      },
      
      updateTodo: (id, updates) => {
        set((state) => ({
          todos: state.todos.map((todo) =>
            todo.id === id
              ? { ...todo, ...updates, updatedAt: new Date() }
              : todo
          ),
        }));
      },
      
      deleteTodo: (id) => {
        set((state) => ({
          todos: state.todos.filter((todo) => todo.id !== id),
        }));
      },
      
      toggleTodo: (id) => {
        set((state) => ({
          todos: state.todos.map((todo) =>
            todo.id === id
              ? { ...todo, completed: !todo.completed, updatedAt: new Date() }
              : todo
          ),
        }));
      },
      
      reorderTodos: (todos) => {
        set({ todos });
      },
      
      addCategory: (categoryData) => {
        const newCategory: Category = {
          ...categoryData,
          id: crypto.randomUUID(),
        };
        set((state) => ({ categories: [...state.categories, newCategory] }));
      },
      
      updateCategory: (id, updates) => {
        set((state) => ({
          categories: state.categories.map((category) =>
            category.id === id ? { ...category, ...updates } : category
          ),
        }));
      },
      
      deleteCategory: (id) => {
        set((state) => ({
          categories: state.categories.filter((category) => category.id !== id),
          todos: state.todos.map((todo) =>
            todo.category === id ? { ...todo, category: '' } : todo
          ),
        }));
      },
      
      setFilter: (newFilter) => {
        set((state) => ({ filter: { ...state.filter, ...newFilter } }));
      },
      
      clearFilter: () => {
        set({ filter: defaultFilter });
      },
      
      setSortBy: (sortBy) => {
        set({ sortBy });
      },
      
      setSortOrder: (sortOrder) => {
        set({ sortOrder });
      },
      
      getFilteredTodos: () => {
        const { todos, filter, sortBy, sortOrder } = get();
        let filtered = todos;
        
        // Apply filters
        if (filter.search) {
          const searchLower = filter.search.toLowerCase();
          filtered = filtered.filter(
            (todo) =>
              todo.title.toLowerCase().includes(searchLower) ||
              todo.description?.toLowerCase().includes(searchLower)
          );
        }
        
        if (filter.category) {
          filtered = filtered.filter((todo) => todo.category === filter.category);
        }
        
        if (filter.priority) {
          filtered = filtered.filter((todo) => todo.priority === filter.priority);
        }
        
        if (filter.completed !== null) {
          filtered = filtered.filter((todo) => todo.completed === filter.completed);
        }
        
        if (filter.tags.length > 0) {
          filtered = filtered.filter((todo) =>
            filter.tags.some((tag) => todo.tags.includes(tag))
          );
        }
        
        // Apply sorting
        filtered.sort((a, b) => {
          let aValue: any = a[sortBy];
          let bValue: any = b[sortBy];
          
          if (sortBy === 'priority') {
            const priorityOrder = { high: 3, medium: 2, low: 1 };
            aValue = priorityOrder[a.priority];
            bValue = priorityOrder[b.priority];
          }
          
          if (aValue instanceof Date) {
            aValue = aValue.getTime();
          }
          if (bValue instanceof Date) {
            bValue = bValue.getTime();
          }
          
          if (typeof aValue === 'string') {
            aValue = aValue.toLowerCase();
          }
          if (typeof bValue === 'string') {
            bValue = bValue.toLowerCase();
          }
          
          if (sortOrder === 'asc') {
            return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
          } else {
            return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
          }
        });
        
        return filtered;
      },
      
      getTodoStats: () => {
        const { todos } = get();
        const now = new Date();
        
        return {
          total: todos.length,
          completed: todos.filter((todo) => todo.completed).length,
          pending: todos.filter((todo) => !todo.completed).length,
          overdue: todos.filter(
            (todo) => !todo.completed && todo.dueDate && todo.dueDate < now
          ).length,
        };
      },
    }),
    {
      name: 'todo-storage',
      partialize: (state) => ({
        todos: state.todos,
        categories: state.categories,
      }),
    }
  )
);
