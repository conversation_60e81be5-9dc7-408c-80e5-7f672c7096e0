'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Check, 
  Edit2, 
  Trash2, 
  Calendar, 
  Tag, 
  AlertCircle,
  Clock,
  GripVertical
} from 'lucide-react';
import { Todo, Category } from '@/types';
import { useTodoStore } from '@/store/todoStore';
import { formatDate, isOverdue, getPriorityColor, getPriorityColorDark, cn } from '@/utils';
import { useThemeStore } from '@/store/themeStore';

interface TodoItemProps {
  todo: Todo;
  isDragging?: boolean;
}

export function TodoItem({ todo, isDragging }: TodoItemProps) {
  const { updateTodo, deleteTodo, toggleTodo, categories } = useTodoStore();
  const { theme } = useThemeStore();
  const [isEditing, setIsEditing] = useState(false);
  const [editTitle, setEditTitle] = useState(todo.title);
  const [editDescription, setEditDescription] = useState(todo.description || '');

  const category = categories.find(c => c.id === todo.category);
  const priorityClass = theme === 'dark' ? getPriorityColorDark(todo.priority) : getPriorityColor(todo.priority);
  const isTaskOverdue = todo.dueDate && !todo.completed && isOverdue(todo.dueDate);

  const handleSave = () => {
    if (editTitle.trim()) {
      updateTodo(todo.id, {
        title: editTitle.trim(),
        description: editDescription.trim() || undefined,
      });
      setIsEditing(false);
    }
  };

  const handleCancel = () => {
    setEditTitle(todo.title);
    setEditDescription(todo.description || '');
    setIsEditing(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      handleCancel();
    }
  };

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className={cn(
        'group relative bg-card border border-border rounded-lg p-4 shadow-sm hover:shadow-md transition-all duration-200',
        isDragging && 'shadow-lg scale-105 rotate-2',
        todo.completed && 'opacity-75'
      )}
    >
      {/* Drag Handle */}
      <div className="absolute left-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity cursor-grab active:cursor-grabbing">
        <GripVertical className="h-4 w-4 text-muted-foreground" />
      </div>

      <div className="flex items-start gap-3 pl-6">
        {/* Checkbox */}
        <motion.button
          onClick={() => toggleTodo(todo.id)}
          className={cn(
            'flex-shrink-0 w-5 h-5 rounded border-2 flex items-center justify-center transition-colors mt-0.5',
            todo.completed
              ? 'bg-primary border-primary text-primary-foreground'
              : 'border-border hover:border-primary'
          )}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <AnimatePresence>
            {todo.completed && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                exit={{ scale: 0 }}
              >
                <Check className="h-3 w-3" />
              </motion.div>
            )}
          </AnimatePresence>
        </motion.button>

        {/* Content */}
        <div className="flex-1 min-w-0">
          {isEditing ? (
            <div className="space-y-2">
              <input
                type="text"
                value={editTitle}
                onChange={(e) => setEditTitle(e.target.value)}
                onKeyDown={handleKeyPress}
                className="w-full px-2 py-1 text-sm bg-background border border-border rounded focus:outline-none focus:ring-2 focus:ring-primary"
                autoFocus
              />
              <textarea
                value={editDescription}
                onChange={(e) => setEditDescription(e.target.value)}
                onKeyDown={handleKeyPress}
                placeholder="Description (optional)"
                className="w-full px-2 py-1 text-xs bg-background border border-border rounded focus:outline-none focus:ring-2 focus:ring-primary resize-none"
                rows={2}
              />
              <div className="flex gap-2">
                <button
                  onClick={handleSave}
                  className="px-2 py-1 text-xs bg-primary text-primary-foreground rounded hover:bg-primary/90"
                >
                  Save
                </button>
                <button
                  onClick={handleCancel}
                  className="px-2 py-1 text-xs bg-secondary text-secondary-foreground rounded hover:bg-secondary/80"
                >
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <>
              <h3 className={cn(
                'font-medium text-sm',
                todo.completed && 'line-through text-muted-foreground'
              )}>
                {todo.title}
              </h3>
              {todo.description && (
                <p className={cn(
                  'text-xs text-muted-foreground mt-1',
                  todo.completed && 'line-through'
                )}>
                  {todo.description}
                </p>
              )}
            </>
          )}

          {/* Metadata */}
          {!isEditing && (
            <div className="flex flex-wrap items-center gap-2 mt-2">
              {/* Priority */}
              <span className={cn(
                'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border',
                priorityClass
              )}>
                {todo.priority}
              </span>

              {/* Category */}
              {category && (
                <span 
                  className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium text-white"
                  style={{ backgroundColor: category.color }}
                >
                  {category.name}
                </span>
              )}

              {/* Due Date */}
              {todo.dueDate && (
                <span className={cn(
                  'inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs',
                  isTaskOverdue 
                    ? 'text-red-600 bg-red-50 border border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800'
                    : 'text-muted-foreground bg-muted'
                )}>
                  {isTaskOverdue ? <AlertCircle className="h-3 w-3" /> : <Calendar className="h-3 w-3" />}
                  {formatDate(todo.dueDate)}
                </span>
              )}

              {/* Tags */}
              {todo.tags.map((tag) => (
                <span
                  key={tag}
                  className="inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs bg-accent text-accent-foreground"
                >
                  <Tag className="h-3 w-3" />
                  {tag}
                </span>
              ))}

              {/* Created time */}
              <span className="inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs text-muted-foreground bg-muted">
                <Clock className="h-3 w-3" />
                {formatDate(todo.createdAt)}
              </span>
            </div>
          )}
        </div>

        {/* Actions */}
        {!isEditing && (
          <div className="flex-shrink-0 flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <motion.button
              onClick={() => setIsEditing(true)}
              className="p-1.5 rounded hover:bg-accent text-muted-foreground hover:text-accent-foreground"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <Edit2 className="h-4 w-4" />
            </motion.button>
            <motion.button
              onClick={() => deleteTodo(todo.id)}
              className="p-1.5 rounded hover:bg-destructive/10 text-muted-foreground hover:text-destructive"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <Trash2 className="h-4 w-4" />
            </motion.button>
          </div>
        )}
      </div>
    </motion.div>
  );
}
