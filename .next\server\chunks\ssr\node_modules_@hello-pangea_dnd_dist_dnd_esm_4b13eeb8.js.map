{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/augment%E9%A1%B9%E7%9B%AE/todo/node_modules/%40hello-pangea/dnd/dist/dnd.esm.js"], "sourcesContent": ["import React, { useState, useRef, useEffect, useLayoutEffect, useContext } from 'react';\nimport ReactDOM, { flushSync } from 'react-dom';\nimport { createStore as createStore$1, compose, applyMiddleware, bindActionCreators } from 'redux';\nimport { Provider, connect } from 'react-redux';\nimport { getRect, expand, offset, withScroll, calculateBox, getBox, createBox } from 'css-box-model';\nimport rafSchd from 'raf-schd';\nimport _extends from '@babel/runtime/helpers/esm/extends';\n\nconst isProduction$1 = process.env.NODE_ENV === 'production';\nconst spacesAndTabs = /[ \\t]{2,}/g;\nconst lineStartWithSpaces = /^[ \\t]*/gm;\nconst clean$2 = value => value.replace(spacesAndTabs, ' ').replace(lineStartWithSpaces, '').trim();\nconst getDevMessage = message => clean$2(`\n  %c@hello-pangea/dnd\n\n  %c${clean$2(message)}\n\n  %c👷‍ This is a development only message. It will be removed in production builds.\n`);\nconst getFormattedMessage = message => [getDevMessage(message), 'color: #00C584; font-size: 1.2em; font-weight: bold;', 'line-height: 1.5', 'color: #723874;'];\nconst isDisabledFlag = '__@hello-pangea/dnd-disable-dev-warnings';\nfunction log(type, message) {\n  if (isProduction$1) {\n    return;\n  }\n  if (typeof window !== 'undefined' && window[isDisabledFlag]) {\n    return;\n  }\n  console[type](...getFormattedMessage(message));\n}\nconst warning = log.bind(null, 'warn');\nconst error = log.bind(null, 'error');\n\nfunction noop$2() {}\n\nfunction getOptions(shared, fromBinding) {\n  return {\n    ...shared,\n    ...fromBinding\n  };\n}\nfunction bindEvents(el, bindings, sharedOptions) {\n  const unbindings = bindings.map(binding => {\n    const options = getOptions(sharedOptions, binding.options);\n    el.addEventListener(binding.eventName, binding.fn, options);\n    return function unbind() {\n      el.removeEventListener(binding.eventName, binding.fn, options);\n    };\n  });\n  return function unbindAll() {\n    unbindings.forEach(unbind => {\n      unbind();\n    });\n  };\n}\n\nconst isProduction = process.env.NODE_ENV === 'production';\nconst prefix$1 = 'Invariant failed';\nclass RbdInvariant extends Error {}\nRbdInvariant.prototype.toString = function toString() {\n  return this.message;\n};\nfunction invariant(condition, message) {\n  if (isProduction) {\n    throw new RbdInvariant(prefix$1);\n  } else {\n    throw new RbdInvariant(`${prefix$1}: ${message || ''}`);\n  }\n}\n\nclass ErrorBoundary extends React.Component {\n  constructor(...args) {\n    super(...args);\n    this.callbacks = null;\n    this.unbind = noop$2;\n    this.onWindowError = event => {\n      const callbacks = this.getCallbacks();\n      if (callbacks.isDragging()) {\n        callbacks.tryAbort();\n        process.env.NODE_ENV !== \"production\" ? warning(`\n        An error was caught by our window 'error' event listener while a drag was occurring.\n        The active drag has been aborted.\n      `) : void 0;\n      }\n      const err = event.error;\n      if (err instanceof RbdInvariant) {\n        event.preventDefault();\n        if (process.env.NODE_ENV !== 'production') {\n          error(err.message);\n        }\n      }\n    };\n    this.getCallbacks = () => {\n      if (!this.callbacks) {\n        throw new Error('Unable to find AppCallbacks in <ErrorBoundary/>');\n      }\n      return this.callbacks;\n    };\n    this.setCallbacks = callbacks => {\n      this.callbacks = callbacks;\n    };\n  }\n  componentDidMount() {\n    this.unbind = bindEvents(window, [{\n      eventName: 'error',\n      fn: this.onWindowError\n    }]);\n  }\n  componentDidCatch(err) {\n    if (err instanceof RbdInvariant) {\n      if (process.env.NODE_ENV !== 'production') {\n        error(err.message);\n      }\n      this.setState({});\n      return;\n    }\n    throw err;\n  }\n  componentWillUnmount() {\n    this.unbind();\n  }\n  render() {\n    return this.props.children(this.setCallbacks);\n  }\n}\n\nconst dragHandleUsageInstructions = `\n  Press space bar to start a drag.\n  When dragging you can use the arrow keys to move the item around and escape to cancel.\n  Some screen readers may require you to be in focus mode or to use your pass through key\n`;\nconst position = index => index + 1;\nconst onDragStart = start => `\n  You have lifted an item in position ${position(start.source.index)}\n`;\nconst withLocation = (source, destination) => {\n  const isInHomeList = source.droppableId === destination.droppableId;\n  const startPosition = position(source.index);\n  const endPosition = position(destination.index);\n  if (isInHomeList) {\n    return `\n      You have moved the item from position ${startPosition}\n      to position ${endPosition}\n    `;\n  }\n  return `\n    You have moved the item from position ${startPosition}\n    in list ${source.droppableId}\n    to list ${destination.droppableId}\n    in position ${endPosition}\n  `;\n};\nconst withCombine = (id, source, combine) => {\n  const inHomeList = source.droppableId === combine.droppableId;\n  if (inHomeList) {\n    return `\n      The item ${id}\n      has been combined with ${combine.draggableId}`;\n  }\n  return `\n      The item ${id}\n      in list ${source.droppableId}\n      has been combined with ${combine.draggableId}\n      in list ${combine.droppableId}\n    `;\n};\nconst onDragUpdate = update => {\n  const location = update.destination;\n  if (location) {\n    return withLocation(update.source, location);\n  }\n  const combine = update.combine;\n  if (combine) {\n    return withCombine(update.draggableId, update.source, combine);\n  }\n  return 'You are over an area that cannot be dropped on';\n};\nconst returnedToStart = source => `\n  The item has returned to its starting position\n  of ${position(source.index)}\n`;\nconst onDragEnd = result => {\n  if (result.reason === 'CANCEL') {\n    return `\n      Movement cancelled.\n      ${returnedToStart(result.source)}\n    `;\n  }\n  const location = result.destination;\n  const combine = result.combine;\n  if (location) {\n    return `\n      You have dropped the item.\n      ${withLocation(result.source, location)}\n    `;\n  }\n  if (combine) {\n    return `\n      You have dropped the item.\n      ${withCombine(result.draggableId, result.source, combine)}\n    `;\n  }\n  return `\n    The item has been dropped while not over a drop area.\n    ${returnedToStart(result.source)}\n  `;\n};\nconst preset = {\n  dragHandleUsageInstructions,\n  onDragStart,\n  onDragUpdate,\n  onDragEnd\n};\n\nfunction isEqual$2(first, second) {\n  if (first === second) {\n    return true;\n  }\n  if (Number.isNaN(first) && Number.isNaN(second)) {\n    return true;\n  }\n  return false;\n}\nfunction areInputsEqual(newInputs, lastInputs) {\n  if (newInputs.length !== lastInputs.length) {\n    return false;\n  }\n  for (let i = 0; i < newInputs.length; i++) {\n    if (!isEqual$2(newInputs[i], lastInputs[i])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction useMemo(getResult, inputs) {\n  const initial = useState(() => ({\n    inputs,\n    result: getResult()\n  }))[0];\n  const isFirstRun = useRef(true);\n  const committed = useRef(initial);\n  const useCache = isFirstRun.current || Boolean(inputs && committed.current.inputs && areInputsEqual(inputs, committed.current.inputs));\n  const cache = useCache ? committed.current : {\n    inputs,\n    result: getResult()\n  };\n  useEffect(() => {\n    isFirstRun.current = false;\n    committed.current = cache;\n  }, [cache]);\n  return cache.result;\n}\nfunction useCallback(callback, inputs) {\n  return useMemo(() => callback, inputs);\n}\n\nconst origin = {\n  x: 0,\n  y: 0\n};\nconst add = (point1, point2) => ({\n  x: point1.x + point2.x,\n  y: point1.y + point2.y\n});\nconst subtract = (point1, point2) => ({\n  x: point1.x - point2.x,\n  y: point1.y - point2.y\n});\nconst isEqual$1 = (point1, point2) => point1.x === point2.x && point1.y === point2.y;\nconst negate = point => ({\n  x: point.x !== 0 ? -point.x : 0,\n  y: point.y !== 0 ? -point.y : 0\n});\nconst patch = (line, value, otherValue = 0) => {\n  if (line === 'x') {\n    return {\n      x: value,\n      y: otherValue\n    };\n  }\n  return {\n    x: otherValue,\n    y: value\n  };\n};\nconst distance = (point1, point2) => Math.sqrt((point2.x - point1.x) ** 2 + (point2.y - point1.y) ** 2);\nconst closest$1 = (target, points) => Math.min(...points.map(point => distance(target, point)));\nconst apply = fn => point => ({\n  x: fn(point.x),\n  y: fn(point.y)\n});\n\nvar executeClip = (frame, subject) => {\n  const result = getRect({\n    top: Math.max(subject.top, frame.top),\n    right: Math.min(subject.right, frame.right),\n    bottom: Math.min(subject.bottom, frame.bottom),\n    left: Math.max(subject.left, frame.left)\n  });\n  if (result.width <= 0 || result.height <= 0) {\n    return null;\n  }\n  return result;\n};\n\nconst offsetByPosition = (spacing, point) => ({\n  top: spacing.top + point.y,\n  left: spacing.left + point.x,\n  bottom: spacing.bottom + point.y,\n  right: spacing.right + point.x\n});\nconst getCorners = spacing => [{\n  x: spacing.left,\n  y: spacing.top\n}, {\n  x: spacing.right,\n  y: spacing.top\n}, {\n  x: spacing.left,\n  y: spacing.bottom\n}, {\n  x: spacing.right,\n  y: spacing.bottom\n}];\nconst noSpacing = {\n  top: 0,\n  right: 0,\n  bottom: 0,\n  left: 0\n};\n\nconst scroll$1 = (target, frame) => {\n  if (!frame) {\n    return target;\n  }\n  return offsetByPosition(target, frame.scroll.diff.displacement);\n};\nconst increase = (target, axis, withPlaceholder) => {\n  if (withPlaceholder && withPlaceholder.increasedBy) {\n    return {\n      ...target,\n      [axis.end]: target[axis.end] + withPlaceholder.increasedBy[axis.line]\n    };\n  }\n  return target;\n};\nconst clip = (target, frame) => {\n  if (frame && frame.shouldClipSubject) {\n    return executeClip(frame.pageMarginBox, target);\n  }\n  return getRect(target);\n};\nvar getSubject = ({\n  page,\n  withPlaceholder,\n  axis,\n  frame\n}) => {\n  const scrolled = scroll$1(page.marginBox, frame);\n  const increased = increase(scrolled, axis, withPlaceholder);\n  const clipped = clip(increased, frame);\n  return {\n    page,\n    withPlaceholder,\n    active: clipped\n  };\n};\n\nvar scrollDroppable = (droppable, newScroll) => {\n  !droppable.frame ? process.env.NODE_ENV !== \"production\" ? invariant() : invariant() : void 0;\n  const scrollable = droppable.frame;\n  const scrollDiff = subtract(newScroll, scrollable.scroll.initial);\n  const scrollDisplacement = negate(scrollDiff);\n  const frame = {\n    ...scrollable,\n    scroll: {\n      initial: scrollable.scroll.initial,\n      current: newScroll,\n      diff: {\n        value: scrollDiff,\n        displacement: scrollDisplacement\n      },\n      max: scrollable.scroll.max\n    }\n  };\n  const subject = getSubject({\n    page: droppable.subject.page,\n    withPlaceholder: droppable.subject.withPlaceholder,\n    axis: droppable.axis,\n    frame\n  });\n  const result = {\n    ...droppable,\n    frame,\n    subject\n  };\n  return result;\n};\n\nfunction memoizeOne(resultFn, isEqual = areInputsEqual) {\n  let cache = null;\n  function memoized(...newArgs) {\n    if (cache && cache.lastThis === this && isEqual(newArgs, cache.lastArgs)) {\n      return cache.lastResult;\n    }\n    const lastResult = resultFn.apply(this, newArgs);\n    cache = {\n      lastResult,\n      lastArgs: newArgs,\n      lastThis: this\n    };\n    return lastResult;\n  }\n  memoized.clear = function clear() {\n    cache = null;\n  };\n  return memoized;\n}\n\nconst toDroppableMap = memoizeOne(droppables => droppables.reduce((previous, current) => {\n  previous[current.descriptor.id] = current;\n  return previous;\n}, {}));\nconst toDraggableMap = memoizeOne(draggables => draggables.reduce((previous, current) => {\n  previous[current.descriptor.id] = current;\n  return previous;\n}, {}));\nconst toDroppableList = memoizeOne(droppables => Object.values(droppables));\nconst toDraggableList = memoizeOne(draggables => Object.values(draggables));\n\nvar getDraggablesInsideDroppable = memoizeOne((droppableId, draggables) => {\n  const result = toDraggableList(draggables).filter(draggable => droppableId === draggable.descriptor.droppableId).sort((a, b) => a.descriptor.index - b.descriptor.index);\n  return result;\n});\n\nfunction tryGetDestination(impact) {\n  if (impact.at && impact.at.type === 'REORDER') {\n    return impact.at.destination;\n  }\n  return null;\n}\nfunction tryGetCombine(impact) {\n  if (impact.at && impact.at.type === 'COMBINE') {\n    return impact.at.combine;\n  }\n  return null;\n}\n\nvar removeDraggableFromList = memoizeOne((remove, list) => list.filter(item => item.descriptor.id !== remove.descriptor.id));\n\nvar moveToNextCombine = ({\n  isMovingForward,\n  draggable,\n  destination,\n  insideDestination,\n  previousImpact\n}) => {\n  if (!destination.isCombineEnabled) {\n    return null;\n  }\n  const location = tryGetDestination(previousImpact);\n  if (!location) {\n    return null;\n  }\n  function getImpact(target) {\n    const at = {\n      type: 'COMBINE',\n      combine: {\n        draggableId: target,\n        droppableId: destination.descriptor.id\n      }\n    };\n    return {\n      ...previousImpact,\n      at\n    };\n  }\n  const all = previousImpact.displaced.all;\n  const closestId = all.length ? all[0] : null;\n  if (isMovingForward) {\n    return closestId ? getImpact(closestId) : null;\n  }\n  const withoutDraggable = removeDraggableFromList(draggable, insideDestination);\n  if (!closestId) {\n    if (!withoutDraggable.length) {\n      return null;\n    }\n    const last = withoutDraggable[withoutDraggable.length - 1];\n    return getImpact(last.descriptor.id);\n  }\n  const indexOfClosest = withoutDraggable.findIndex(d => d.descriptor.id === closestId);\n  !(indexOfClosest !== -1) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find displaced item in set') : invariant() : void 0;\n  const proposedIndex = indexOfClosest - 1;\n  if (proposedIndex < 0) {\n    return null;\n  }\n  const before = withoutDraggable[proposedIndex];\n  return getImpact(before.descriptor.id);\n};\n\nvar isHomeOf = (draggable, destination) => draggable.descriptor.droppableId === destination.descriptor.id;\n\nconst noDisplacedBy = {\n  point: origin,\n  value: 0\n};\nconst emptyGroups = {\n  invisible: {},\n  visible: {},\n  all: []\n};\nconst noImpact = {\n  displaced: emptyGroups,\n  displacedBy: noDisplacedBy,\n  at: null\n};\n\nvar isWithin = (lowerBound, upperBound) => value => lowerBound <= value && value <= upperBound;\n\nvar isPartiallyVisibleThroughFrame = frame => {\n  const isWithinVertical = isWithin(frame.top, frame.bottom);\n  const isWithinHorizontal = isWithin(frame.left, frame.right);\n  return subject => {\n    const isContained = isWithinVertical(subject.top) && isWithinVertical(subject.bottom) && isWithinHorizontal(subject.left) && isWithinHorizontal(subject.right);\n    if (isContained) {\n      return true;\n    }\n    const isPartiallyVisibleVertically = isWithinVertical(subject.top) || isWithinVertical(subject.bottom);\n    const isPartiallyVisibleHorizontally = isWithinHorizontal(subject.left) || isWithinHorizontal(subject.right);\n    const isPartiallyContained = isPartiallyVisibleVertically && isPartiallyVisibleHorizontally;\n    if (isPartiallyContained) {\n      return true;\n    }\n    const isBiggerVertically = subject.top < frame.top && subject.bottom > frame.bottom;\n    const isBiggerHorizontally = subject.left < frame.left && subject.right > frame.right;\n    const isTargetBiggerThanFrame = isBiggerVertically && isBiggerHorizontally;\n    if (isTargetBiggerThanFrame) {\n      return true;\n    }\n    const isTargetBiggerOnOneAxis = isBiggerVertically && isPartiallyVisibleHorizontally || isBiggerHorizontally && isPartiallyVisibleVertically;\n    return isTargetBiggerOnOneAxis;\n  };\n};\n\nvar isTotallyVisibleThroughFrame = frame => {\n  const isWithinVertical = isWithin(frame.top, frame.bottom);\n  const isWithinHorizontal = isWithin(frame.left, frame.right);\n  return subject => {\n    const isContained = isWithinVertical(subject.top) && isWithinVertical(subject.bottom) && isWithinHorizontal(subject.left) && isWithinHorizontal(subject.right);\n    return isContained;\n  };\n};\n\nconst vertical = {\n  direction: 'vertical',\n  line: 'y',\n  crossAxisLine: 'x',\n  start: 'top',\n  end: 'bottom',\n  size: 'height',\n  crossAxisStart: 'left',\n  crossAxisEnd: 'right',\n  crossAxisSize: 'width'\n};\nconst horizontal = {\n  direction: 'horizontal',\n  line: 'x',\n  crossAxisLine: 'y',\n  start: 'left',\n  end: 'right',\n  size: 'width',\n  crossAxisStart: 'top',\n  crossAxisEnd: 'bottom',\n  crossAxisSize: 'height'\n};\n\nvar isTotallyVisibleThroughFrameOnAxis = axis => frame => {\n  const isWithinVertical = isWithin(frame.top, frame.bottom);\n  const isWithinHorizontal = isWithin(frame.left, frame.right);\n  return subject => {\n    if (axis === vertical) {\n      return isWithinVertical(subject.top) && isWithinVertical(subject.bottom);\n    }\n    return isWithinHorizontal(subject.left) && isWithinHorizontal(subject.right);\n  };\n};\n\nconst getDroppableDisplaced = (target, destination) => {\n  const displacement = destination.frame ? destination.frame.scroll.diff.displacement : origin;\n  return offsetByPosition(target, displacement);\n};\nconst isVisibleInDroppable = (target, destination, isVisibleThroughFrameFn) => {\n  if (!destination.subject.active) {\n    return false;\n  }\n  return isVisibleThroughFrameFn(destination.subject.active)(target);\n};\nconst isVisibleInViewport = (target, viewport, isVisibleThroughFrameFn) => isVisibleThroughFrameFn(viewport)(target);\nconst isVisible$1 = ({\n  target: toBeDisplaced,\n  destination,\n  viewport,\n  withDroppableDisplacement,\n  isVisibleThroughFrameFn\n}) => {\n  const displacedTarget = withDroppableDisplacement ? getDroppableDisplaced(toBeDisplaced, destination) : toBeDisplaced;\n  return isVisibleInDroppable(displacedTarget, destination, isVisibleThroughFrameFn) && isVisibleInViewport(displacedTarget, viewport, isVisibleThroughFrameFn);\n};\nconst isPartiallyVisible = args => isVisible$1({\n  ...args,\n  isVisibleThroughFrameFn: isPartiallyVisibleThroughFrame\n});\nconst isTotallyVisible = args => isVisible$1({\n  ...args,\n  isVisibleThroughFrameFn: isTotallyVisibleThroughFrame\n});\nconst isTotallyVisibleOnAxis = args => isVisible$1({\n  ...args,\n  isVisibleThroughFrameFn: isTotallyVisibleThroughFrameOnAxis(args.destination.axis)\n});\n\nconst getShouldAnimate = (id, last, forceShouldAnimate) => {\n  if (typeof forceShouldAnimate === 'boolean') {\n    return forceShouldAnimate;\n  }\n  if (!last) {\n    return true;\n  }\n  const {\n    invisible,\n    visible\n  } = last;\n  if (invisible[id]) {\n    return false;\n  }\n  const previous = visible[id];\n  return previous ? previous.shouldAnimate : true;\n};\nfunction getTarget(draggable, displacedBy) {\n  const marginBox = draggable.page.marginBox;\n  const expandBy = {\n    top: displacedBy.point.y,\n    right: 0,\n    bottom: 0,\n    left: displacedBy.point.x\n  };\n  return getRect(expand(marginBox, expandBy));\n}\nfunction getDisplacementGroups({\n  afterDragging,\n  destination,\n  displacedBy,\n  viewport,\n  forceShouldAnimate,\n  last\n}) {\n  return afterDragging.reduce(function process(groups, draggable) {\n    const target = getTarget(draggable, displacedBy);\n    const id = draggable.descriptor.id;\n    groups.all.push(id);\n    const isVisible = isPartiallyVisible({\n      target,\n      destination,\n      viewport,\n      withDroppableDisplacement: true\n    });\n    if (!isVisible) {\n      groups.invisible[draggable.descriptor.id] = true;\n      return groups;\n    }\n    const shouldAnimate = getShouldAnimate(id, last, forceShouldAnimate);\n    const displacement = {\n      draggableId: id,\n      shouldAnimate\n    };\n    groups.visible[id] = displacement;\n    return groups;\n  }, {\n    all: [],\n    visible: {},\n    invisible: {}\n  });\n}\n\nfunction getIndexOfLastItem(draggables, options) {\n  if (!draggables.length) {\n    return 0;\n  }\n  const indexOfLastItem = draggables[draggables.length - 1].descriptor.index;\n  return options.inHomeList ? indexOfLastItem : indexOfLastItem + 1;\n}\nfunction goAtEnd({\n  insideDestination,\n  inHomeList,\n  displacedBy,\n  destination\n}) {\n  const newIndex = getIndexOfLastItem(insideDestination, {\n    inHomeList\n  });\n  return {\n    displaced: emptyGroups,\n    displacedBy,\n    at: {\n      type: 'REORDER',\n      destination: {\n        droppableId: destination.descriptor.id,\n        index: newIndex\n      }\n    }\n  };\n}\nfunction calculateReorderImpact({\n  draggable,\n  insideDestination,\n  destination,\n  viewport,\n  displacedBy,\n  last,\n  index,\n  forceShouldAnimate\n}) {\n  const inHomeList = isHomeOf(draggable, destination);\n  if (index == null) {\n    return goAtEnd({\n      insideDestination,\n      inHomeList,\n      displacedBy,\n      destination\n    });\n  }\n  const match = insideDestination.find(item => item.descriptor.index === index);\n  if (!match) {\n    return goAtEnd({\n      insideDestination,\n      inHomeList,\n      displacedBy,\n      destination\n    });\n  }\n  const withoutDragging = removeDraggableFromList(draggable, insideDestination);\n  const sliceFrom = insideDestination.indexOf(match);\n  const impacted = withoutDragging.slice(sliceFrom);\n  const displaced = getDisplacementGroups({\n    afterDragging: impacted,\n    destination,\n    displacedBy,\n    last,\n    viewport: viewport.frame,\n    forceShouldAnimate\n  });\n  return {\n    displaced,\n    displacedBy,\n    at: {\n      type: 'REORDER',\n      destination: {\n        droppableId: destination.descriptor.id,\n        index\n      }\n    }\n  };\n}\n\nfunction didStartAfterCritical(draggableId, afterCritical) {\n  return Boolean(afterCritical.effected[draggableId]);\n}\n\nvar fromCombine = ({\n  isMovingForward,\n  destination,\n  draggables,\n  combine,\n  afterCritical\n}) => {\n  if (!destination.isCombineEnabled) {\n    return null;\n  }\n  const combineId = combine.draggableId;\n  const combineWith = draggables[combineId];\n  const combineWithIndex = combineWith.descriptor.index;\n  const didCombineWithStartAfterCritical = didStartAfterCritical(combineId, afterCritical);\n  if (didCombineWithStartAfterCritical) {\n    if (isMovingForward) {\n      return combineWithIndex;\n    }\n    return combineWithIndex - 1;\n  }\n  if (isMovingForward) {\n    return combineWithIndex + 1;\n  }\n  return combineWithIndex;\n};\n\nvar fromReorder = ({\n  isMovingForward,\n  isInHomeList,\n  insideDestination,\n  location\n}) => {\n  if (!insideDestination.length) {\n    return null;\n  }\n  const currentIndex = location.index;\n  const proposedIndex = isMovingForward ? currentIndex + 1 : currentIndex - 1;\n  const firstIndex = insideDestination[0].descriptor.index;\n  const lastIndex = insideDestination[insideDestination.length - 1].descriptor.index;\n  const upperBound = isInHomeList ? lastIndex : lastIndex + 1;\n  if (proposedIndex < firstIndex) {\n    return null;\n  }\n  if (proposedIndex > upperBound) {\n    return null;\n  }\n  return proposedIndex;\n};\n\nvar moveToNextIndex = ({\n  isMovingForward,\n  isInHomeList,\n  draggable,\n  draggables,\n  destination,\n  insideDestination,\n  previousImpact,\n  viewport,\n  afterCritical\n}) => {\n  const wasAt = previousImpact.at;\n  !wasAt ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot move in direction without previous impact location') : invariant() : void 0;\n  if (wasAt.type === 'REORDER') {\n    const newIndex = fromReorder({\n      isMovingForward,\n      isInHomeList,\n      location: wasAt.destination,\n      insideDestination\n    });\n    if (newIndex == null) {\n      return null;\n    }\n    return calculateReorderImpact({\n      draggable,\n      insideDestination,\n      destination,\n      viewport,\n      last: previousImpact.displaced,\n      displacedBy: previousImpact.displacedBy,\n      index: newIndex\n    });\n  }\n  const newIndex = fromCombine({\n    isMovingForward,\n    destination,\n    displaced: previousImpact.displaced,\n    draggables,\n    combine: wasAt.combine,\n    afterCritical\n  });\n  if (newIndex == null) {\n    return null;\n  }\n  return calculateReorderImpact({\n    draggable,\n    insideDestination,\n    destination,\n    viewport,\n    last: previousImpact.displaced,\n    displacedBy: previousImpact.displacedBy,\n    index: newIndex\n  });\n};\n\nvar getCombinedItemDisplacement = ({\n  displaced,\n  afterCritical,\n  combineWith,\n  displacedBy\n}) => {\n  const isDisplaced = Boolean(displaced.visible[combineWith] || displaced.invisible[combineWith]);\n  if (didStartAfterCritical(combineWith, afterCritical)) {\n    return isDisplaced ? origin : negate(displacedBy.point);\n  }\n  return isDisplaced ? displacedBy.point : origin;\n};\n\nvar whenCombining = ({\n  afterCritical,\n  impact,\n  draggables\n}) => {\n  const combine = tryGetCombine(impact);\n  !combine ? process.env.NODE_ENV !== \"production\" ? invariant() : invariant() : void 0;\n  const combineWith = combine.draggableId;\n  const center = draggables[combineWith].page.borderBox.center;\n  const displaceBy = getCombinedItemDisplacement({\n    displaced: impact.displaced,\n    afterCritical,\n    combineWith,\n    displacedBy: impact.displacedBy\n  });\n  return add(center, displaceBy);\n};\n\nconst distanceFromStartToBorderBoxCenter = (axis, box) => box.margin[axis.start] + box.borderBox[axis.size] / 2;\nconst distanceFromEndToBorderBoxCenter = (axis, box) => box.margin[axis.end] + box.borderBox[axis.size] / 2;\nconst getCrossAxisBorderBoxCenter = (axis, target, isMoving) => target[axis.crossAxisStart] + isMoving.margin[axis.crossAxisStart] + isMoving.borderBox[axis.crossAxisSize] / 2;\nconst goAfter = ({\n  axis,\n  moveRelativeTo,\n  isMoving\n}) => patch(axis.line, moveRelativeTo.marginBox[axis.end] + distanceFromStartToBorderBoxCenter(axis, isMoving), getCrossAxisBorderBoxCenter(axis, moveRelativeTo.marginBox, isMoving));\nconst goBefore = ({\n  axis,\n  moveRelativeTo,\n  isMoving\n}) => patch(axis.line, moveRelativeTo.marginBox[axis.start] - distanceFromEndToBorderBoxCenter(axis, isMoving), getCrossAxisBorderBoxCenter(axis, moveRelativeTo.marginBox, isMoving));\nconst goIntoStart = ({\n  axis,\n  moveInto,\n  isMoving\n}) => patch(axis.line, moveInto.contentBox[axis.start] + distanceFromStartToBorderBoxCenter(axis, isMoving), getCrossAxisBorderBoxCenter(axis, moveInto.contentBox, isMoving));\n\nvar whenReordering = ({\n  impact,\n  draggable,\n  draggables,\n  droppable,\n  afterCritical\n}) => {\n  const insideDestination = getDraggablesInsideDroppable(droppable.descriptor.id, draggables);\n  const draggablePage = draggable.page;\n  const axis = droppable.axis;\n  if (!insideDestination.length) {\n    return goIntoStart({\n      axis,\n      moveInto: droppable.page,\n      isMoving: draggablePage\n    });\n  }\n  const {\n    displaced,\n    displacedBy\n  } = impact;\n  const closestAfter = displaced.all[0];\n  if (closestAfter) {\n    const closest = draggables[closestAfter];\n    if (didStartAfterCritical(closestAfter, afterCritical)) {\n      return goBefore({\n        axis,\n        moveRelativeTo: closest.page,\n        isMoving: draggablePage\n      });\n    }\n    const withDisplacement = offset(closest.page, displacedBy.point);\n    return goBefore({\n      axis,\n      moveRelativeTo: withDisplacement,\n      isMoving: draggablePage\n    });\n  }\n  const last = insideDestination[insideDestination.length - 1];\n  if (last.descriptor.id === draggable.descriptor.id) {\n    return draggablePage.borderBox.center;\n  }\n  if (didStartAfterCritical(last.descriptor.id, afterCritical)) {\n    const page = offset(last.page, negate(afterCritical.displacedBy.point));\n    return goAfter({\n      axis,\n      moveRelativeTo: page,\n      isMoving: draggablePage\n    });\n  }\n  return goAfter({\n    axis,\n    moveRelativeTo: last.page,\n    isMoving: draggablePage\n  });\n};\n\nvar withDroppableDisplacement = (droppable, point) => {\n  const frame = droppable.frame;\n  if (!frame) {\n    return point;\n  }\n  return add(point, frame.scroll.diff.displacement);\n};\n\nconst getResultWithoutDroppableDisplacement = ({\n  impact,\n  draggable,\n  droppable,\n  draggables,\n  afterCritical\n}) => {\n  const original = draggable.page.borderBox.center;\n  const at = impact.at;\n  if (!droppable) {\n    return original;\n  }\n  if (!at) {\n    return original;\n  }\n  if (at.type === 'REORDER') {\n    return whenReordering({\n      impact,\n      draggable,\n      draggables,\n      droppable,\n      afterCritical\n    });\n  }\n  return whenCombining({\n    impact,\n    draggables,\n    afterCritical\n  });\n};\nvar getPageBorderBoxCenterFromImpact = args => {\n  const withoutDisplacement = getResultWithoutDroppableDisplacement(args);\n  const droppable = args.droppable;\n  const withDisplacement = droppable ? withDroppableDisplacement(droppable, withoutDisplacement) : withoutDisplacement;\n  return withDisplacement;\n};\n\nvar scrollViewport = (viewport, newScroll) => {\n  const diff = subtract(newScroll, viewport.scroll.initial);\n  const displacement = negate(diff);\n  const frame = getRect({\n    top: newScroll.y,\n    bottom: newScroll.y + viewport.frame.height,\n    left: newScroll.x,\n    right: newScroll.x + viewport.frame.width\n  });\n  const updated = {\n    frame,\n    scroll: {\n      initial: viewport.scroll.initial,\n      max: viewport.scroll.max,\n      current: newScroll,\n      diff: {\n        value: diff,\n        displacement\n      }\n    }\n  };\n  return updated;\n};\n\nfunction getDraggables$1(ids, draggables) {\n  return ids.map(id => draggables[id]);\n}\nfunction tryGetVisible(id, groups) {\n  for (let i = 0; i < groups.length; i++) {\n    const displacement = groups[i].visible[id];\n    if (displacement) {\n      return displacement;\n    }\n  }\n  return null;\n}\nvar speculativelyIncrease = ({\n  impact,\n  viewport,\n  destination,\n  draggables,\n  maxScrollChange\n}) => {\n  const scrolledViewport = scrollViewport(viewport, add(viewport.scroll.current, maxScrollChange));\n  const scrolledDroppable = destination.frame ? scrollDroppable(destination, add(destination.frame.scroll.current, maxScrollChange)) : destination;\n  const last = impact.displaced;\n  const withViewportScroll = getDisplacementGroups({\n    afterDragging: getDraggables$1(last.all, draggables),\n    destination,\n    displacedBy: impact.displacedBy,\n    viewport: scrolledViewport.frame,\n    last,\n    forceShouldAnimate: false\n  });\n  const withDroppableScroll = getDisplacementGroups({\n    afterDragging: getDraggables$1(last.all, draggables),\n    destination: scrolledDroppable,\n    displacedBy: impact.displacedBy,\n    viewport: viewport.frame,\n    last,\n    forceShouldAnimate: false\n  });\n  const invisible = {};\n  const visible = {};\n  const groups = [last, withViewportScroll, withDroppableScroll];\n  last.all.forEach(id => {\n    const displacement = tryGetVisible(id, groups);\n    if (displacement) {\n      visible[id] = displacement;\n      return;\n    }\n    invisible[id] = true;\n  });\n  const newImpact = {\n    ...impact,\n    displaced: {\n      all: last.all,\n      invisible,\n      visible\n    }\n  };\n  return newImpact;\n};\n\nvar withViewportDisplacement = (viewport, point) => add(viewport.scroll.diff.displacement, point);\n\nvar getClientFromPageBorderBoxCenter = ({\n  pageBorderBoxCenter,\n  draggable,\n  viewport\n}) => {\n  const withoutPageScrollChange = withViewportDisplacement(viewport, pageBorderBoxCenter);\n  const offset = subtract(withoutPageScrollChange, draggable.page.borderBox.center);\n  return add(draggable.client.borderBox.center, offset);\n};\n\nvar isTotallyVisibleInNewLocation = ({\n  draggable,\n  destination,\n  newPageBorderBoxCenter,\n  viewport,\n  withDroppableDisplacement,\n  onlyOnMainAxis = false\n}) => {\n  const changeNeeded = subtract(newPageBorderBoxCenter, draggable.page.borderBox.center);\n  const shifted = offsetByPosition(draggable.page.borderBox, changeNeeded);\n  const args = {\n    target: shifted,\n    destination,\n    withDroppableDisplacement,\n    viewport\n  };\n  return onlyOnMainAxis ? isTotallyVisibleOnAxis(args) : isTotallyVisible(args);\n};\n\nvar moveToNextPlace = ({\n  isMovingForward,\n  draggable,\n  destination,\n  draggables,\n  previousImpact,\n  viewport,\n  previousPageBorderBoxCenter,\n  previousClientSelection,\n  afterCritical\n}) => {\n  if (!destination.isEnabled) {\n    return null;\n  }\n  const insideDestination = getDraggablesInsideDroppable(destination.descriptor.id, draggables);\n  const isInHomeList = isHomeOf(draggable, destination);\n  const impact = moveToNextCombine({\n    isMovingForward,\n    draggable,\n    destination,\n    insideDestination,\n    previousImpact\n  }) || moveToNextIndex({\n    isMovingForward,\n    isInHomeList,\n    draggable,\n    draggables,\n    destination,\n    insideDestination,\n    previousImpact,\n    viewport,\n    afterCritical\n  });\n  if (!impact) {\n    return null;\n  }\n  const pageBorderBoxCenter = getPageBorderBoxCenterFromImpact({\n    impact,\n    draggable,\n    droppable: destination,\n    draggables,\n    afterCritical\n  });\n  const isVisibleInNewLocation = isTotallyVisibleInNewLocation({\n    draggable,\n    destination,\n    newPageBorderBoxCenter: pageBorderBoxCenter,\n    viewport: viewport.frame,\n    withDroppableDisplacement: false,\n    onlyOnMainAxis: true\n  });\n  if (isVisibleInNewLocation) {\n    const clientSelection = getClientFromPageBorderBoxCenter({\n      pageBorderBoxCenter,\n      draggable,\n      viewport\n    });\n    return {\n      clientSelection,\n      impact,\n      scrollJumpRequest: null\n    };\n  }\n  const distance = subtract(pageBorderBoxCenter, previousPageBorderBoxCenter);\n  const cautious = speculativelyIncrease({\n    impact,\n    viewport,\n    destination,\n    draggables,\n    maxScrollChange: distance\n  });\n  return {\n    clientSelection: previousClientSelection,\n    impact: cautious,\n    scrollJumpRequest: distance\n  };\n};\n\nconst getKnownActive = droppable => {\n  const rect = droppable.subject.active;\n  !rect ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot get clipped area from droppable') : invariant() : void 0;\n  return rect;\n};\nvar getBestCrossAxisDroppable = ({\n  isMovingForward,\n  pageBorderBoxCenter,\n  source,\n  droppables,\n  viewport\n}) => {\n  const active = source.subject.active;\n  if (!active) {\n    return null;\n  }\n  const axis = source.axis;\n  const isBetweenSourceClipped = isWithin(active[axis.start], active[axis.end]);\n  const candidates = toDroppableList(droppables).filter(droppable => droppable !== source).filter(droppable => droppable.isEnabled).filter(droppable => Boolean(droppable.subject.active)).filter(droppable => isPartiallyVisibleThroughFrame(viewport.frame)(getKnownActive(droppable))).filter(droppable => {\n    const activeOfTarget = getKnownActive(droppable);\n    if (isMovingForward) {\n      return active[axis.crossAxisEnd] < activeOfTarget[axis.crossAxisEnd];\n    }\n    return activeOfTarget[axis.crossAxisStart] < active[axis.crossAxisStart];\n  }).filter(droppable => {\n    const activeOfTarget = getKnownActive(droppable);\n    const isBetweenDestinationClipped = isWithin(activeOfTarget[axis.start], activeOfTarget[axis.end]);\n    return isBetweenSourceClipped(activeOfTarget[axis.start]) || isBetweenSourceClipped(activeOfTarget[axis.end]) || isBetweenDestinationClipped(active[axis.start]) || isBetweenDestinationClipped(active[axis.end]);\n  }).sort((a, b) => {\n    const first = getKnownActive(a)[axis.crossAxisStart];\n    const second = getKnownActive(b)[axis.crossAxisStart];\n    if (isMovingForward) {\n      return first - second;\n    }\n    return second - first;\n  }).filter((droppable, index, array) => getKnownActive(droppable)[axis.crossAxisStart] === getKnownActive(array[0])[axis.crossAxisStart]);\n  if (!candidates.length) {\n    return null;\n  }\n  if (candidates.length === 1) {\n    return candidates[0];\n  }\n  const contains = candidates.filter(droppable => {\n    const isWithinDroppable = isWithin(getKnownActive(droppable)[axis.start], getKnownActive(droppable)[axis.end]);\n    return isWithinDroppable(pageBorderBoxCenter[axis.line]);\n  });\n  if (contains.length === 1) {\n    return contains[0];\n  }\n  if (contains.length > 1) {\n    return contains.sort((a, b) => getKnownActive(a)[axis.start] - getKnownActive(b)[axis.start])[0];\n  }\n  return candidates.sort((a, b) => {\n    const first = closest$1(pageBorderBoxCenter, getCorners(getKnownActive(a)));\n    const second = closest$1(pageBorderBoxCenter, getCorners(getKnownActive(b)));\n    if (first !== second) {\n      return first - second;\n    }\n    return getKnownActive(a)[axis.start] - getKnownActive(b)[axis.start];\n  })[0];\n};\n\nconst getCurrentPageBorderBoxCenter = (draggable, afterCritical) => {\n  const original = draggable.page.borderBox.center;\n  return didStartAfterCritical(draggable.descriptor.id, afterCritical) ? subtract(original, afterCritical.displacedBy.point) : original;\n};\nconst getCurrentPageBorderBox = (draggable, afterCritical) => {\n  const original = draggable.page.borderBox;\n  return didStartAfterCritical(draggable.descriptor.id, afterCritical) ? offsetByPosition(original, negate(afterCritical.displacedBy.point)) : original;\n};\n\nvar getClosestDraggable = ({\n  pageBorderBoxCenter,\n  viewport,\n  destination,\n  insideDestination,\n  afterCritical\n}) => {\n  const sorted = insideDestination.filter(draggable => isTotallyVisible({\n    target: getCurrentPageBorderBox(draggable, afterCritical),\n    destination,\n    viewport: viewport.frame,\n    withDroppableDisplacement: true\n  })).sort((a, b) => {\n    const distanceToA = distance(pageBorderBoxCenter, withDroppableDisplacement(destination, getCurrentPageBorderBoxCenter(a, afterCritical)));\n    const distanceToB = distance(pageBorderBoxCenter, withDroppableDisplacement(destination, getCurrentPageBorderBoxCenter(b, afterCritical)));\n    if (distanceToA < distanceToB) {\n      return -1;\n    }\n    if (distanceToB < distanceToA) {\n      return 1;\n    }\n    return a.descriptor.index - b.descriptor.index;\n  });\n  return sorted[0] || null;\n};\n\nvar getDisplacedBy = memoizeOne(function getDisplacedBy(axis, displaceBy) {\n  const displacement = displaceBy[axis.line];\n  return {\n    value: displacement,\n    point: patch(axis.line, displacement)\n  };\n});\n\nconst getRequiredGrowthForPlaceholder = (droppable, placeholderSize, draggables) => {\n  const axis = droppable.axis;\n  if (droppable.descriptor.mode === 'virtual') {\n    return patch(axis.line, placeholderSize[axis.line]);\n  }\n  const availableSpace = droppable.subject.page.contentBox[axis.size];\n  const insideDroppable = getDraggablesInsideDroppable(droppable.descriptor.id, draggables);\n  const spaceUsed = insideDroppable.reduce((sum, dimension) => sum + dimension.client.marginBox[axis.size], 0);\n  const requiredSpace = spaceUsed + placeholderSize[axis.line];\n  const needsToGrowBy = requiredSpace - availableSpace;\n  if (needsToGrowBy <= 0) {\n    return null;\n  }\n  return patch(axis.line, needsToGrowBy);\n};\nconst withMaxScroll = (frame, max) => ({\n  ...frame,\n  scroll: {\n    ...frame.scroll,\n    max\n  }\n});\nconst addPlaceholder = (droppable, draggable, draggables) => {\n  const frame = droppable.frame;\n  !!isHomeOf(draggable, droppable) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Should not add placeholder space to home list') : invariant() : void 0;\n  !!droppable.subject.withPlaceholder ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot add placeholder size to a subject when it already has one') : invariant() : void 0;\n  const placeholderSize = getDisplacedBy(droppable.axis, draggable.displaceBy).point;\n  const requiredGrowth = getRequiredGrowthForPlaceholder(droppable, placeholderSize, draggables);\n  const added = {\n    placeholderSize,\n    increasedBy: requiredGrowth,\n    oldFrameMaxScroll: droppable.frame ? droppable.frame.scroll.max : null\n  };\n  if (!frame) {\n    const subject = getSubject({\n      page: droppable.subject.page,\n      withPlaceholder: added,\n      axis: droppable.axis,\n      frame: droppable.frame\n    });\n    return {\n      ...droppable,\n      subject\n    };\n  }\n  const maxScroll = requiredGrowth ? add(frame.scroll.max, requiredGrowth) : frame.scroll.max;\n  const newFrame = withMaxScroll(frame, maxScroll);\n  const subject = getSubject({\n    page: droppable.subject.page,\n    withPlaceholder: added,\n    axis: droppable.axis,\n    frame: newFrame\n  });\n  return {\n    ...droppable,\n    subject,\n    frame: newFrame\n  };\n};\nconst removePlaceholder = droppable => {\n  const added = droppable.subject.withPlaceholder;\n  !added ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot remove placeholder form subject when there was none') : invariant() : void 0;\n  const frame = droppable.frame;\n  if (!frame) {\n    const subject = getSubject({\n      page: droppable.subject.page,\n      axis: droppable.axis,\n      frame: null,\n      withPlaceholder: null\n    });\n    return {\n      ...droppable,\n      subject\n    };\n  }\n  const oldMaxScroll = added.oldFrameMaxScroll;\n  !oldMaxScroll ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected droppable with frame to have old max frame scroll when removing placeholder') : invariant() : void 0;\n  const newFrame = withMaxScroll(frame, oldMaxScroll);\n  const subject = getSubject({\n    page: droppable.subject.page,\n    axis: droppable.axis,\n    frame: newFrame,\n    withPlaceholder: null\n  });\n  return {\n    ...droppable,\n    subject,\n    frame: newFrame\n  };\n};\n\nvar moveToNewDroppable = ({\n  previousPageBorderBoxCenter,\n  moveRelativeTo,\n  insideDestination,\n  draggable,\n  draggables,\n  destination,\n  viewport,\n  afterCritical\n}) => {\n  if (!moveRelativeTo) {\n    if (insideDestination.length) {\n      return null;\n    }\n    const proposed = {\n      displaced: emptyGroups,\n      displacedBy: noDisplacedBy,\n      at: {\n        type: 'REORDER',\n        destination: {\n          droppableId: destination.descriptor.id,\n          index: 0\n        }\n      }\n    };\n    const proposedPageBorderBoxCenter = getPageBorderBoxCenterFromImpact({\n      impact: proposed,\n      draggable,\n      droppable: destination,\n      draggables,\n      afterCritical\n    });\n    const withPlaceholder = isHomeOf(draggable, destination) ? destination : addPlaceholder(destination, draggable, draggables);\n    const isVisibleInNewLocation = isTotallyVisibleInNewLocation({\n      draggable,\n      destination: withPlaceholder,\n      newPageBorderBoxCenter: proposedPageBorderBoxCenter,\n      viewport: viewport.frame,\n      withDroppableDisplacement: false,\n      onlyOnMainAxis: true\n    });\n    return isVisibleInNewLocation ? proposed : null;\n  }\n  const isGoingBeforeTarget = Boolean(previousPageBorderBoxCenter[destination.axis.line] <= moveRelativeTo.page.borderBox.center[destination.axis.line]);\n  const proposedIndex = (() => {\n    const relativeTo = moveRelativeTo.descriptor.index;\n    if (moveRelativeTo.descriptor.id === draggable.descriptor.id) {\n      return relativeTo;\n    }\n    if (isGoingBeforeTarget) {\n      return relativeTo;\n    }\n    return relativeTo + 1;\n  })();\n  const displacedBy = getDisplacedBy(destination.axis, draggable.displaceBy);\n  return calculateReorderImpact({\n    draggable,\n    insideDestination,\n    destination,\n    viewport,\n    displacedBy,\n    last: emptyGroups,\n    index: proposedIndex\n  });\n};\n\nvar moveCrossAxis = ({\n  isMovingForward,\n  previousPageBorderBoxCenter,\n  draggable,\n  isOver,\n  draggables,\n  droppables,\n  viewport,\n  afterCritical\n}) => {\n  const destination = getBestCrossAxisDroppable({\n    isMovingForward,\n    pageBorderBoxCenter: previousPageBorderBoxCenter,\n    source: isOver,\n    droppables,\n    viewport\n  });\n  if (!destination) {\n    return null;\n  }\n  const insideDestination = getDraggablesInsideDroppable(destination.descriptor.id, draggables);\n  const moveRelativeTo = getClosestDraggable({\n    pageBorderBoxCenter: previousPageBorderBoxCenter,\n    viewport,\n    destination,\n    insideDestination,\n    afterCritical\n  });\n  const impact = moveToNewDroppable({\n    previousPageBorderBoxCenter,\n    destination,\n    draggable,\n    draggables,\n    moveRelativeTo,\n    insideDestination,\n    viewport,\n    afterCritical\n  });\n  if (!impact) {\n    return null;\n  }\n  const pageBorderBoxCenter = getPageBorderBoxCenterFromImpact({\n    impact,\n    draggable,\n    droppable: destination,\n    draggables,\n    afterCritical\n  });\n  const clientSelection = getClientFromPageBorderBoxCenter({\n    pageBorderBoxCenter,\n    draggable,\n    viewport\n  });\n  return {\n    clientSelection,\n    impact,\n    scrollJumpRequest: null\n  };\n};\n\nvar whatIsDraggedOver = impact => {\n  const at = impact.at;\n  if (!at) {\n    return null;\n  }\n  if (at.type === 'REORDER') {\n    return at.destination.droppableId;\n  }\n  return at.combine.droppableId;\n};\n\nconst getDroppableOver$1 = (impact, droppables) => {\n  const id = whatIsDraggedOver(impact);\n  return id ? droppables[id] : null;\n};\nvar moveInDirection = ({\n  state,\n  type\n}) => {\n  const isActuallyOver = getDroppableOver$1(state.impact, state.dimensions.droppables);\n  const isMainAxisMovementAllowed = Boolean(isActuallyOver);\n  const home = state.dimensions.droppables[state.critical.droppable.id];\n  const isOver = isActuallyOver || home;\n  const direction = isOver.axis.direction;\n  const isMovingOnMainAxis = direction === 'vertical' && (type === 'MOVE_UP' || type === 'MOVE_DOWN') || direction === 'horizontal' && (type === 'MOVE_LEFT' || type === 'MOVE_RIGHT');\n  if (isMovingOnMainAxis && !isMainAxisMovementAllowed) {\n    return null;\n  }\n  const isMovingForward = type === 'MOVE_DOWN' || type === 'MOVE_RIGHT';\n  const draggable = state.dimensions.draggables[state.critical.draggable.id];\n  const previousPageBorderBoxCenter = state.current.page.borderBoxCenter;\n  const {\n    draggables,\n    droppables\n  } = state.dimensions;\n  return isMovingOnMainAxis ? moveToNextPlace({\n    isMovingForward,\n    previousPageBorderBoxCenter,\n    draggable,\n    destination: isOver,\n    draggables,\n    viewport: state.viewport,\n    previousClientSelection: state.current.client.selection,\n    previousImpact: state.impact,\n    afterCritical: state.afterCritical\n  }) : moveCrossAxis({\n    isMovingForward,\n    previousPageBorderBoxCenter,\n    draggable,\n    isOver,\n    draggables,\n    droppables,\n    viewport: state.viewport,\n    afterCritical: state.afterCritical\n  });\n};\n\nfunction isMovementAllowed(state) {\n  return state.phase === 'DRAGGING' || state.phase === 'COLLECTING';\n}\n\nfunction isPositionInFrame(frame) {\n  const isWithinVertical = isWithin(frame.top, frame.bottom);\n  const isWithinHorizontal = isWithin(frame.left, frame.right);\n  return function run(point) {\n    return isWithinVertical(point.y) && isWithinHorizontal(point.x);\n  };\n}\n\nfunction getHasOverlap(first, second) {\n  return first.left < second.right && first.right > second.left && first.top < second.bottom && first.bottom > second.top;\n}\nfunction getFurthestAway({\n  pageBorderBox,\n  draggable,\n  candidates\n}) {\n  const startCenter = draggable.page.borderBox.center;\n  const sorted = candidates.map(candidate => {\n    const axis = candidate.axis;\n    const target = patch(candidate.axis.line, pageBorderBox.center[axis.line], candidate.page.borderBox.center[axis.crossAxisLine]);\n    return {\n      id: candidate.descriptor.id,\n      distance: distance(startCenter, target)\n    };\n  }).sort((a, b) => b.distance - a.distance);\n  return sorted[0] ? sorted[0].id : null;\n}\nfunction getDroppableOver({\n  pageBorderBox,\n  draggable,\n  droppables\n}) {\n  const candidates = toDroppableList(droppables).filter(item => {\n    if (!item.isEnabled) {\n      return false;\n    }\n    const active = item.subject.active;\n    if (!active) {\n      return false;\n    }\n    if (!getHasOverlap(pageBorderBox, active)) {\n      return false;\n    }\n    if (isPositionInFrame(active)(pageBorderBox.center)) {\n      return true;\n    }\n    const axis = item.axis;\n    const childCenter = active.center[axis.crossAxisLine];\n    const crossAxisStart = pageBorderBox[axis.crossAxisStart];\n    const crossAxisEnd = pageBorderBox[axis.crossAxisEnd];\n    const isContained = isWithin(active[axis.crossAxisStart], active[axis.crossAxisEnd]);\n    const isStartContained = isContained(crossAxisStart);\n    const isEndContained = isContained(crossAxisEnd);\n    if (!isStartContained && !isEndContained) {\n      return true;\n    }\n    if (isStartContained) {\n      return crossAxisStart < childCenter;\n    }\n    return crossAxisEnd > childCenter;\n  });\n  if (!candidates.length) {\n    return null;\n  }\n  if (candidates.length === 1) {\n    return candidates[0].descriptor.id;\n  }\n  return getFurthestAway({\n    pageBorderBox,\n    draggable,\n    candidates\n  });\n}\n\nconst offsetRectByPosition = (rect, point) => getRect(offsetByPosition(rect, point));\n\nvar withDroppableScroll = (droppable, area) => {\n  const frame = droppable.frame;\n  if (!frame) {\n    return area;\n  }\n  return offsetRectByPosition(area, frame.scroll.diff.value);\n};\n\nfunction getIsDisplaced({\n  displaced,\n  id\n}) {\n  return Boolean(displaced.visible[id] || displaced.invisible[id]);\n}\n\nfunction atIndex({\n  draggable,\n  closest,\n  inHomeList\n}) {\n  if (!closest) {\n    return null;\n  }\n  if (!inHomeList) {\n    return closest.descriptor.index;\n  }\n  if (closest.descriptor.index > draggable.descriptor.index) {\n    return closest.descriptor.index - 1;\n  }\n  return closest.descriptor.index;\n}\nvar getReorderImpact = ({\n  pageBorderBoxWithDroppableScroll: targetRect,\n  draggable,\n  destination,\n  insideDestination,\n  last,\n  viewport,\n  afterCritical\n}) => {\n  const axis = destination.axis;\n  const displacedBy = getDisplacedBy(destination.axis, draggable.displaceBy);\n  const displacement = displacedBy.value;\n  const targetStart = targetRect[axis.start];\n  const targetEnd = targetRect[axis.end];\n  const withoutDragging = removeDraggableFromList(draggable, insideDestination);\n  const closest = withoutDragging.find(child => {\n    const id = child.descriptor.id;\n    const childCenter = child.page.borderBox.center[axis.line];\n    const didStartAfterCritical$1 = didStartAfterCritical(id, afterCritical);\n    const isDisplaced = getIsDisplaced({\n      displaced: last,\n      id\n    });\n    if (didStartAfterCritical$1) {\n      if (isDisplaced) {\n        return targetEnd <= childCenter;\n      }\n      return targetStart < childCenter - displacement;\n    }\n    if (isDisplaced) {\n      return targetEnd <= childCenter + displacement;\n    }\n    return targetStart < childCenter;\n  }) || null;\n  const newIndex = atIndex({\n    draggable,\n    closest,\n    inHomeList: isHomeOf(draggable, destination)\n  });\n  return calculateReorderImpact({\n    draggable,\n    insideDestination,\n    destination,\n    viewport,\n    last,\n    displacedBy,\n    index: newIndex\n  });\n};\n\nconst combineThresholdDivisor = 4;\nvar getCombineImpact = ({\n  draggable,\n  pageBorderBoxWithDroppableScroll: targetRect,\n  previousImpact,\n  destination,\n  insideDestination,\n  afterCritical\n}) => {\n  if (!destination.isCombineEnabled) {\n    return null;\n  }\n  const axis = destination.axis;\n  const displacedBy = getDisplacedBy(destination.axis, draggable.displaceBy);\n  const displacement = displacedBy.value;\n  const targetStart = targetRect[axis.start];\n  const targetEnd = targetRect[axis.end];\n  const withoutDragging = removeDraggableFromList(draggable, insideDestination);\n  const combineWith = withoutDragging.find(child => {\n    const id = child.descriptor.id;\n    const childRect = child.page.borderBox;\n    const childSize = childRect[axis.size];\n    const threshold = childSize / combineThresholdDivisor;\n    const didStartAfterCritical$1 = didStartAfterCritical(id, afterCritical);\n    const isDisplaced = getIsDisplaced({\n      displaced: previousImpact.displaced,\n      id\n    });\n    if (didStartAfterCritical$1) {\n      if (isDisplaced) {\n        return targetEnd > childRect[axis.start] + threshold && targetEnd < childRect[axis.end] - threshold;\n      }\n      return targetStart > childRect[axis.start] - displacement + threshold && targetStart < childRect[axis.end] - displacement - threshold;\n    }\n    if (isDisplaced) {\n      return targetEnd > childRect[axis.start] + displacement + threshold && targetEnd < childRect[axis.end] + displacement - threshold;\n    }\n    return targetStart > childRect[axis.start] + threshold && targetStart < childRect[axis.end] - threshold;\n  });\n  if (!combineWith) {\n    return null;\n  }\n  const impact = {\n    displacedBy,\n    displaced: previousImpact.displaced,\n    at: {\n      type: 'COMBINE',\n      combine: {\n        draggableId: combineWith.descriptor.id,\n        droppableId: destination.descriptor.id\n      }\n    }\n  };\n  return impact;\n};\n\nvar getDragImpact = ({\n  pageOffset,\n  draggable,\n  draggables,\n  droppables,\n  previousImpact,\n  viewport,\n  afterCritical\n}) => {\n  const pageBorderBox = offsetRectByPosition(draggable.page.borderBox, pageOffset);\n  const destinationId = getDroppableOver({\n    pageBorderBox,\n    draggable,\n    droppables\n  });\n  if (!destinationId) {\n    return noImpact;\n  }\n  const destination = droppables[destinationId];\n  const insideDestination = getDraggablesInsideDroppable(destination.descriptor.id, draggables);\n  const pageBorderBoxWithDroppableScroll = withDroppableScroll(destination, pageBorderBox);\n  return getCombineImpact({\n    pageBorderBoxWithDroppableScroll,\n    draggable,\n    previousImpact,\n    destination,\n    insideDestination,\n    afterCritical\n  }) || getReorderImpact({\n    pageBorderBoxWithDroppableScroll,\n    draggable,\n    destination,\n    insideDestination,\n    last: previousImpact.displaced,\n    viewport,\n    afterCritical\n  });\n};\n\nvar patchDroppableMap = (droppables, updated) => ({\n  ...droppables,\n  [updated.descriptor.id]: updated\n});\n\nconst clearUnusedPlaceholder = ({\n  previousImpact,\n  impact,\n  droppables\n}) => {\n  const last = whatIsDraggedOver(previousImpact);\n  const now = whatIsDraggedOver(impact);\n  if (!last) {\n    return droppables;\n  }\n  if (last === now) {\n    return droppables;\n  }\n  const lastDroppable = droppables[last];\n  if (!lastDroppable.subject.withPlaceholder) {\n    return droppables;\n  }\n  const updated = removePlaceholder(lastDroppable);\n  return patchDroppableMap(droppables, updated);\n};\nvar recomputePlaceholders = ({\n  draggable,\n  draggables,\n  droppables,\n  previousImpact,\n  impact\n}) => {\n  const cleaned = clearUnusedPlaceholder({\n    previousImpact,\n    impact,\n    droppables\n  });\n  const isOver = whatIsDraggedOver(impact);\n  if (!isOver) {\n    return cleaned;\n  }\n  const droppable = droppables[isOver];\n  if (isHomeOf(draggable, droppable)) {\n    return cleaned;\n  }\n  if (droppable.subject.withPlaceholder) {\n    return cleaned;\n  }\n  const patched = addPlaceholder(droppable, draggable, draggables);\n  return patchDroppableMap(cleaned, patched);\n};\n\nvar update = ({\n  state,\n  clientSelection: forcedClientSelection,\n  dimensions: forcedDimensions,\n  viewport: forcedViewport,\n  impact: forcedImpact,\n  scrollJumpRequest\n}) => {\n  const viewport = forcedViewport || state.viewport;\n  const dimensions = forcedDimensions || state.dimensions;\n  const clientSelection = forcedClientSelection || state.current.client.selection;\n  const offset = subtract(clientSelection, state.initial.client.selection);\n  const client = {\n    offset,\n    selection: clientSelection,\n    borderBoxCenter: add(state.initial.client.borderBoxCenter, offset)\n  };\n  const page = {\n    selection: add(client.selection, viewport.scroll.current),\n    borderBoxCenter: add(client.borderBoxCenter, viewport.scroll.current),\n    offset: add(client.offset, viewport.scroll.diff.value)\n  };\n  const current = {\n    client,\n    page\n  };\n  if (state.phase === 'COLLECTING') {\n    return {\n      ...state,\n      dimensions,\n      viewport,\n      current\n    };\n  }\n  const draggable = dimensions.draggables[state.critical.draggable.id];\n  const newImpact = forcedImpact || getDragImpact({\n    pageOffset: page.offset,\n    draggable,\n    draggables: dimensions.draggables,\n    droppables: dimensions.droppables,\n    previousImpact: state.impact,\n    viewport,\n    afterCritical: state.afterCritical\n  });\n  const withUpdatedPlaceholders = recomputePlaceholders({\n    draggable,\n    impact: newImpact,\n    previousImpact: state.impact,\n    draggables: dimensions.draggables,\n    droppables: dimensions.droppables\n  });\n  const result = {\n    ...state,\n    current,\n    dimensions: {\n      draggables: dimensions.draggables,\n      droppables: withUpdatedPlaceholders\n    },\n    impact: newImpact,\n    viewport,\n    scrollJumpRequest: scrollJumpRequest || null,\n    forceShouldAnimate: scrollJumpRequest ? false : null\n  };\n  return result;\n};\n\nfunction getDraggables(ids, draggables) {\n  return ids.map(id => draggables[id]);\n}\nvar recompute = ({\n  impact,\n  viewport,\n  draggables,\n  destination,\n  forceShouldAnimate\n}) => {\n  const last = impact.displaced;\n  const afterDragging = getDraggables(last.all, draggables);\n  const displaced = getDisplacementGroups({\n    afterDragging,\n    destination,\n    displacedBy: impact.displacedBy,\n    viewport: viewport.frame,\n    forceShouldAnimate,\n    last\n  });\n  return {\n    ...impact,\n    displaced\n  };\n};\n\nvar getClientBorderBoxCenter = ({\n  impact,\n  draggable,\n  droppable,\n  draggables,\n  viewport,\n  afterCritical\n}) => {\n  const pageBorderBoxCenter = getPageBorderBoxCenterFromImpact({\n    impact,\n    draggable,\n    draggables,\n    droppable,\n    afterCritical\n  });\n  return getClientFromPageBorderBoxCenter({\n    pageBorderBoxCenter,\n    draggable,\n    viewport\n  });\n};\n\nvar refreshSnap = ({\n  state,\n  dimensions: forcedDimensions,\n  viewport: forcedViewport\n}) => {\n  !(state.movementMode === 'SNAP') ? process.env.NODE_ENV !== \"production\" ? invariant() : invariant() : void 0;\n  const needsVisibilityCheck = state.impact;\n  const viewport = forcedViewport || state.viewport;\n  const dimensions = forcedDimensions || state.dimensions;\n  const {\n    draggables,\n    droppables\n  } = dimensions;\n  const draggable = draggables[state.critical.draggable.id];\n  const isOver = whatIsDraggedOver(needsVisibilityCheck);\n  !isOver ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Must be over a destination in SNAP movement mode') : invariant() : void 0;\n  const destination = droppables[isOver];\n  const impact = recompute({\n    impact: needsVisibilityCheck,\n    viewport,\n    destination,\n    draggables\n  });\n  const clientSelection = getClientBorderBoxCenter({\n    impact,\n    draggable,\n    droppable: destination,\n    draggables,\n    viewport,\n    afterCritical: state.afterCritical\n  });\n  return update({\n    impact,\n    clientSelection,\n    state,\n    dimensions,\n    viewport\n  });\n};\n\nvar getHomeLocation = descriptor => ({\n  index: descriptor.index,\n  droppableId: descriptor.droppableId\n});\n\nvar getLiftEffect = ({\n  draggable,\n  home,\n  draggables,\n  viewport\n}) => {\n  const displacedBy = getDisplacedBy(home.axis, draggable.displaceBy);\n  const insideHome = getDraggablesInsideDroppable(home.descriptor.id, draggables);\n  const rawIndex = insideHome.indexOf(draggable);\n  !(rawIndex !== -1) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected draggable to be inside home list') : invariant() : void 0;\n  const afterDragging = insideHome.slice(rawIndex + 1);\n  const effected = afterDragging.reduce((previous, item) => {\n    previous[item.descriptor.id] = true;\n    return previous;\n  }, {});\n  const afterCritical = {\n    inVirtualList: home.descriptor.mode === 'virtual',\n    displacedBy,\n    effected\n  };\n  const displaced = getDisplacementGroups({\n    afterDragging,\n    destination: home,\n    displacedBy,\n    last: null,\n    viewport: viewport.frame,\n    forceShouldAnimate: false\n  });\n  const impact = {\n    displaced,\n    displacedBy,\n    at: {\n      type: 'REORDER',\n      destination: getHomeLocation(draggable.descriptor)\n    }\n  };\n  return {\n    impact,\n    afterCritical\n  };\n};\n\nvar patchDimensionMap = (dimensions, updated) => ({\n  draggables: dimensions.draggables,\n  droppables: patchDroppableMap(dimensions.droppables, updated)\n});\n\nconst start = key => {\n  if (process.env.NODE_ENV !== 'production') {\n    {\n      return;\n    }\n  }\n};\nconst finish = key => {\n  if (process.env.NODE_ENV !== 'production') {\n    {\n      return;\n    }\n  }\n};\n\nvar offsetDraggable = ({\n  draggable,\n  offset: offset$1,\n  initialWindowScroll\n}) => {\n  const client = offset(draggable.client, offset$1);\n  const page = withScroll(client, initialWindowScroll);\n  const moved = {\n    ...draggable,\n    placeholder: {\n      ...draggable.placeholder,\n      client\n    },\n    client,\n    page\n  };\n  return moved;\n};\n\nvar getFrame = droppable => {\n  const frame = droppable.frame;\n  !frame ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected Droppable to have a frame') : invariant() : void 0;\n  return frame;\n};\n\nvar adjustAdditionsForScrollChanges = ({\n  additions,\n  updatedDroppables,\n  viewport\n}) => {\n  const windowScrollChange = viewport.scroll.diff.value;\n  return additions.map(draggable => {\n    const droppableId = draggable.descriptor.droppableId;\n    const modified = updatedDroppables[droppableId];\n    const frame = getFrame(modified);\n    const droppableScrollChange = frame.scroll.diff.value;\n    const totalChange = add(windowScrollChange, droppableScrollChange);\n    const moved = offsetDraggable({\n      draggable,\n      offset: totalChange,\n      initialWindowScroll: viewport.scroll.initial\n    });\n    return moved;\n  });\n};\n\nvar publishWhileDraggingInVirtual = ({\n  state,\n  published\n}) => {\n  start();\n  const withScrollChange = published.modified.map(update => {\n    const existing = state.dimensions.droppables[update.droppableId];\n    const scrolled = scrollDroppable(existing, update.scroll);\n    return scrolled;\n  });\n  const droppables = {\n    ...state.dimensions.droppables,\n    ...toDroppableMap(withScrollChange)\n  };\n  const updatedAdditions = toDraggableMap(adjustAdditionsForScrollChanges({\n    additions: published.additions,\n    updatedDroppables: droppables,\n    viewport: state.viewport\n  }));\n  const draggables = {\n    ...state.dimensions.draggables,\n    ...updatedAdditions\n  };\n  published.removals.forEach(id => {\n    delete draggables[id];\n  });\n  const dimensions = {\n    droppables,\n    draggables\n  };\n  const wasOverId = whatIsDraggedOver(state.impact);\n  const wasOver = wasOverId ? dimensions.droppables[wasOverId] : null;\n  const draggable = dimensions.draggables[state.critical.draggable.id];\n  const home = dimensions.droppables[state.critical.droppable.id];\n  const {\n    impact: onLiftImpact,\n    afterCritical\n  } = getLiftEffect({\n    draggable,\n    home,\n    draggables,\n    viewport: state.viewport\n  });\n  const previousImpact = wasOver && wasOver.isCombineEnabled ? state.impact : onLiftImpact;\n  const impact = getDragImpact({\n    pageOffset: state.current.page.offset,\n    draggable: dimensions.draggables[state.critical.draggable.id],\n    draggables: dimensions.draggables,\n    droppables: dimensions.droppables,\n    previousImpact,\n    viewport: state.viewport,\n    afterCritical\n  });\n  finish();\n  const draggingState = {\n    ...state,\n    phase: 'DRAGGING',\n    impact,\n    onLiftImpact,\n    dimensions,\n    afterCritical,\n    forceShouldAnimate: false\n  };\n  if (state.phase === 'COLLECTING') {\n    return draggingState;\n  }\n  const dropPending = {\n    ...draggingState,\n    phase: 'DROP_PENDING',\n    reason: state.reason,\n    isWaiting: false\n  };\n  return dropPending;\n};\n\nconst isSnapping = state => state.movementMode === 'SNAP';\nconst postDroppableChange = (state, updated, isEnabledChanging) => {\n  const dimensions = patchDimensionMap(state.dimensions, updated);\n  if (!isSnapping(state) || isEnabledChanging) {\n    return update({\n      state,\n      dimensions\n    });\n  }\n  return refreshSnap({\n    state,\n    dimensions\n  });\n};\nfunction removeScrollJumpRequest(state) {\n  if (state.isDragging && state.movementMode === 'SNAP') {\n    return {\n      ...state,\n      scrollJumpRequest: null\n    };\n  }\n  return state;\n}\nconst idle$2 = {\n  phase: 'IDLE',\n  completed: null,\n  shouldFlush: false\n};\nvar reducer = (state = idle$2, action) => {\n  if (action.type === 'FLUSH') {\n    return {\n      ...idle$2,\n      shouldFlush: true\n    };\n  }\n  if (action.type === 'INITIAL_PUBLISH') {\n    !(state.phase === 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'INITIAL_PUBLISH must come after a IDLE phase') : invariant() : void 0;\n    const {\n      critical,\n      clientSelection,\n      viewport,\n      dimensions,\n      movementMode\n    } = action.payload;\n    const draggable = dimensions.draggables[critical.draggable.id];\n    const home = dimensions.droppables[critical.droppable.id];\n    const client = {\n      selection: clientSelection,\n      borderBoxCenter: draggable.client.borderBox.center,\n      offset: origin\n    };\n    const initial = {\n      client,\n      page: {\n        selection: add(client.selection, viewport.scroll.initial),\n        borderBoxCenter: add(client.selection, viewport.scroll.initial),\n        offset: add(client.selection, viewport.scroll.diff.value)\n      }\n    };\n    const isWindowScrollAllowed = toDroppableList(dimensions.droppables).every(item => !item.isFixedOnPage);\n    const {\n      impact,\n      afterCritical\n    } = getLiftEffect({\n      draggable,\n      home,\n      draggables: dimensions.draggables,\n      viewport\n    });\n    const result = {\n      phase: 'DRAGGING',\n      isDragging: true,\n      critical,\n      movementMode,\n      dimensions,\n      initial,\n      current: initial,\n      isWindowScrollAllowed,\n      impact,\n      afterCritical,\n      onLiftImpact: impact,\n      viewport,\n      scrollJumpRequest: null,\n      forceShouldAnimate: null\n    };\n    return result;\n  }\n  if (action.type === 'COLLECTION_STARTING') {\n    if (state.phase === 'COLLECTING' || state.phase === 'DROP_PENDING') {\n      return state;\n    }\n    !(state.phase === 'DRAGGING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Collection cannot start from phase ${state.phase}`) : invariant() : void 0;\n    const result = {\n      ...state,\n      phase: 'COLLECTING'\n    };\n    return result;\n  }\n  if (action.type === 'PUBLISH_WHILE_DRAGGING') {\n    !(state.phase === 'COLLECTING' || state.phase === 'DROP_PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Unexpected ${action.type} received in phase ${state.phase}`) : invariant() : void 0;\n    return publishWhileDraggingInVirtual({\n      state,\n      published: action.payload\n    });\n  }\n  if (action.type === 'MOVE') {\n    if (state.phase === 'DROP_PENDING') {\n      return state;\n    }\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `${action.type} not permitted in phase ${state.phase}`) : invariant() : void 0;\n    const {\n      client: clientSelection\n    } = action.payload;\n    if (isEqual$1(clientSelection, state.current.client.selection)) {\n      return state;\n    }\n    return update({\n      state,\n      clientSelection,\n      impact: isSnapping(state) ? state.impact : null\n    });\n  }\n  if (action.type === 'UPDATE_DROPPABLE_SCROLL') {\n    if (state.phase === 'DROP_PENDING') {\n      return removeScrollJumpRequest(state);\n    }\n    if (state.phase === 'COLLECTING') {\n      return removeScrollJumpRequest(state);\n    }\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `${action.type} not permitted in phase ${state.phase}`) : invariant() : void 0;\n    const {\n      id,\n      newScroll\n    } = action.payload;\n    const target = state.dimensions.droppables[id];\n    if (!target) {\n      return state;\n    }\n    const scrolled = scrollDroppable(target, newScroll);\n    return postDroppableChange(state, scrolled, false);\n  }\n  if (action.type === 'UPDATE_DROPPABLE_IS_ENABLED') {\n    if (state.phase === 'DROP_PENDING') {\n      return state;\n    }\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Attempting to move in an unsupported phase ${state.phase}`) : invariant() : void 0;\n    const {\n      id,\n      isEnabled\n    } = action.payload;\n    const target = state.dimensions.droppables[id];\n    !target ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot find Droppable[id: ${id}] to toggle its enabled state`) : invariant() : void 0;\n    !(target.isEnabled !== isEnabled) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Trying to set droppable isEnabled to ${String(isEnabled)}\n      but it is already ${String(target.isEnabled)}`) : invariant() : void 0;\n    const updated = {\n      ...target,\n      isEnabled\n    };\n    return postDroppableChange(state, updated, true);\n  }\n  if (action.type === 'UPDATE_DROPPABLE_IS_COMBINE_ENABLED') {\n    if (state.phase === 'DROP_PENDING') {\n      return state;\n    }\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Attempting to move in an unsupported phase ${state.phase}`) : invariant() : void 0;\n    const {\n      id,\n      isCombineEnabled\n    } = action.payload;\n    const target = state.dimensions.droppables[id];\n    !target ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot find Droppable[id: ${id}] to toggle its isCombineEnabled state`) : invariant() : void 0;\n    !(target.isCombineEnabled !== isCombineEnabled) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Trying to set droppable isCombineEnabled to ${String(isCombineEnabled)}\n      but it is already ${String(target.isCombineEnabled)}`) : invariant() : void 0;\n    const updated = {\n      ...target,\n      isCombineEnabled\n    };\n    return postDroppableChange(state, updated, true);\n  }\n  if (action.type === 'MOVE_BY_WINDOW_SCROLL') {\n    if (state.phase === 'DROP_PENDING' || state.phase === 'DROP_ANIMATING') {\n      return state;\n    }\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot move by window in phase ${state.phase}`) : invariant() : void 0;\n    !state.isWindowScrollAllowed ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Window scrolling is currently not supported for fixed lists') : invariant() : void 0;\n    const newScroll = action.payload.newScroll;\n    if (isEqual$1(state.viewport.scroll.current, newScroll)) {\n      return removeScrollJumpRequest(state);\n    }\n    const viewport = scrollViewport(state.viewport, newScroll);\n    if (isSnapping(state)) {\n      return refreshSnap({\n        state,\n        viewport\n      });\n    }\n    return update({\n      state,\n      viewport\n    });\n  }\n  if (action.type === 'UPDATE_VIEWPORT_MAX_SCROLL') {\n    if (!isMovementAllowed(state)) {\n      return state;\n    }\n    const maxScroll = action.payload.maxScroll;\n    if (isEqual$1(maxScroll, state.viewport.scroll.max)) {\n      return state;\n    }\n    const withMaxScroll = {\n      ...state.viewport,\n      scroll: {\n        ...state.viewport.scroll,\n        max: maxScroll\n      }\n    };\n    return {\n      ...state,\n      viewport: withMaxScroll\n    };\n  }\n  if (action.type === 'MOVE_UP' || action.type === 'MOVE_DOWN' || action.type === 'MOVE_LEFT' || action.type === 'MOVE_RIGHT') {\n    if (state.phase === 'COLLECTING' || state.phase === 'DROP_PENDING') {\n      return state;\n    }\n    !(state.phase === 'DRAGGING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, `${action.type} received while not in DRAGGING phase`) : invariant() : void 0;\n    const result = moveInDirection({\n      state,\n      type: action.type\n    });\n    if (!result) {\n      return state;\n    }\n    return update({\n      state,\n      impact: result.impact,\n      clientSelection: result.clientSelection,\n      scrollJumpRequest: result.scrollJumpRequest\n    });\n  }\n  if (action.type === 'DROP_PENDING') {\n    const reason = action.payload.reason;\n    !(state.phase === 'COLLECTING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Can only move into the DROP_PENDING phase from the COLLECTING phase') : invariant() : void 0;\n    const newState = {\n      ...state,\n      phase: 'DROP_PENDING',\n      isWaiting: true,\n      reason\n    };\n    return newState;\n  }\n  if (action.type === 'DROP_ANIMATE') {\n    const {\n      completed,\n      dropDuration,\n      newHomeClientOffset\n    } = action.payload;\n    !(state.phase === 'DRAGGING' || state.phase === 'DROP_PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot animate drop from phase ${state.phase}`) : invariant() : void 0;\n    const result = {\n      phase: 'DROP_ANIMATING',\n      completed,\n      dropDuration,\n      newHomeClientOffset,\n      dimensions: state.dimensions\n    };\n    return result;\n  }\n  if (action.type === 'DROP_COMPLETE') {\n    const {\n      completed\n    } = action.payload;\n    return {\n      phase: 'IDLE',\n      completed,\n      shouldFlush: false\n    };\n  }\n  return state;\n};\n\nfunction guard(action, predicate) {\n  return action instanceof Object && 'type' in action && action.type === predicate;\n}\nconst beforeInitialCapture = args => ({\n  type: 'BEFORE_INITIAL_CAPTURE',\n  payload: args\n});\nconst lift$1 = args => ({\n  type: 'LIFT',\n  payload: args\n});\nconst initialPublish = args => ({\n  type: 'INITIAL_PUBLISH',\n  payload: args\n});\nconst publishWhileDragging = args => ({\n  type: 'PUBLISH_WHILE_DRAGGING',\n  payload: args\n});\nconst collectionStarting = () => ({\n  type: 'COLLECTION_STARTING',\n  payload: null\n});\nconst updateDroppableScroll = args => ({\n  type: 'UPDATE_DROPPABLE_SCROLL',\n  payload: args\n});\nconst updateDroppableIsEnabled = args => ({\n  type: 'UPDATE_DROPPABLE_IS_ENABLED',\n  payload: args\n});\nconst updateDroppableIsCombineEnabled = args => ({\n  type: 'UPDATE_DROPPABLE_IS_COMBINE_ENABLED',\n  payload: args\n});\nconst move = args => ({\n  type: 'MOVE',\n  payload: args\n});\nconst moveByWindowScroll = args => ({\n  type: 'MOVE_BY_WINDOW_SCROLL',\n  payload: args\n});\nconst updateViewportMaxScroll = args => ({\n  type: 'UPDATE_VIEWPORT_MAX_SCROLL',\n  payload: args\n});\nconst moveUp = () => ({\n  type: 'MOVE_UP',\n  payload: null\n});\nconst moveDown = () => ({\n  type: 'MOVE_DOWN',\n  payload: null\n});\nconst moveRight = () => ({\n  type: 'MOVE_RIGHT',\n  payload: null\n});\nconst moveLeft = () => ({\n  type: 'MOVE_LEFT',\n  payload: null\n});\nconst flush = () => ({\n  type: 'FLUSH',\n  payload: null\n});\nconst animateDrop = args => ({\n  type: 'DROP_ANIMATE',\n  payload: args\n});\nconst completeDrop = args => ({\n  type: 'DROP_COMPLETE',\n  payload: args\n});\nconst drop = args => ({\n  type: 'DROP',\n  payload: args\n});\nconst dropPending = args => ({\n  type: 'DROP_PENDING',\n  payload: args\n});\nconst dropAnimationFinished = () => ({\n  type: 'DROP_ANIMATION_FINISHED',\n  payload: null\n});\n\nfunction checkIndexes(insideDestination) {\n  if (insideDestination.length <= 1) {\n    return;\n  }\n  const indexes = insideDestination.map(d => d.descriptor.index);\n  const errors = {};\n  for (let i = 1; i < indexes.length; i++) {\n    const current = indexes[i];\n    const previous = indexes[i - 1];\n    if (current !== previous + 1) {\n      errors[current] = true;\n    }\n  }\n  if (!Object.keys(errors).length) {\n    return;\n  }\n  const formatted = indexes.map(index => {\n    const hasError = Boolean(errors[index]);\n    return hasError ? `[🔥${index}]` : `${index}`;\n  }).join(', ');\n  process.env.NODE_ENV !== \"production\" ? warning(`\n    Detected non-consecutive <Draggable /> indexes.\n\n    (This can cause unexpected bugs)\n\n    ${formatted}\n  `) : void 0;\n}\nfunction validateDimensions(critical, dimensions) {\n  if (process.env.NODE_ENV !== 'production') {\n    const insideDestination = getDraggablesInsideDroppable(critical.droppable.id, dimensions.draggables);\n    checkIndexes(insideDestination);\n  }\n}\n\nvar lift = marshal => ({\n  getState,\n  dispatch\n}) => next => action => {\n  if (!guard(action, 'LIFT')) {\n    next(action);\n    return;\n  }\n  const {\n    id,\n    clientSelection,\n    movementMode\n  } = action.payload;\n  const initial = getState();\n  if (initial.phase === 'DROP_ANIMATING') {\n    dispatch(completeDrop({\n      completed: initial.completed\n    }));\n  }\n  !(getState().phase === 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Unexpected phase to start a drag') : invariant() : void 0;\n  dispatch(flush());\n  dispatch(beforeInitialCapture({\n    draggableId: id,\n    movementMode\n  }));\n  const scrollOptions = {\n    shouldPublishImmediately: movementMode === 'SNAP'\n  };\n  const request = {\n    draggableId: id,\n    scrollOptions\n  };\n  const {\n    critical,\n    dimensions,\n    viewport\n  } = marshal.startPublishing(request);\n  validateDimensions(critical, dimensions);\n  dispatch(initialPublish({\n    critical,\n    dimensions,\n    clientSelection,\n    movementMode,\n    viewport\n  }));\n};\n\nvar style = marshal => () => next => action => {\n  if (guard(action, 'INITIAL_PUBLISH')) {\n    marshal.dragging();\n  }\n  if (guard(action, 'DROP_ANIMATE')) {\n    marshal.dropping(action.payload.completed.result.reason);\n  }\n  if (guard(action, 'FLUSH') || guard(action, 'DROP_COMPLETE')) {\n    marshal.resting();\n  }\n  next(action);\n};\n\nconst curves = {\n  outOfTheWay: 'cubic-bezier(0.2, 0, 0, 1)',\n  drop: 'cubic-bezier(.2,1,.1,1)'\n};\nconst combine = {\n  opacity: {\n    drop: 0,\n    combining: 0.7\n  },\n  scale: {\n    drop: 0.75\n  }\n};\nconst timings = {\n  outOfTheWay: 0.2,\n  minDropTime: 0.33,\n  maxDropTime: 0.55\n};\nconst outOfTheWayTiming = `${timings.outOfTheWay}s ${curves.outOfTheWay}`;\nconst transitions = {\n  fluid: `opacity ${outOfTheWayTiming}`,\n  snap: `transform ${outOfTheWayTiming}, opacity ${outOfTheWayTiming}`,\n  drop: duration => {\n    const timing = `${duration}s ${curves.drop}`;\n    return `transform ${timing}, opacity ${timing}`;\n  },\n  outOfTheWay: `transform ${outOfTheWayTiming}`,\n  placeholder: `height ${outOfTheWayTiming}, width ${outOfTheWayTiming}, margin ${outOfTheWayTiming}`\n};\nconst moveTo = offset => isEqual$1(offset, origin) ? undefined : `translate(${offset.x}px, ${offset.y}px)`;\nconst transforms = {\n  moveTo,\n  drop: (offset, isCombining) => {\n    const translate = moveTo(offset);\n    if (!translate) {\n      return undefined;\n    }\n    if (!isCombining) {\n      return translate;\n    }\n    return `${translate} scale(${combine.scale.drop})`;\n  }\n};\n\nconst {\n  minDropTime,\n  maxDropTime\n} = timings;\nconst dropTimeRange = maxDropTime - minDropTime;\nconst maxDropTimeAtDistance = 1500;\nconst cancelDropModifier = 0.6;\nvar getDropDuration = ({\n  current,\n  destination,\n  reason\n}) => {\n  const distance$1 = distance(current, destination);\n  if (distance$1 <= 0) {\n    return minDropTime;\n  }\n  if (distance$1 >= maxDropTimeAtDistance) {\n    return maxDropTime;\n  }\n  const percentage = distance$1 / maxDropTimeAtDistance;\n  const duration = minDropTime + dropTimeRange * percentage;\n  const withDuration = reason === 'CANCEL' ? duration * cancelDropModifier : duration;\n  return Number(withDuration.toFixed(2));\n};\n\nvar getNewHomeClientOffset = ({\n  impact,\n  draggable,\n  dimensions,\n  viewport,\n  afterCritical\n}) => {\n  const {\n    draggables,\n    droppables\n  } = dimensions;\n  const droppableId = whatIsDraggedOver(impact);\n  const destination = droppableId ? droppables[droppableId] : null;\n  const home = droppables[draggable.descriptor.droppableId];\n  const newClientCenter = getClientBorderBoxCenter({\n    impact,\n    draggable,\n    draggables,\n    afterCritical,\n    droppable: destination || home,\n    viewport\n  });\n  const offset = subtract(newClientCenter, draggable.client.borderBox.center);\n  return offset;\n};\n\nvar getDropImpact = ({\n  draggables,\n  reason,\n  lastImpact,\n  home,\n  viewport,\n  onLiftImpact\n}) => {\n  if (!lastImpact.at || reason !== 'DROP') {\n    const recomputedHomeImpact = recompute({\n      draggables,\n      impact: onLiftImpact,\n      destination: home,\n      viewport,\n      forceShouldAnimate: true\n    });\n    return {\n      impact: recomputedHomeImpact,\n      didDropInsideDroppable: false\n    };\n  }\n  if (lastImpact.at.type === 'REORDER') {\n    return {\n      impact: lastImpact,\n      didDropInsideDroppable: true\n    };\n  }\n  const withoutMovement = {\n    ...lastImpact,\n    displaced: emptyGroups\n  };\n  return {\n    impact: withoutMovement,\n    didDropInsideDroppable: true\n  };\n};\n\nconst dropMiddleware = ({\n  getState,\n  dispatch\n}) => next => action => {\n  if (!guard(action, 'DROP')) {\n    next(action);\n    return;\n  }\n  const state = getState();\n  const reason = action.payload.reason;\n  if (state.phase === 'COLLECTING') {\n    dispatch(dropPending({\n      reason\n    }));\n    return;\n  }\n  if (state.phase === 'IDLE') {\n    return;\n  }\n  const isWaitingForDrop = state.phase === 'DROP_PENDING' && state.isWaiting;\n  !!isWaitingForDrop ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'A DROP action occurred while DROP_PENDING and still waiting') : invariant() : void 0;\n  !(state.phase === 'DRAGGING' || state.phase === 'DROP_PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot drop in phase: ${state.phase}`) : invariant() : void 0;\n  const critical = state.critical;\n  const dimensions = state.dimensions;\n  const draggable = dimensions.draggables[state.critical.draggable.id];\n  const {\n    impact,\n    didDropInsideDroppable\n  } = getDropImpact({\n    reason,\n    lastImpact: state.impact,\n    afterCritical: state.afterCritical,\n    onLiftImpact: state.onLiftImpact,\n    home: state.dimensions.droppables[state.critical.droppable.id],\n    viewport: state.viewport,\n    draggables: state.dimensions.draggables\n  });\n  const destination = didDropInsideDroppable ? tryGetDestination(impact) : null;\n  const combine = didDropInsideDroppable ? tryGetCombine(impact) : null;\n  const source = {\n    index: critical.draggable.index,\n    droppableId: critical.droppable.id\n  };\n  const result = {\n    draggableId: draggable.descriptor.id,\n    type: draggable.descriptor.type,\n    source,\n    reason,\n    mode: state.movementMode,\n    destination,\n    combine\n  };\n  const newHomeClientOffset = getNewHomeClientOffset({\n    impact,\n    draggable,\n    dimensions,\n    viewport: state.viewport,\n    afterCritical: state.afterCritical\n  });\n  const completed = {\n    critical: state.critical,\n    afterCritical: state.afterCritical,\n    result,\n    impact\n  };\n  const isAnimationRequired = !isEqual$1(state.current.client.offset, newHomeClientOffset) || Boolean(result.combine);\n  if (!isAnimationRequired) {\n    dispatch(completeDrop({\n      completed\n    }));\n    return;\n  }\n  const dropDuration = getDropDuration({\n    current: state.current.client.offset,\n    destination: newHomeClientOffset,\n    reason\n  });\n  const args = {\n    newHomeClientOffset,\n    dropDuration,\n    completed\n  };\n  dispatch(animateDrop(args));\n};\n\nvar getWindowScroll = () => ({\n  x: window.pageXOffset,\n  y: window.pageYOffset\n});\n\nfunction getWindowScrollBinding(update) {\n  return {\n    eventName: 'scroll',\n    options: {\n      passive: true,\n      capture: false\n    },\n    fn: event => {\n      if (event.target !== window && event.target !== window.document) {\n        return;\n      }\n      update();\n    }\n  };\n}\nfunction getScrollListener({\n  onWindowScroll\n}) {\n  function updateScroll() {\n    onWindowScroll(getWindowScroll());\n  }\n  const scheduled = rafSchd(updateScroll);\n  const binding = getWindowScrollBinding(scheduled);\n  let unbind = noop$2;\n  function isActive() {\n    return unbind !== noop$2;\n  }\n  function start() {\n    !!isActive() ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot start scroll listener when already active') : invariant() : void 0;\n    unbind = bindEvents(window, [binding]);\n  }\n  function stop() {\n    !isActive() ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot stop scroll listener when not active') : invariant() : void 0;\n    scheduled.cancel();\n    unbind();\n    unbind = noop$2;\n  }\n  return {\n    start,\n    stop,\n    isActive\n  };\n}\n\nconst shouldStop$1 = action => guard(action, 'DROP_COMPLETE') || guard(action, 'DROP_ANIMATE') || guard(action, 'FLUSH');\nconst scrollListener = store => {\n  const listener = getScrollListener({\n    onWindowScroll: newScroll => {\n      store.dispatch(moveByWindowScroll({\n        newScroll\n      }));\n    }\n  });\n  return next => action => {\n    if (!listener.isActive() && guard(action, 'INITIAL_PUBLISH')) {\n      listener.start();\n    }\n    if (listener.isActive() && shouldStop$1(action)) {\n      listener.stop();\n    }\n    next(action);\n  };\n};\n\nvar getExpiringAnnounce = announce => {\n  let wasCalled = false;\n  let isExpired = false;\n  const timeoutId = setTimeout(() => {\n    isExpired = true;\n  });\n  const result = message => {\n    if (wasCalled) {\n      process.env.NODE_ENV !== \"production\" ? warning('Announcement already made. Not making a second announcement') : void 0;\n      return;\n    }\n    if (isExpired) {\n      process.env.NODE_ENV !== \"production\" ? warning(`\n        Announcements cannot be made asynchronously.\n        Default message has already been announced.\n      `) : void 0;\n      return;\n    }\n    wasCalled = true;\n    announce(message);\n    clearTimeout(timeoutId);\n  };\n  result.wasCalled = () => wasCalled;\n  return result;\n};\n\nvar getAsyncMarshal = () => {\n  const entries = [];\n  const execute = timerId => {\n    const index = entries.findIndex(item => item.timerId === timerId);\n    !(index !== -1) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find timer') : invariant() : void 0;\n    const [entry] = entries.splice(index, 1);\n    entry.callback();\n  };\n  const add = fn => {\n    const timerId = setTimeout(() => execute(timerId));\n    const entry = {\n      timerId,\n      callback: fn\n    };\n    entries.push(entry);\n  };\n  const flush = () => {\n    if (!entries.length) {\n      return;\n    }\n    const shallow = [...entries];\n    entries.length = 0;\n    shallow.forEach(entry => {\n      clearTimeout(entry.timerId);\n      entry.callback();\n    });\n  };\n  return {\n    add,\n    flush\n  };\n};\n\nconst areLocationsEqual = (first, second) => {\n  if (first == null && second == null) {\n    return true;\n  }\n  if (first == null || second == null) {\n    return false;\n  }\n  return first.droppableId === second.droppableId && first.index === second.index;\n};\nconst isCombineEqual = (first, second) => {\n  if (first == null && second == null) {\n    return true;\n  }\n  if (first == null || second == null) {\n    return false;\n  }\n  return first.draggableId === second.draggableId && first.droppableId === second.droppableId;\n};\nconst isCriticalEqual = (first, second) => {\n  if (first === second) {\n    return true;\n  }\n  const isDraggableEqual = first.draggable.id === second.draggable.id && first.draggable.droppableId === second.draggable.droppableId && first.draggable.type === second.draggable.type && first.draggable.index === second.draggable.index;\n  const isDroppableEqual = first.droppable.id === second.droppable.id && first.droppable.type === second.droppable.type;\n  return isDraggableEqual && isDroppableEqual;\n};\n\nconst withTimings = (key, fn) => {\n  start();\n  fn();\n  finish();\n};\nconst getDragStart = (critical, mode) => ({\n  draggableId: critical.draggable.id,\n  type: critical.droppable.type,\n  source: {\n    droppableId: critical.droppable.id,\n    index: critical.draggable.index\n  },\n  mode\n});\nfunction execute(responder, data, announce, getDefaultMessage) {\n  if (!responder) {\n    announce(getDefaultMessage(data));\n    return;\n  }\n  const willExpire = getExpiringAnnounce(announce);\n  const provided = {\n    announce: willExpire\n  };\n  responder(data, provided);\n  if (!willExpire.wasCalled()) {\n    announce(getDefaultMessage(data));\n  }\n}\nvar getPublisher = (getResponders, announce) => {\n  const asyncMarshal = getAsyncMarshal();\n  let dragging = null;\n  const beforeCapture = (draggableId, mode) => {\n    !!dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onBeforeCapture as a drag start has already been published') : invariant() : void 0;\n    withTimings('onBeforeCapture', () => {\n      const fn = getResponders().onBeforeCapture;\n      if (fn) {\n        const before = {\n          draggableId,\n          mode\n        };\n        fn(before);\n      }\n    });\n  };\n  const beforeStart = (critical, mode) => {\n    !!dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onBeforeDragStart as a drag start has already been published') : invariant() : void 0;\n    withTimings('onBeforeDragStart', () => {\n      const fn = getResponders().onBeforeDragStart;\n      if (fn) {\n        fn(getDragStart(critical, mode));\n      }\n    });\n  };\n  const start = (critical, mode) => {\n    !!dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onBeforeDragStart as a drag start has already been published') : invariant() : void 0;\n    const data = getDragStart(critical, mode);\n    dragging = {\n      mode,\n      lastCritical: critical,\n      lastLocation: data.source,\n      lastCombine: null\n    };\n    asyncMarshal.add(() => {\n      withTimings('onDragStart', () => execute(getResponders().onDragStart, data, announce, preset.onDragStart));\n    });\n  };\n  const update = (critical, impact) => {\n    const location = tryGetDestination(impact);\n    const combine = tryGetCombine(impact);\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onDragMove when onDragStart has not been called') : invariant() : void 0;\n    const hasCriticalChanged = !isCriticalEqual(critical, dragging.lastCritical);\n    if (hasCriticalChanged) {\n      dragging.lastCritical = critical;\n    }\n    const hasLocationChanged = !areLocationsEqual(dragging.lastLocation, location);\n    if (hasLocationChanged) {\n      dragging.lastLocation = location;\n    }\n    const hasGroupingChanged = !isCombineEqual(dragging.lastCombine, combine);\n    if (hasGroupingChanged) {\n      dragging.lastCombine = combine;\n    }\n    if (!hasCriticalChanged && !hasLocationChanged && !hasGroupingChanged) {\n      return;\n    }\n    const data = {\n      ...getDragStart(critical, dragging.mode),\n      combine,\n      destination: location\n    };\n    asyncMarshal.add(() => {\n      withTimings('onDragUpdate', () => execute(getResponders().onDragUpdate, data, announce, preset.onDragUpdate));\n    });\n  };\n  const flush = () => {\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Can only flush responders while dragging') : invariant() : void 0;\n    asyncMarshal.flush();\n  };\n  const drop = result => {\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onDragEnd when there is no matching onDragStart') : invariant() : void 0;\n    dragging = null;\n    withTimings('onDragEnd', () => execute(getResponders().onDragEnd, result, announce, preset.onDragEnd));\n  };\n  const abort = () => {\n    if (!dragging) {\n      return;\n    }\n    const result = {\n      ...getDragStart(dragging.lastCritical, dragging.mode),\n      combine: null,\n      destination: null,\n      reason: 'CANCEL'\n    };\n    drop(result);\n  };\n  return {\n    beforeCapture,\n    beforeStart,\n    start,\n    update,\n    flush,\n    drop,\n    abort\n  };\n};\n\nvar responders = (getResponders, announce) => {\n  const publisher = getPublisher(getResponders, announce);\n  return store => next => action => {\n    if (guard(action, 'BEFORE_INITIAL_CAPTURE')) {\n      publisher.beforeCapture(action.payload.draggableId, action.payload.movementMode);\n      return;\n    }\n    if (guard(action, 'INITIAL_PUBLISH')) {\n      const critical = action.payload.critical;\n      publisher.beforeStart(critical, action.payload.movementMode);\n      next(action);\n      publisher.start(critical, action.payload.movementMode);\n      return;\n    }\n    if (guard(action, 'DROP_COMPLETE')) {\n      const result = action.payload.completed.result;\n      publisher.flush();\n      next(action);\n      publisher.drop(result);\n      return;\n    }\n    next(action);\n    if (guard(action, 'FLUSH')) {\n      publisher.abort();\n      return;\n    }\n    const state = store.getState();\n    if (state.phase === 'DRAGGING') {\n      publisher.update(state.critical, state.impact);\n    }\n  };\n};\n\nconst dropAnimationFinishMiddleware = store => next => action => {\n  if (!guard(action, 'DROP_ANIMATION_FINISHED')) {\n    next(action);\n    return;\n  }\n  const state = store.getState();\n  !(state.phase === 'DROP_ANIMATING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot finish a drop animating when no drop is occurring') : invariant() : void 0;\n  store.dispatch(completeDrop({\n    completed: state.completed\n  }));\n};\n\nconst dropAnimationFlushOnScrollMiddleware = store => {\n  let unbind = null;\n  let frameId = null;\n  function clear() {\n    if (frameId) {\n      cancelAnimationFrame(frameId);\n      frameId = null;\n    }\n    if (unbind) {\n      unbind();\n      unbind = null;\n    }\n  }\n  return next => action => {\n    if (guard(action, 'FLUSH') || guard(action, 'DROP_COMPLETE') || guard(action, 'DROP_ANIMATION_FINISHED')) {\n      clear();\n    }\n    next(action);\n    if (!guard(action, 'DROP_ANIMATE')) {\n      return;\n    }\n    const binding = {\n      eventName: 'scroll',\n      options: {\n        capture: true,\n        passive: false,\n        once: true\n      },\n      fn: function flushDropAnimation() {\n        const state = store.getState();\n        if (state.phase === 'DROP_ANIMATING') {\n          store.dispatch(dropAnimationFinished());\n        }\n      }\n    };\n    frameId = requestAnimationFrame(() => {\n      frameId = null;\n      unbind = bindEvents(window, [binding]);\n    });\n  };\n};\n\nvar dimensionMarshalStopper = marshal => () => next => action => {\n  if (guard(action, 'DROP_COMPLETE') || guard(action, 'FLUSH') || guard(action, 'DROP_ANIMATE')) {\n    marshal.stopPublishing();\n  }\n  next(action);\n};\n\nvar focus = marshal => {\n  let isWatching = false;\n  return () => next => action => {\n    if (guard(action, 'INITIAL_PUBLISH')) {\n      isWatching = true;\n      marshal.tryRecordFocus(action.payload.critical.draggable.id);\n      next(action);\n      marshal.tryRestoreFocusRecorded();\n      return;\n    }\n    next(action);\n    if (!isWatching) {\n      return;\n    }\n    if (guard(action, 'FLUSH')) {\n      isWatching = false;\n      marshal.tryRestoreFocusRecorded();\n      return;\n    }\n    if (guard(action, 'DROP_COMPLETE')) {\n      isWatching = false;\n      const result = action.payload.completed.result;\n      if (result.combine) {\n        marshal.tryShiftRecord(result.draggableId, result.combine.draggableId);\n      }\n      marshal.tryRestoreFocusRecorded();\n    }\n  };\n};\n\nconst shouldStop = action => guard(action, 'DROP_COMPLETE') || guard(action, 'DROP_ANIMATE') || guard(action, 'FLUSH');\nvar autoScroll = autoScroller => store => next => action => {\n  if (shouldStop(action)) {\n    autoScroller.stop();\n    next(action);\n    return;\n  }\n  if (guard(action, 'INITIAL_PUBLISH')) {\n    next(action);\n    const state = store.getState();\n    !(state.phase === 'DRAGGING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected phase to be DRAGGING after INITIAL_PUBLISH') : invariant() : void 0;\n    autoScroller.start(state);\n    return;\n  }\n  next(action);\n  autoScroller.scroll(store.getState());\n};\n\nconst pendingDrop = store => next => action => {\n  next(action);\n  if (!guard(action, 'PUBLISH_WHILE_DRAGGING')) {\n    return;\n  }\n  const postActionState = store.getState();\n  if (postActionState.phase !== 'DROP_PENDING') {\n    return;\n  }\n  if (postActionState.isWaiting) {\n    return;\n  }\n  store.dispatch(drop({\n    reason: postActionState.reason\n  }));\n};\n\nconst composeEnhancers = process.env.NODE_ENV !== 'production' && typeof window !== 'undefined' && window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ ? window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__({\n  name: '@hello-pangea/dnd'\n}) : compose;\nvar createStore = ({\n  dimensionMarshal,\n  focusMarshal,\n  styleMarshal,\n  getResponders,\n  announce,\n  autoScroller\n}) => createStore$1(reducer, composeEnhancers(applyMiddleware(style(styleMarshal), dimensionMarshalStopper(dimensionMarshal), lift(dimensionMarshal), dropMiddleware, dropAnimationFinishMiddleware, dropAnimationFlushOnScrollMiddleware, pendingDrop, autoScroll(autoScroller), scrollListener, focus(focusMarshal), responders(getResponders, announce))));\n\nconst clean$1 = () => ({\n  additions: {},\n  removals: {},\n  modified: {}\n});\nfunction createPublisher({\n  registry,\n  callbacks\n}) {\n  let staging = clean$1();\n  let frameId = null;\n  const collect = () => {\n    if (frameId) {\n      return;\n    }\n    callbacks.collectionStarting();\n    frameId = requestAnimationFrame(() => {\n      frameId = null;\n      start();\n      const {\n        additions,\n        removals,\n        modified\n      } = staging;\n      const added = Object.keys(additions).map(id => registry.draggable.getById(id).getDimension(origin)).sort((a, b) => a.descriptor.index - b.descriptor.index);\n      const updated = Object.keys(modified).map(id => {\n        const entry = registry.droppable.getById(id);\n        const scroll = entry.callbacks.getScrollWhileDragging();\n        return {\n          droppableId: id,\n          scroll\n        };\n      });\n      const result = {\n        additions: added,\n        removals: Object.keys(removals),\n        modified: updated\n      };\n      staging = clean$1();\n      finish();\n      callbacks.publish(result);\n    });\n  };\n  const add = entry => {\n    const id = entry.descriptor.id;\n    staging.additions[id] = entry;\n    staging.modified[entry.descriptor.droppableId] = true;\n    if (staging.removals[id]) {\n      delete staging.removals[id];\n    }\n    collect();\n  };\n  const remove = entry => {\n    const descriptor = entry.descriptor;\n    staging.removals[descriptor.id] = true;\n    staging.modified[descriptor.droppableId] = true;\n    if (staging.additions[descriptor.id]) {\n      delete staging.additions[descriptor.id];\n    }\n    collect();\n  };\n  const stop = () => {\n    if (!frameId) {\n      return;\n    }\n    cancelAnimationFrame(frameId);\n    frameId = null;\n    staging = clean$1();\n  };\n  return {\n    add,\n    remove,\n    stop\n  };\n}\n\nvar getMaxScroll = ({\n  scrollHeight,\n  scrollWidth,\n  height,\n  width\n}) => {\n  const maxScroll = subtract({\n    x: scrollWidth,\n    y: scrollHeight\n  }, {\n    x: width,\n    y: height\n  });\n  const adjustedMaxScroll = {\n    x: Math.max(0, maxScroll.x),\n    y: Math.max(0, maxScroll.y)\n  };\n  return adjustedMaxScroll;\n};\n\nvar getDocumentElement = () => {\n  const doc = document.documentElement;\n  !doc ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot find document.documentElement') : invariant() : void 0;\n  return doc;\n};\n\nvar getMaxWindowScroll = () => {\n  const doc = getDocumentElement();\n  const maxScroll = getMaxScroll({\n    scrollHeight: doc.scrollHeight,\n    scrollWidth: doc.scrollWidth,\n    width: doc.clientWidth,\n    height: doc.clientHeight\n  });\n  return maxScroll;\n};\n\nvar getViewport = () => {\n  const scroll = getWindowScroll();\n  const maxScroll = getMaxWindowScroll();\n  const top = scroll.y;\n  const left = scroll.x;\n  const doc = getDocumentElement();\n  const width = doc.clientWidth;\n  const height = doc.clientHeight;\n  const right = left + width;\n  const bottom = top + height;\n  const frame = getRect({\n    top,\n    left,\n    right,\n    bottom\n  });\n  const viewport = {\n    frame,\n    scroll: {\n      initial: scroll,\n      current: scroll,\n      max: maxScroll,\n      diff: {\n        value: origin,\n        displacement: origin\n      }\n    }\n  };\n  return viewport;\n};\n\nvar getInitialPublish = ({\n  critical,\n  scrollOptions,\n  registry\n}) => {\n  start();\n  const viewport = getViewport();\n  const windowScroll = viewport.scroll.current;\n  const home = critical.droppable;\n  const droppables = registry.droppable.getAllByType(home.type).map(entry => entry.callbacks.getDimensionAndWatchScroll(windowScroll, scrollOptions));\n  const draggables = registry.draggable.getAllByType(critical.draggable.type).map(entry => entry.getDimension(windowScroll));\n  const dimensions = {\n    draggables: toDraggableMap(draggables),\n    droppables: toDroppableMap(droppables)\n  };\n  finish();\n  const result = {\n    dimensions,\n    critical,\n    viewport\n  };\n  return result;\n};\n\nfunction shouldPublishUpdate(registry, dragging, entry) {\n  if (entry.descriptor.id === dragging.id) {\n    return false;\n  }\n  if (entry.descriptor.type !== dragging.type) {\n    return false;\n  }\n  const home = registry.droppable.getById(entry.descriptor.droppableId);\n  if (home.descriptor.mode !== 'virtual') {\n    process.env.NODE_ENV !== \"production\" ? warning(`\n      You are attempting to add or remove a Draggable [id: ${entry.descriptor.id}]\n      while a drag is occurring. This is only supported for virtual lists.\n\n      See https://github.com/hello-pangea/dnd/blob/main/docs/patterns/virtual-lists.md\n    `) : void 0;\n    return false;\n  }\n  return true;\n}\nvar createDimensionMarshal = (registry, callbacks) => {\n  let collection = null;\n  const publisher = createPublisher({\n    callbacks: {\n      publish: callbacks.publishWhileDragging,\n      collectionStarting: callbacks.collectionStarting\n    },\n    registry\n  });\n  const updateDroppableIsEnabled = (id, isEnabled) => {\n    !registry.droppable.exists(id) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot update is enabled flag of Droppable ${id} as it is not registered`) : invariant() : void 0;\n    if (!collection) {\n      return;\n    }\n    callbacks.updateDroppableIsEnabled({\n      id,\n      isEnabled\n    });\n  };\n  const updateDroppableIsCombineEnabled = (id, isCombineEnabled) => {\n    if (!collection) {\n      return;\n    }\n    !registry.droppable.exists(id) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot update isCombineEnabled flag of Droppable ${id} as it is not registered`) : invariant() : void 0;\n    callbacks.updateDroppableIsCombineEnabled({\n      id,\n      isCombineEnabled\n    });\n  };\n  const updateDroppableScroll = (id, newScroll) => {\n    if (!collection) {\n      return;\n    }\n    !registry.droppable.exists(id) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot update the scroll on Droppable ${id} as it is not registered`) : invariant() : void 0;\n    callbacks.updateDroppableScroll({\n      id,\n      newScroll\n    });\n  };\n  const scrollDroppable = (id, change) => {\n    if (!collection) {\n      return;\n    }\n    registry.droppable.getById(id).callbacks.scroll(change);\n  };\n  const stopPublishing = () => {\n    if (!collection) {\n      return;\n    }\n    publisher.stop();\n    const home = collection.critical.droppable;\n    registry.droppable.getAllByType(home.type).forEach(entry => entry.callbacks.dragStopped());\n    collection.unsubscribe();\n    collection = null;\n  };\n  const subscriber = event => {\n    !collection ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Should only be subscribed when a collection is occurring') : invariant() : void 0;\n    const dragging = collection.critical.draggable;\n    if (event.type === 'ADDITION') {\n      if (shouldPublishUpdate(registry, dragging, event.value)) {\n        publisher.add(event.value);\n      }\n    }\n    if (event.type === 'REMOVAL') {\n      if (shouldPublishUpdate(registry, dragging, event.value)) {\n        publisher.remove(event.value);\n      }\n    }\n  };\n  const startPublishing = request => {\n    !!collection ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot start capturing critical dimensions as there is already a collection') : invariant() : void 0;\n    const entry = registry.draggable.getById(request.draggableId);\n    const home = registry.droppable.getById(entry.descriptor.droppableId);\n    const critical = {\n      draggable: entry.descriptor,\n      droppable: home.descriptor\n    };\n    const unsubscribe = registry.subscribe(subscriber);\n    collection = {\n      critical,\n      unsubscribe\n    };\n    return getInitialPublish({\n      critical,\n      registry,\n      scrollOptions: request.scrollOptions\n    });\n  };\n  const marshal = {\n    updateDroppableIsEnabled,\n    updateDroppableIsCombineEnabled,\n    scrollDroppable,\n    updateDroppableScroll,\n    startPublishing,\n    stopPublishing\n  };\n  return marshal;\n};\n\nvar canStartDrag = (state, id) => {\n  if (state.phase === 'IDLE') {\n    return true;\n  }\n  if (state.phase !== 'DROP_ANIMATING') {\n    return false;\n  }\n  if (state.completed.result.draggableId === id) {\n    return false;\n  }\n  return state.completed.result.reason === 'DROP';\n};\n\nvar scrollWindow = change => {\n  window.scrollBy(change.x, change.y);\n};\n\nconst getScrollableDroppables = memoizeOne(droppables => toDroppableList(droppables).filter(droppable => {\n  if (!droppable.isEnabled) {\n    return false;\n  }\n  if (!droppable.frame) {\n    return false;\n  }\n  return true;\n}));\nconst getScrollableDroppableOver = (target, droppables) => {\n  const maybe = getScrollableDroppables(droppables).find(droppable => {\n    !droppable.frame ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Invalid result') : invariant() : void 0;\n    return isPositionInFrame(droppable.frame.pageMarginBox)(target);\n  }) || null;\n  return maybe;\n};\nvar getBestScrollableDroppable = ({\n  center,\n  destination,\n  droppables\n}) => {\n  if (destination) {\n    const dimension = droppables[destination];\n    if (!dimension.frame) {\n      return null;\n    }\n    return dimension;\n  }\n  const dimension = getScrollableDroppableOver(center, droppables);\n  return dimension;\n};\n\nconst defaultAutoScrollerOptions = {\n  startFromPercentage: 0.25,\n  maxScrollAtPercentage: 0.05,\n  maxPixelScroll: 28,\n  ease: percentage => percentage ** 2,\n  durationDampening: {\n    stopDampeningAt: 1200,\n    accelerateAt: 360\n  },\n  disabled: false\n};\n\nvar getDistanceThresholds = (container, axis, getAutoScrollerOptions = () => defaultAutoScrollerOptions) => {\n  const autoScrollerOptions = getAutoScrollerOptions();\n  const startScrollingFrom = container[axis.size] * autoScrollerOptions.startFromPercentage;\n  const maxScrollValueAt = container[axis.size] * autoScrollerOptions.maxScrollAtPercentage;\n  const thresholds = {\n    startScrollingFrom,\n    maxScrollValueAt\n  };\n  return thresholds;\n};\n\nvar getPercentage = ({\n  startOfRange,\n  endOfRange,\n  current\n}) => {\n  const range = endOfRange - startOfRange;\n  if (range === 0) {\n    process.env.NODE_ENV !== \"production\" ? warning(`\n      Detected distance range of 0 in the fluid auto scroller\n      This is unexpected and would cause a divide by 0 issue.\n      Not allowing an auto scroll\n    `) : void 0;\n    return 0;\n  }\n  const currentInRange = current - startOfRange;\n  const percentage = currentInRange / range;\n  return percentage;\n};\n\nvar minScroll = 1;\n\nvar getValueFromDistance = (distanceToEdge, thresholds, getAutoScrollerOptions = () => defaultAutoScrollerOptions) => {\n  const autoScrollerOptions = getAutoScrollerOptions();\n  if (distanceToEdge > thresholds.startScrollingFrom) {\n    return 0;\n  }\n  if (distanceToEdge <= thresholds.maxScrollValueAt) {\n    return autoScrollerOptions.maxPixelScroll;\n  }\n  if (distanceToEdge === thresholds.startScrollingFrom) {\n    return minScroll;\n  }\n  const percentageFromMaxScrollValueAt = getPercentage({\n    startOfRange: thresholds.maxScrollValueAt,\n    endOfRange: thresholds.startScrollingFrom,\n    current: distanceToEdge\n  });\n  const percentageFromStartScrollingFrom = 1 - percentageFromMaxScrollValueAt;\n  const scroll = autoScrollerOptions.maxPixelScroll * autoScrollerOptions.ease(percentageFromStartScrollingFrom);\n  return Math.ceil(scroll);\n};\n\nvar dampenValueByTime = (proposedScroll, dragStartTime, getAutoScrollerOptions) => {\n  const autoScrollerOptions = getAutoScrollerOptions();\n  const accelerateAt = autoScrollerOptions.durationDampening.accelerateAt;\n  const stopAt = autoScrollerOptions.durationDampening.stopDampeningAt;\n  const startOfRange = dragStartTime;\n  const endOfRange = stopAt;\n  const now = Date.now();\n  const runTime = now - startOfRange;\n  if (runTime >= stopAt) {\n    return proposedScroll;\n  }\n  if (runTime < accelerateAt) {\n    return minScroll;\n  }\n  const betweenAccelerateAtAndStopAtPercentage = getPercentage({\n    startOfRange: accelerateAt,\n    endOfRange,\n    current: runTime\n  });\n  const scroll = proposedScroll * autoScrollerOptions.ease(betweenAccelerateAtAndStopAtPercentage);\n  return Math.ceil(scroll);\n};\n\nvar getValue = ({\n  distanceToEdge,\n  thresholds,\n  dragStartTime,\n  shouldUseTimeDampening,\n  getAutoScrollerOptions\n}) => {\n  const scroll = getValueFromDistance(distanceToEdge, thresholds, getAutoScrollerOptions);\n  if (scroll === 0) {\n    return 0;\n  }\n  if (!shouldUseTimeDampening) {\n    return scroll;\n  }\n  return Math.max(dampenValueByTime(scroll, dragStartTime, getAutoScrollerOptions), minScroll);\n};\n\nvar getScrollOnAxis = ({\n  container,\n  distanceToEdges,\n  dragStartTime,\n  axis,\n  shouldUseTimeDampening,\n  getAutoScrollerOptions\n}) => {\n  const thresholds = getDistanceThresholds(container, axis, getAutoScrollerOptions);\n  const isCloserToEnd = distanceToEdges[axis.end] < distanceToEdges[axis.start];\n  if (isCloserToEnd) {\n    return getValue({\n      distanceToEdge: distanceToEdges[axis.end],\n      thresholds,\n      dragStartTime,\n      shouldUseTimeDampening,\n      getAutoScrollerOptions\n    });\n  }\n  return -1 * getValue({\n    distanceToEdge: distanceToEdges[axis.start],\n    thresholds,\n    dragStartTime,\n    shouldUseTimeDampening,\n    getAutoScrollerOptions\n  });\n};\n\nvar adjustForSizeLimits = ({\n  container,\n  subject,\n  proposedScroll\n}) => {\n  const isTooBigVertically = subject.height > container.height;\n  const isTooBigHorizontally = subject.width > container.width;\n  if (!isTooBigHorizontally && !isTooBigVertically) {\n    return proposedScroll;\n  }\n  if (isTooBigHorizontally && isTooBigVertically) {\n    return null;\n  }\n  return {\n    x: isTooBigHorizontally ? 0 : proposedScroll.x,\n    y: isTooBigVertically ? 0 : proposedScroll.y\n  };\n};\n\nconst clean = apply(value => value === 0 ? 0 : value);\nvar getScroll$1 = ({\n  dragStartTime,\n  container,\n  subject,\n  center,\n  shouldUseTimeDampening,\n  getAutoScrollerOptions\n}) => {\n  const distanceToEdges = {\n    top: center.y - container.top,\n    right: container.right - center.x,\n    bottom: container.bottom - center.y,\n    left: center.x - container.left\n  };\n  const y = getScrollOnAxis({\n    container,\n    distanceToEdges,\n    dragStartTime,\n    axis: vertical,\n    shouldUseTimeDampening,\n    getAutoScrollerOptions\n  });\n  const x = getScrollOnAxis({\n    container,\n    distanceToEdges,\n    dragStartTime,\n    axis: horizontal,\n    shouldUseTimeDampening,\n    getAutoScrollerOptions\n  });\n  const required = clean({\n    x,\n    y\n  });\n  if (isEqual$1(required, origin)) {\n    return null;\n  }\n  const limited = adjustForSizeLimits({\n    container,\n    subject,\n    proposedScroll: required\n  });\n  if (!limited) {\n    return null;\n  }\n  return isEqual$1(limited, origin) ? null : limited;\n};\n\nconst smallestSigned = apply(value => {\n  if (value === 0) {\n    return 0;\n  }\n  return value > 0 ? 1 : -1;\n});\nconst getOverlap = (() => {\n  const getRemainder = (target, max) => {\n    if (target < 0) {\n      return target;\n    }\n    if (target > max) {\n      return target - max;\n    }\n    return 0;\n  };\n  return ({\n    current,\n    max,\n    change\n  }) => {\n    const targetScroll = add(current, change);\n    const overlap = {\n      x: getRemainder(targetScroll.x, max.x),\n      y: getRemainder(targetScroll.y, max.y)\n    };\n    if (isEqual$1(overlap, origin)) {\n      return null;\n    }\n    return overlap;\n  };\n})();\nconst canPartiallyScroll = ({\n  max: rawMax,\n  current,\n  change\n}) => {\n  const max = {\n    x: Math.max(current.x, rawMax.x),\n    y: Math.max(current.y, rawMax.y)\n  };\n  const smallestChange = smallestSigned(change);\n  const overlap = getOverlap({\n    max,\n    current,\n    change: smallestChange\n  });\n  if (!overlap) {\n    return true;\n  }\n  if (smallestChange.x !== 0 && overlap.x === 0) {\n    return true;\n  }\n  if (smallestChange.y !== 0 && overlap.y === 0) {\n    return true;\n  }\n  return false;\n};\nconst canScrollWindow = (viewport, change) => canPartiallyScroll({\n  current: viewport.scroll.current,\n  max: viewport.scroll.max,\n  change\n});\nconst getWindowOverlap = (viewport, change) => {\n  if (!canScrollWindow(viewport, change)) {\n    return null;\n  }\n  const max = viewport.scroll.max;\n  const current = viewport.scroll.current;\n  return getOverlap({\n    current,\n    max,\n    change\n  });\n};\nconst canScrollDroppable = (droppable, change) => {\n  const frame = droppable.frame;\n  if (!frame) {\n    return false;\n  }\n  return canPartiallyScroll({\n    current: frame.scroll.current,\n    max: frame.scroll.max,\n    change\n  });\n};\nconst getDroppableOverlap = (droppable, change) => {\n  const frame = droppable.frame;\n  if (!frame) {\n    return null;\n  }\n  if (!canScrollDroppable(droppable, change)) {\n    return null;\n  }\n  return getOverlap({\n    current: frame.scroll.current,\n    max: frame.scroll.max,\n    change\n  });\n};\n\nvar getWindowScrollChange = ({\n  viewport,\n  subject,\n  center,\n  dragStartTime,\n  shouldUseTimeDampening,\n  getAutoScrollerOptions\n}) => {\n  const scroll = getScroll$1({\n    dragStartTime,\n    container: viewport.frame,\n    subject,\n    center,\n    shouldUseTimeDampening,\n    getAutoScrollerOptions\n  });\n  return scroll && canScrollWindow(viewport, scroll) ? scroll : null;\n};\n\nvar getDroppableScrollChange = ({\n  droppable,\n  subject,\n  center,\n  dragStartTime,\n  shouldUseTimeDampening,\n  getAutoScrollerOptions\n}) => {\n  const frame = droppable.frame;\n  if (!frame) {\n    return null;\n  }\n  const scroll = getScroll$1({\n    dragStartTime,\n    container: frame.pageMarginBox,\n    subject,\n    center,\n    shouldUseTimeDampening,\n    getAutoScrollerOptions\n  });\n  return scroll && canScrollDroppable(droppable, scroll) ? scroll : null;\n};\n\nvar scroll = ({\n  state,\n  dragStartTime,\n  shouldUseTimeDampening,\n  scrollWindow,\n  scrollDroppable,\n  getAutoScrollerOptions\n}) => {\n  const center = state.current.page.borderBoxCenter;\n  const draggable = state.dimensions.draggables[state.critical.draggable.id];\n  const subject = draggable.page.marginBox;\n  if (state.isWindowScrollAllowed) {\n    const viewport = state.viewport;\n    const change = getWindowScrollChange({\n      dragStartTime,\n      viewport,\n      subject,\n      center,\n      shouldUseTimeDampening,\n      getAutoScrollerOptions\n    });\n    if (change) {\n      scrollWindow(change);\n      return;\n    }\n  }\n  const droppable = getBestScrollableDroppable({\n    center,\n    destination: whatIsDraggedOver(state.impact),\n    droppables: state.dimensions.droppables\n  });\n  if (!droppable) {\n    return;\n  }\n  const change = getDroppableScrollChange({\n    dragStartTime,\n    droppable,\n    subject,\n    center,\n    shouldUseTimeDampening,\n    getAutoScrollerOptions\n  });\n  if (change) {\n    scrollDroppable(droppable.descriptor.id, change);\n  }\n};\n\nvar createFluidScroller = ({\n  scrollWindow,\n  scrollDroppable,\n  getAutoScrollerOptions = () => defaultAutoScrollerOptions\n}) => {\n  const scheduleWindowScroll = rafSchd(scrollWindow);\n  const scheduleDroppableScroll = rafSchd(scrollDroppable);\n  let dragging = null;\n  const tryScroll = state => {\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fluid scroll if not dragging') : invariant() : void 0;\n    const {\n      shouldUseTimeDampening,\n      dragStartTime\n    } = dragging;\n    scroll({\n      state,\n      scrollWindow: scheduleWindowScroll,\n      scrollDroppable: scheduleDroppableScroll,\n      dragStartTime,\n      shouldUseTimeDampening,\n      getAutoScrollerOptions\n    });\n  };\n  const start$1 = state => {\n    start();\n    !!dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot start auto scrolling when already started') : invariant() : void 0;\n    const dragStartTime = Date.now();\n    let wasScrollNeeded = false;\n    const fakeScrollCallback = () => {\n      wasScrollNeeded = true;\n    };\n    scroll({\n      state,\n      dragStartTime: 0,\n      shouldUseTimeDampening: false,\n      scrollWindow: fakeScrollCallback,\n      scrollDroppable: fakeScrollCallback,\n      getAutoScrollerOptions\n    });\n    dragging = {\n      dragStartTime,\n      shouldUseTimeDampening: wasScrollNeeded\n    };\n    finish();\n    if (wasScrollNeeded) {\n      tryScroll(state);\n    }\n  };\n  const stop = () => {\n    if (!dragging) {\n      return;\n    }\n    scheduleWindowScroll.cancel();\n    scheduleDroppableScroll.cancel();\n    dragging = null;\n  };\n  return {\n    start: start$1,\n    stop,\n    scroll: tryScroll\n  };\n};\n\nvar createJumpScroller = ({\n  move,\n  scrollDroppable,\n  scrollWindow\n}) => {\n  const moveByOffset = (state, offset) => {\n    const client = add(state.current.client.selection, offset);\n    move({\n      client\n    });\n  };\n  const scrollDroppableAsMuchAsItCan = (droppable, change) => {\n    if (!canScrollDroppable(droppable, change)) {\n      return change;\n    }\n    const overlap = getDroppableOverlap(droppable, change);\n    if (!overlap) {\n      scrollDroppable(droppable.descriptor.id, change);\n      return null;\n    }\n    const whatTheDroppableCanScroll = subtract(change, overlap);\n    scrollDroppable(droppable.descriptor.id, whatTheDroppableCanScroll);\n    const remainder = subtract(change, whatTheDroppableCanScroll);\n    return remainder;\n  };\n  const scrollWindowAsMuchAsItCan = (isWindowScrollAllowed, viewport, change) => {\n    if (!isWindowScrollAllowed) {\n      return change;\n    }\n    if (!canScrollWindow(viewport, change)) {\n      return change;\n    }\n    const overlap = getWindowOverlap(viewport, change);\n    if (!overlap) {\n      scrollWindow(change);\n      return null;\n    }\n    const whatTheWindowCanScroll = subtract(change, overlap);\n    scrollWindow(whatTheWindowCanScroll);\n    const remainder = subtract(change, whatTheWindowCanScroll);\n    return remainder;\n  };\n  const jumpScroller = state => {\n    const request = state.scrollJumpRequest;\n    if (!request) {\n      return;\n    }\n    const destination = whatIsDraggedOver(state.impact);\n    !destination ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot perform a jump scroll when there is no destination') : invariant() : void 0;\n    const droppableRemainder = scrollDroppableAsMuchAsItCan(state.dimensions.droppables[destination], request);\n    if (!droppableRemainder) {\n      return;\n    }\n    const viewport = state.viewport;\n    const windowRemainder = scrollWindowAsMuchAsItCan(state.isWindowScrollAllowed, viewport, droppableRemainder);\n    if (!windowRemainder) {\n      return;\n    }\n    moveByOffset(state, windowRemainder);\n  };\n  return jumpScroller;\n};\n\nvar createAutoScroller = ({\n  scrollDroppable,\n  scrollWindow,\n  move,\n  getAutoScrollerOptions\n}) => {\n  const fluidScroller = createFluidScroller({\n    scrollWindow,\n    scrollDroppable,\n    getAutoScrollerOptions\n  });\n  const jumpScroll = createJumpScroller({\n    move,\n    scrollWindow,\n    scrollDroppable\n  });\n  const scroll = state => {\n    const autoScrollerOptions = getAutoScrollerOptions();\n    if (autoScrollerOptions.disabled || state.phase !== 'DRAGGING') {\n      return;\n    }\n    if (state.movementMode === 'FLUID') {\n      fluidScroller.scroll(state);\n      return;\n    }\n    if (!state.scrollJumpRequest) {\n      return;\n    }\n    jumpScroll(state);\n  };\n  const scroller = {\n    scroll,\n    start: fluidScroller.start,\n    stop: fluidScroller.stop\n  };\n  return scroller;\n};\n\nconst prefix = 'data-rfd';\nconst dragHandle = (() => {\n  const base = `${prefix}-drag-handle`;\n  return {\n    base,\n    draggableId: `${base}-draggable-id`,\n    contextId: `${base}-context-id`\n  };\n})();\nconst draggable = (() => {\n  const base = `${prefix}-draggable`;\n  return {\n    base,\n    contextId: `${base}-context-id`,\n    id: `${base}-id`\n  };\n})();\nconst droppable = (() => {\n  const base = `${prefix}-droppable`;\n  return {\n    base,\n    contextId: `${base}-context-id`,\n    id: `${base}-id`\n  };\n})();\nconst scrollContainer = {\n  contextId: `${prefix}-scroll-container-context-id`\n};\n\nconst makeGetSelector = context => attribute => `[${attribute}=\"${context}\"]`;\nconst getStyles = (rules, property) => rules.map(rule => {\n  const value = rule.styles[property];\n  if (!value) {\n    return '';\n  }\n  return `${rule.selector} { ${value} }`;\n}).join(' ');\nconst noPointerEvents = 'pointer-events: none;';\nvar getStyles$1 = contextId => {\n  const getSelector = makeGetSelector(contextId);\n  const dragHandle$1 = (() => {\n    const grabCursor = `\n      cursor: -webkit-grab;\n      cursor: grab;\n    `;\n    return {\n      selector: getSelector(dragHandle.contextId),\n      styles: {\n        always: `\n          -webkit-touch-callout: none;\n          -webkit-tap-highlight-color: rgba(0,0,0,0);\n          touch-action: manipulation;\n        `,\n        resting: grabCursor,\n        dragging: noPointerEvents,\n        dropAnimating: grabCursor\n      }\n    };\n  })();\n  const draggable$1 = (() => {\n    const transition = `\n      transition: ${transitions.outOfTheWay};\n    `;\n    return {\n      selector: getSelector(draggable.contextId),\n      styles: {\n        dragging: transition,\n        dropAnimating: transition,\n        userCancel: transition\n      }\n    };\n  })();\n  const droppable$1 = {\n    selector: getSelector(droppable.contextId),\n    styles: {\n      always: `overflow-anchor: none;`\n    }\n  };\n  const body = {\n    selector: 'body',\n    styles: {\n      dragging: `\n        cursor: grabbing;\n        cursor: -webkit-grabbing;\n        user-select: none;\n        -webkit-user-select: none;\n        -moz-user-select: none;\n        -ms-user-select: none;\n        overflow-anchor: none;\n      `\n    }\n  };\n  const rules = [draggable$1, dragHandle$1, droppable$1, body];\n  return {\n    always: getStyles(rules, 'always'),\n    resting: getStyles(rules, 'resting'),\n    dragging: getStyles(rules, 'dragging'),\n    dropAnimating: getStyles(rules, 'dropAnimating'),\n    userCancel: getStyles(rules, 'userCancel')\n  };\n};\n\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined' ? useLayoutEffect : useEffect;\n\nconst getHead = () => {\n  const head = document.querySelector('head');\n  !head ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot find the head to append a style to') : invariant() : void 0;\n  return head;\n};\nconst createStyleEl = nonce => {\n  const el = document.createElement('style');\n  if (nonce) {\n    el.setAttribute('nonce', nonce);\n  }\n  el.type = 'text/css';\n  return el;\n};\nfunction useStyleMarshal(contextId, nonce) {\n  const styles = useMemo(() => getStyles$1(contextId), [contextId]);\n  const alwaysRef = useRef(null);\n  const dynamicRef = useRef(null);\n  const setDynamicStyle = useCallback(memoizeOne(proposed => {\n    const el = dynamicRef.current;\n    !el ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot set dynamic style element if it is not set') : invariant() : void 0;\n    el.textContent = proposed;\n  }), []);\n  const setAlwaysStyle = useCallback(proposed => {\n    const el = alwaysRef.current;\n    !el ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot set dynamic style element if it is not set') : invariant() : void 0;\n    el.textContent = proposed;\n  }, []);\n  useIsomorphicLayoutEffect(() => {\n    !(!alwaysRef.current && !dynamicRef.current) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'style elements already mounted') : invariant() : void 0;\n    const always = createStyleEl(nonce);\n    const dynamic = createStyleEl(nonce);\n    alwaysRef.current = always;\n    dynamicRef.current = dynamic;\n    always.setAttribute(`${prefix}-always`, contextId);\n    dynamic.setAttribute(`${prefix}-dynamic`, contextId);\n    getHead().appendChild(always);\n    getHead().appendChild(dynamic);\n    setAlwaysStyle(styles.always);\n    setDynamicStyle(styles.resting);\n    return () => {\n      const remove = ref => {\n        const current = ref.current;\n        !current ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot unmount ref as it is not set') : invariant() : void 0;\n        getHead().removeChild(current);\n        ref.current = null;\n      };\n      remove(alwaysRef);\n      remove(dynamicRef);\n    };\n  }, [nonce, setAlwaysStyle, setDynamicStyle, styles.always, styles.resting, contextId]);\n  const dragging = useCallback(() => setDynamicStyle(styles.dragging), [setDynamicStyle, styles.dragging]);\n  const dropping = useCallback(reason => {\n    if (reason === 'DROP') {\n      setDynamicStyle(styles.dropAnimating);\n      return;\n    }\n    setDynamicStyle(styles.userCancel);\n  }, [setDynamicStyle, styles.dropAnimating, styles.userCancel]);\n  const resting = useCallback(() => {\n    if (!dynamicRef.current) {\n      return;\n    }\n    setDynamicStyle(styles.resting);\n  }, [setDynamicStyle, styles.resting]);\n  const marshal = useMemo(() => ({\n    dragging,\n    dropping,\n    resting\n  }), [dragging, dropping, resting]);\n  return marshal;\n}\n\nfunction querySelectorAll(parentNode, selector) {\n  return Array.from(parentNode.querySelectorAll(selector));\n}\n\nvar getWindowFromEl = el => {\n  if (el && el.ownerDocument && el.ownerDocument.defaultView) {\n    return el.ownerDocument.defaultView;\n  }\n  return window;\n};\n\nfunction isHtmlElement(el) {\n  return el instanceof getWindowFromEl(el).HTMLElement;\n}\n\nfunction findDragHandle(contextId, draggableId) {\n  const selector = `[${dragHandle.contextId}=\"${contextId}\"]`;\n  const possible = querySelectorAll(document, selector);\n  if (!possible.length) {\n    process.env.NODE_ENV !== \"production\" ? warning(`Unable to find any drag handles in the context \"${contextId}\"`) : void 0;\n    return null;\n  }\n  const handle = possible.find(el => {\n    return el.getAttribute(dragHandle.draggableId) === draggableId;\n  });\n  if (!handle) {\n    process.env.NODE_ENV !== \"production\" ? warning(`Unable to find drag handle with id \"${draggableId}\" as no handle with a matching id was found`) : void 0;\n    return null;\n  }\n  if (!isHtmlElement(handle)) {\n    process.env.NODE_ENV !== \"production\" ? warning('drag handle needs to be a HTMLElement') : void 0;\n    return null;\n  }\n  return handle;\n}\n\nfunction useFocusMarshal(contextId) {\n  const entriesRef = useRef({});\n  const recordRef = useRef(null);\n  const restoreFocusFrameRef = useRef(null);\n  const isMountedRef = useRef(false);\n  const register = useCallback(function register(id, focus) {\n    const entry = {\n      id,\n      focus\n    };\n    entriesRef.current[id] = entry;\n    return function unregister() {\n      const entries = entriesRef.current;\n      const current = entries[id];\n      if (current !== entry) {\n        delete entries[id];\n      }\n    };\n  }, []);\n  const tryGiveFocus = useCallback(function tryGiveFocus(tryGiveFocusTo) {\n    const handle = findDragHandle(contextId, tryGiveFocusTo);\n    if (handle && handle !== document.activeElement) {\n      handle.focus();\n    }\n  }, [contextId]);\n  const tryShiftRecord = useCallback(function tryShiftRecord(previous, redirectTo) {\n    if (recordRef.current === previous) {\n      recordRef.current = redirectTo;\n    }\n  }, []);\n  const tryRestoreFocusRecorded = useCallback(function tryRestoreFocusRecorded() {\n    if (restoreFocusFrameRef.current) {\n      return;\n    }\n    if (!isMountedRef.current) {\n      return;\n    }\n    restoreFocusFrameRef.current = requestAnimationFrame(() => {\n      restoreFocusFrameRef.current = null;\n      const record = recordRef.current;\n      if (record) {\n        tryGiveFocus(record);\n      }\n    });\n  }, [tryGiveFocus]);\n  const tryRecordFocus = useCallback(function tryRecordFocus(id) {\n    recordRef.current = null;\n    const focused = document.activeElement;\n    if (!focused) {\n      return;\n    }\n    if (focused.getAttribute(dragHandle.draggableId) !== id) {\n      return;\n    }\n    recordRef.current = id;\n  }, []);\n  useIsomorphicLayoutEffect(() => {\n    isMountedRef.current = true;\n    return function clearFrameOnUnmount() {\n      isMountedRef.current = false;\n      const frameId = restoreFocusFrameRef.current;\n      if (frameId) {\n        cancelAnimationFrame(frameId);\n      }\n    };\n  }, []);\n  const marshal = useMemo(() => ({\n    register,\n    tryRecordFocus,\n    tryRestoreFocusRecorded,\n    tryShiftRecord\n  }), [register, tryRecordFocus, tryRestoreFocusRecorded, tryShiftRecord]);\n  return marshal;\n}\n\nfunction createRegistry() {\n  const entries = {\n    draggables: {},\n    droppables: {}\n  };\n  const subscribers = [];\n  function subscribe(cb) {\n    subscribers.push(cb);\n    return function unsubscribe() {\n      const index = subscribers.indexOf(cb);\n      if (index === -1) {\n        return;\n      }\n      subscribers.splice(index, 1);\n    };\n  }\n  function notify(event) {\n    if (subscribers.length) {\n      subscribers.forEach(cb => cb(event));\n    }\n  }\n  function findDraggableById(id) {\n    return entries.draggables[id] || null;\n  }\n  function getDraggableById(id) {\n    const entry = findDraggableById(id);\n    !entry ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot find draggable entry with id [${id}]`) : invariant() : void 0;\n    return entry;\n  }\n  const draggableAPI = {\n    register: entry => {\n      entries.draggables[entry.descriptor.id] = entry;\n      notify({\n        type: 'ADDITION',\n        value: entry\n      });\n    },\n    update: (entry, last) => {\n      const current = entries.draggables[last.descriptor.id];\n      if (!current) {\n        return;\n      }\n      if (current.uniqueId !== entry.uniqueId) {\n        return;\n      }\n      delete entries.draggables[last.descriptor.id];\n      entries.draggables[entry.descriptor.id] = entry;\n    },\n    unregister: entry => {\n      const draggableId = entry.descriptor.id;\n      const current = findDraggableById(draggableId);\n      if (!current) {\n        return;\n      }\n      if (entry.uniqueId !== current.uniqueId) {\n        return;\n      }\n      delete entries.draggables[draggableId];\n      if (entries.droppables[entry.descriptor.droppableId]) {\n        notify({\n          type: 'REMOVAL',\n          value: entry\n        });\n      }\n    },\n    getById: getDraggableById,\n    findById: findDraggableById,\n    exists: id => Boolean(findDraggableById(id)),\n    getAllByType: type => Object.values(entries.draggables).filter(entry => entry.descriptor.type === type)\n  };\n  function findDroppableById(id) {\n    return entries.droppables[id] || null;\n  }\n  function getDroppableById(id) {\n    const entry = findDroppableById(id);\n    !entry ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot find droppable entry with id [${id}]`) : invariant() : void 0;\n    return entry;\n  }\n  const droppableAPI = {\n    register: entry => {\n      entries.droppables[entry.descriptor.id] = entry;\n    },\n    unregister: entry => {\n      const current = findDroppableById(entry.descriptor.id);\n      if (!current) {\n        return;\n      }\n      if (entry.uniqueId !== current.uniqueId) {\n        return;\n      }\n      delete entries.droppables[entry.descriptor.id];\n    },\n    getById: getDroppableById,\n    findById: findDroppableById,\n    exists: id => Boolean(findDroppableById(id)),\n    getAllByType: type => Object.values(entries.droppables).filter(entry => entry.descriptor.type === type)\n  };\n  function clean() {\n    entries.draggables = {};\n    entries.droppables = {};\n    subscribers.length = 0;\n  }\n  return {\n    draggable: draggableAPI,\n    droppable: droppableAPI,\n    subscribe,\n    clean\n  };\n}\n\nfunction useRegistry() {\n  const registry = useMemo(createRegistry, []);\n  useEffect(() => {\n    return function unmount() {\n      registry.clean();\n    };\n  }, [registry]);\n  return registry;\n}\n\nvar StoreContext = React.createContext(null);\n\nvar getBodyElement = () => {\n  const body = document.body;\n  !body ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot find document.body') : invariant() : void 0;\n  return body;\n};\n\nconst visuallyHidden = {\n  position: 'absolute',\n  width: '1px',\n  height: '1px',\n  margin: '-1px',\n  border: '0',\n  padding: '0',\n  overflow: 'hidden',\n  clip: 'rect(0 0 0 0)',\n  'clip-path': 'inset(100%)'\n};\n\nconst getId = contextId => `rfd-announcement-${contextId}`;\nfunction useAnnouncer(contextId) {\n  const id = useMemo(() => getId(contextId), [contextId]);\n  const ref = useRef(null);\n  useEffect(function setup() {\n    const el = document.createElement('div');\n    ref.current = el;\n    el.id = id;\n    el.setAttribute('aria-live', 'assertive');\n    el.setAttribute('aria-atomic', 'true');\n    _extends(el.style, visuallyHidden);\n    getBodyElement().appendChild(el);\n    return function cleanup() {\n      setTimeout(function remove() {\n        const body = getBodyElement();\n        if (body.contains(el)) {\n          body.removeChild(el);\n        }\n        if (el === ref.current) {\n          ref.current = null;\n        }\n      });\n    };\n  }, [id]);\n  const announce = useCallback(message => {\n    const el = ref.current;\n    if (el) {\n      el.textContent = message;\n      return;\n    }\n    process.env.NODE_ENV !== \"production\" ? warning(`\n      A screen reader message was trying to be announced but it was unable to do so.\n      This can occur if you unmount your <DragDropContext /> in your onDragEnd.\n      Consider calling provided.announce() before the unmount so that the instruction will\n      not be lost for users relying on a screen reader.\n\n      Message not passed to screen reader:\n\n      \"${message}\"\n    `) : void 0;\n  }, []);\n  return announce;\n}\n\nconst defaults = {\n  separator: '::'\n};\nfunction useUniqueId(prefix, options = defaults) {\n  const id = React.useId();\n  return useMemo(() => `${prefix}${options.separator}${id}`, [options.separator, prefix, id]);\n}\n\nfunction getElementId({\n  contextId,\n  uniqueId\n}) {\n  return `rfd-hidden-text-${contextId}-${uniqueId}`;\n}\nfunction useHiddenTextElement({\n  contextId,\n  text\n}) {\n  const uniqueId = useUniqueId('hidden-text', {\n    separator: '-'\n  });\n  const id = useMemo(() => getElementId({\n    contextId,\n    uniqueId\n  }), [uniqueId, contextId]);\n  useEffect(function mount() {\n    const el = document.createElement('div');\n    el.id = id;\n    el.textContent = text;\n    el.style.display = 'none';\n    getBodyElement().appendChild(el);\n    return function unmount() {\n      const body = getBodyElement();\n      if (body.contains(el)) {\n        body.removeChild(el);\n      }\n    };\n  }, [id, text]);\n  return id;\n}\n\nvar AppContext = React.createContext(null);\n\nvar peerDependencies = {\n\treact: \"^18.0.0 || ^19.0.0\"};\n\nconst semver = /(\\d+)\\.(\\d+)\\.(\\d+)/;\nconst getVersion = value => {\n  const result = semver.exec(value);\n  !(result != null) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Unable to parse React version ${value}`) : invariant() : void 0;\n  const major = Number(result[1]);\n  const minor = Number(result[2]);\n  const patch = Number(result[3]);\n  return {\n    major,\n    minor,\n    patch,\n    raw: value\n  };\n};\nconst isSatisfied = (expected, actual) => {\n  if (actual.major > expected.major) {\n    return true;\n  }\n  if (actual.major < expected.major) {\n    return false;\n  }\n  if (actual.minor > expected.minor) {\n    return true;\n  }\n  if (actual.minor < expected.minor) {\n    return false;\n  }\n  return actual.patch >= expected.patch;\n};\nvar checkReactVersion = (peerDepValue, actualValue) => {\n  const peerDep = getVersion(peerDepValue);\n  const actual = getVersion(actualValue);\n  if (isSatisfied(peerDep, actual)) {\n    return;\n  }\n  process.env.NODE_ENV !== \"production\" ? warning(`\n    React version: [${actual.raw}]\n    does not satisfy expected peer dependency version: [${peerDep.raw}]\n\n    This can result in run time bugs, and even fatal crashes\n  `) : void 0;\n};\n\nconst suffix = `\n  We expect a html5 doctype: <!doctype html>\n  This is to ensure consistent browser layout and measurement\n\n  More information: https://github.com/hello-pangea/dnd/blob/main/docs/guides/doctype.md\n`;\nvar checkDoctype = doc => {\n  const doctype = doc.doctype;\n  if (!doctype) {\n    process.env.NODE_ENV !== \"production\" ? warning(`\n      No <!doctype html> found.\n\n      ${suffix}\n    `) : void 0;\n    return;\n  }\n  if (doctype.name.toLowerCase() !== 'html') {\n    process.env.NODE_ENV !== \"production\" ? warning(`\n      Unexpected <!doctype> found: (${doctype.name})\n\n      ${suffix}\n    `) : void 0;\n  }\n  if (doctype.publicId !== '') {\n    process.env.NODE_ENV !== \"production\" ? warning(`\n      Unexpected <!doctype> publicId found: (${doctype.publicId})\n      A html5 doctype does not have a publicId\n\n      ${suffix}\n    `) : void 0;\n  }\n};\n\nfunction useDev(useHook) {\n  if (process.env.NODE_ENV !== 'production') {\n    useHook();\n  }\n}\n\nfunction useDevSetupWarning(fn, inputs) {\n  useDev(() => {\n    useEffect(() => {\n      try {\n        fn();\n      } catch (e) {\n        error(`\n          A setup problem was encountered.\n\n          > ${e.message}\n        `);\n      }\n    }, inputs);\n  });\n}\n\nfunction useStartupValidation() {\n  useDevSetupWarning(() => {\n    checkReactVersion(peerDependencies.react, React.version);\n    checkDoctype(document);\n  }, []);\n}\n\nfunction usePrevious(current) {\n  const ref = useRef(current);\n  useEffect(() => {\n    ref.current = current;\n  });\n  return ref;\n}\n\nfunction create() {\n  let lock = null;\n  function isClaimed() {\n    return Boolean(lock);\n  }\n  function isActive(value) {\n    return value === lock;\n  }\n  function claim(abandon) {\n    !!lock ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot claim lock as it is already claimed') : invariant() : void 0;\n    const newLock = {\n      abandon\n    };\n    lock = newLock;\n    return newLock;\n  }\n  function release() {\n    !lock ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot release lock when there is no lock') : invariant() : void 0;\n    lock = null;\n  }\n  function tryAbandon() {\n    if (lock) {\n      lock.abandon();\n      release();\n    }\n  }\n  return {\n    isClaimed,\n    isActive,\n    claim,\n    release,\n    tryAbandon\n  };\n}\n\nfunction isDragging(state) {\n  if (state.phase === 'IDLE' || state.phase === 'DROP_ANIMATING') {\n    return false;\n  }\n  return state.isDragging;\n}\n\nconst tab = 9;\nconst enter = 13;\nconst escape = 27;\nconst space = 32;\nconst pageUp = 33;\nconst pageDown = 34;\nconst end = 35;\nconst home = 36;\nconst arrowLeft = 37;\nconst arrowUp = 38;\nconst arrowRight = 39;\nconst arrowDown = 40;\n\nconst preventedKeys = {\n  [enter]: true,\n  [tab]: true\n};\nvar preventStandardKeyEvents = event => {\n  if (preventedKeys[event.keyCode]) {\n    event.preventDefault();\n  }\n};\n\nconst supportedEventName = (() => {\n  const base = 'visibilitychange';\n  if (typeof document === 'undefined') {\n    return base;\n  }\n  const candidates = [base, `ms${base}`, `webkit${base}`, `moz${base}`, `o${base}`];\n  const supported = candidates.find(eventName => `on${eventName}` in document);\n  return supported || base;\n})();\n\nconst primaryButton = 0;\nconst sloppyClickThreshold = 5;\nfunction isSloppyClickThresholdExceeded(original, current) {\n  return Math.abs(current.x - original.x) >= sloppyClickThreshold || Math.abs(current.y - original.y) >= sloppyClickThreshold;\n}\nconst idle$1 = {\n  type: 'IDLE'\n};\nfunction getCaptureBindings({\n  cancel,\n  completed,\n  getPhase,\n  setPhase\n}) {\n  return [{\n    eventName: 'mousemove',\n    fn: event => {\n      const {\n        button,\n        clientX,\n        clientY\n      } = event;\n      if (button !== primaryButton) {\n        return;\n      }\n      const point = {\n        x: clientX,\n        y: clientY\n      };\n      const phase = getPhase();\n      if (phase.type === 'DRAGGING') {\n        event.preventDefault();\n        phase.actions.move(point);\n        return;\n      }\n      !(phase.type === 'PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot be IDLE') : invariant() : void 0;\n      const pending = phase.point;\n      if (!isSloppyClickThresholdExceeded(pending, point)) {\n        return;\n      }\n      event.preventDefault();\n      const actions = phase.actions.fluidLift(point);\n      setPhase({\n        type: 'DRAGGING',\n        actions\n      });\n    }\n  }, {\n    eventName: 'mouseup',\n    fn: event => {\n      const phase = getPhase();\n      if (phase.type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n      event.preventDefault();\n      phase.actions.drop({\n        shouldBlockNextClick: true\n      });\n      completed();\n    }\n  }, {\n    eventName: 'mousedown',\n    fn: event => {\n      if (getPhase().type === 'DRAGGING') {\n        event.preventDefault();\n      }\n      cancel();\n    }\n  }, {\n    eventName: 'keydown',\n    fn: event => {\n      const phase = getPhase();\n      if (phase.type === 'PENDING') {\n        cancel();\n        return;\n      }\n      if (event.keyCode === escape) {\n        event.preventDefault();\n        cancel();\n        return;\n      }\n      preventStandardKeyEvents(event);\n    }\n  }, {\n    eventName: 'resize',\n    fn: cancel\n  }, {\n    eventName: 'scroll',\n    options: {\n      passive: true,\n      capture: false\n    },\n    fn: () => {\n      if (getPhase().type === 'PENDING') {\n        cancel();\n      }\n    }\n  }, {\n    eventName: 'webkitmouseforcedown',\n    fn: event => {\n      const phase = getPhase();\n      !(phase.type !== 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Unexpected phase') : invariant() : void 0;\n      if (phase.actions.shouldRespectForcePress()) {\n        cancel();\n        return;\n      }\n      event.preventDefault();\n    }\n  }, {\n    eventName: supportedEventName,\n    fn: cancel\n  }];\n}\nfunction useMouseSensor(api) {\n  const phaseRef = useRef(idle$1);\n  const unbindEventsRef = useRef(noop$2);\n  const startCaptureBinding = useMemo(() => ({\n    eventName: 'mousedown',\n    fn: function onMouseDown(event) {\n      if (event.defaultPrevented) {\n        return;\n      }\n      if (event.button !== primaryButton) {\n        return;\n      }\n      if (event.ctrlKey || event.metaKey || event.shiftKey || event.altKey) {\n        return;\n      }\n      const draggableId = api.findClosestDraggableId(event);\n      if (!draggableId) {\n        return;\n      }\n      const actions = api.tryGetLock(draggableId, stop, {\n        sourceEvent: event\n      });\n      if (!actions) {\n        return;\n      }\n      event.preventDefault();\n      const point = {\n        x: event.clientX,\n        y: event.clientY\n      };\n      unbindEventsRef.current();\n      startPendingDrag(actions, point);\n    }\n  }), [api]);\n  const preventForcePressBinding = useMemo(() => ({\n    eventName: 'webkitmouseforcewillbegin',\n    fn: event => {\n      if (event.defaultPrevented) {\n        return;\n      }\n      const id = api.findClosestDraggableId(event);\n      if (!id) {\n        return;\n      }\n      const options = api.findOptionsForDraggable(id);\n      if (!options) {\n        return;\n      }\n      if (options.shouldRespectForcePress) {\n        return;\n      }\n      if (!api.canGetLock(id)) {\n        return;\n      }\n      event.preventDefault();\n    }\n  }), [api]);\n  const listenForCapture = useCallback(function listenForCapture() {\n    const options = {\n      passive: false,\n      capture: true\n    };\n    unbindEventsRef.current = bindEvents(window, [preventForcePressBinding, startCaptureBinding], options);\n  }, [preventForcePressBinding, startCaptureBinding]);\n  const stop = useCallback(() => {\n    const current = phaseRef.current;\n    if (current.type === 'IDLE') {\n      return;\n    }\n    phaseRef.current = idle$1;\n    unbindEventsRef.current();\n    listenForCapture();\n  }, [listenForCapture]);\n  const cancel = useCallback(() => {\n    const phase = phaseRef.current;\n    stop();\n    if (phase.type === 'DRAGGING') {\n      phase.actions.cancel({\n        shouldBlockNextClick: true\n      });\n    }\n    if (phase.type === 'PENDING') {\n      phase.actions.abort();\n    }\n  }, [stop]);\n  const bindCapturingEvents = useCallback(function bindCapturingEvents() {\n    const options = {\n      capture: true,\n      passive: false\n    };\n    const bindings = getCaptureBindings({\n      cancel,\n      completed: stop,\n      getPhase: () => phaseRef.current,\n      setPhase: phase => {\n        phaseRef.current = phase;\n      }\n    });\n    unbindEventsRef.current = bindEvents(window, bindings, options);\n  }, [cancel, stop]);\n  const startPendingDrag = useCallback(function startPendingDrag(actions, point) {\n    !(phaseRef.current.type === 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected to move from IDLE to PENDING drag') : invariant() : void 0;\n    phaseRef.current = {\n      type: 'PENDING',\n      point,\n      actions\n    };\n    bindCapturingEvents();\n  }, [bindCapturingEvents]);\n  useIsomorphicLayoutEffect(function mount() {\n    listenForCapture();\n    return function unmount() {\n      unbindEventsRef.current();\n    };\n  }, [listenForCapture]);\n}\n\nfunction noop$1() {}\nconst scrollJumpKeys = {\n  [pageDown]: true,\n  [pageUp]: true,\n  [home]: true,\n  [end]: true\n};\nfunction getDraggingBindings(actions, stop) {\n  function cancel() {\n    stop();\n    actions.cancel();\n  }\n  function drop() {\n    stop();\n    actions.drop();\n  }\n  return [{\n    eventName: 'keydown',\n    fn: event => {\n      if (event.keyCode === escape) {\n        event.preventDefault();\n        cancel();\n        return;\n      }\n      if (event.keyCode === space) {\n        event.preventDefault();\n        drop();\n        return;\n      }\n      if (event.keyCode === arrowDown) {\n        event.preventDefault();\n        actions.moveDown();\n        return;\n      }\n      if (event.keyCode === arrowUp) {\n        event.preventDefault();\n        actions.moveUp();\n        return;\n      }\n      if (event.keyCode === arrowRight) {\n        event.preventDefault();\n        actions.moveRight();\n        return;\n      }\n      if (event.keyCode === arrowLeft) {\n        event.preventDefault();\n        actions.moveLeft();\n        return;\n      }\n      if (scrollJumpKeys[event.keyCode]) {\n        event.preventDefault();\n        return;\n      }\n      preventStandardKeyEvents(event);\n    }\n  }, {\n    eventName: 'mousedown',\n    fn: cancel\n  }, {\n    eventName: 'mouseup',\n    fn: cancel\n  }, {\n    eventName: 'click',\n    fn: cancel\n  }, {\n    eventName: 'touchstart',\n    fn: cancel\n  }, {\n    eventName: 'resize',\n    fn: cancel\n  }, {\n    eventName: 'wheel',\n    fn: cancel,\n    options: {\n      passive: true\n    }\n  }, {\n    eventName: supportedEventName,\n    fn: cancel\n  }];\n}\nfunction useKeyboardSensor(api) {\n  const unbindEventsRef = useRef(noop$1);\n  const startCaptureBinding = useMemo(() => ({\n    eventName: 'keydown',\n    fn: function onKeyDown(event) {\n      if (event.defaultPrevented) {\n        return;\n      }\n      if (event.keyCode !== space) {\n        return;\n      }\n      const draggableId = api.findClosestDraggableId(event);\n      if (!draggableId) {\n        return;\n      }\n      const preDrag = api.tryGetLock(draggableId, stop, {\n        sourceEvent: event\n      });\n      if (!preDrag) {\n        return;\n      }\n      event.preventDefault();\n      let isCapturing = true;\n      const actions = preDrag.snapLift();\n      unbindEventsRef.current();\n      function stop() {\n        !isCapturing ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot stop capturing a keyboard drag when not capturing') : invariant() : void 0;\n        isCapturing = false;\n        unbindEventsRef.current();\n        listenForCapture();\n      }\n      unbindEventsRef.current = bindEvents(window, getDraggingBindings(actions, stop), {\n        capture: true,\n        passive: false\n      });\n    }\n  }), [api]);\n  const listenForCapture = useCallback(function tryStartCapture() {\n    const options = {\n      passive: false,\n      capture: true\n    };\n    unbindEventsRef.current = bindEvents(window, [startCaptureBinding], options);\n  }, [startCaptureBinding]);\n  useIsomorphicLayoutEffect(function mount() {\n    listenForCapture();\n    return function unmount() {\n      unbindEventsRef.current();\n    };\n  }, [listenForCapture]);\n}\n\nconst idle = {\n  type: 'IDLE'\n};\nconst timeForLongPress = 120;\nconst forcePressThreshold = 0.15;\nfunction getWindowBindings({\n  cancel,\n  getPhase\n}) {\n  return [{\n    eventName: 'orientationchange',\n    fn: cancel\n  }, {\n    eventName: 'resize',\n    fn: cancel\n  }, {\n    eventName: 'contextmenu',\n    fn: event => {\n      event.preventDefault();\n    }\n  }, {\n    eventName: 'keydown',\n    fn: event => {\n      if (getPhase().type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n      if (event.keyCode === escape) {\n        event.preventDefault();\n      }\n      cancel();\n    }\n  }, {\n    eventName: supportedEventName,\n    fn: cancel\n  }];\n}\nfunction getHandleBindings({\n  cancel,\n  completed,\n  getPhase\n}) {\n  return [{\n    eventName: 'touchmove',\n    options: {\n      capture: false\n    },\n    fn: event => {\n      const phase = getPhase();\n      if (phase.type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n      phase.hasMoved = true;\n      const {\n        clientX,\n        clientY\n      } = event.touches[0];\n      const point = {\n        x: clientX,\n        y: clientY\n      };\n      event.preventDefault();\n      phase.actions.move(point);\n    }\n  }, {\n    eventName: 'touchend',\n    fn: event => {\n      const phase = getPhase();\n      if (phase.type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n      event.preventDefault();\n      phase.actions.drop({\n        shouldBlockNextClick: true\n      });\n      completed();\n    }\n  }, {\n    eventName: 'touchcancel',\n    fn: event => {\n      if (getPhase().type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n      event.preventDefault();\n      cancel();\n    }\n  }, {\n    eventName: 'touchforcechange',\n    fn: event => {\n      const phase = getPhase();\n      !(phase.type !== 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant() : invariant() : void 0;\n      const touch = event.touches[0];\n      if (!touch) {\n        return;\n      }\n      const isForcePress = touch.force >= forcePressThreshold;\n      if (!isForcePress) {\n        return;\n      }\n      const shouldRespect = phase.actions.shouldRespectForcePress();\n      if (phase.type === 'PENDING') {\n        if (shouldRespect) {\n          cancel();\n        }\n        return;\n      }\n      if (shouldRespect) {\n        if (phase.hasMoved) {\n          event.preventDefault();\n          return;\n        }\n        cancel();\n        return;\n      }\n      event.preventDefault();\n    }\n  }, {\n    eventName: supportedEventName,\n    fn: cancel\n  }];\n}\nfunction useTouchSensor(api) {\n  const phaseRef = useRef(idle);\n  const unbindEventsRef = useRef(noop$2);\n  const getPhase = useCallback(function getPhase() {\n    return phaseRef.current;\n  }, []);\n  const setPhase = useCallback(function setPhase(phase) {\n    phaseRef.current = phase;\n  }, []);\n  const startCaptureBinding = useMemo(() => ({\n    eventName: 'touchstart',\n    fn: function onTouchStart(event) {\n      if (event.defaultPrevented) {\n        return;\n      }\n      const draggableId = api.findClosestDraggableId(event);\n      if (!draggableId) {\n        return;\n      }\n      const actions = api.tryGetLock(draggableId, stop, {\n        sourceEvent: event\n      });\n      if (!actions) {\n        return;\n      }\n      const touch = event.touches[0];\n      const {\n        clientX,\n        clientY\n      } = touch;\n      const point = {\n        x: clientX,\n        y: clientY\n      };\n      unbindEventsRef.current();\n      startPendingDrag(actions, point);\n    }\n  }), [api]);\n  const listenForCapture = useCallback(function listenForCapture() {\n    const options = {\n      capture: true,\n      passive: false\n    };\n    unbindEventsRef.current = bindEvents(window, [startCaptureBinding], options);\n  }, [startCaptureBinding]);\n  const stop = useCallback(() => {\n    const current = phaseRef.current;\n    if (current.type === 'IDLE') {\n      return;\n    }\n    if (current.type === 'PENDING') {\n      clearTimeout(current.longPressTimerId);\n    }\n    setPhase(idle);\n    unbindEventsRef.current();\n    listenForCapture();\n  }, [listenForCapture, setPhase]);\n  const cancel = useCallback(() => {\n    const phase = phaseRef.current;\n    stop();\n    if (phase.type === 'DRAGGING') {\n      phase.actions.cancel({\n        shouldBlockNextClick: true\n      });\n    }\n    if (phase.type === 'PENDING') {\n      phase.actions.abort();\n    }\n  }, [stop]);\n  const bindCapturingEvents = useCallback(function bindCapturingEvents() {\n    const options = {\n      capture: true,\n      passive: false\n    };\n    const args = {\n      cancel,\n      completed: stop,\n      getPhase\n    };\n    const unbindTarget = bindEvents(window, getHandleBindings(args), options);\n    const unbindWindow = bindEvents(window, getWindowBindings(args), options);\n    unbindEventsRef.current = function unbindAll() {\n      unbindTarget();\n      unbindWindow();\n    };\n  }, [cancel, getPhase, stop]);\n  const startDragging = useCallback(function startDragging() {\n    const phase = getPhase();\n    !(phase.type === 'PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot start dragging from phase ${phase.type}`) : invariant() : void 0;\n    const actions = phase.actions.fluidLift(phase.point);\n    setPhase({\n      type: 'DRAGGING',\n      actions,\n      hasMoved: false\n    });\n  }, [getPhase, setPhase]);\n  const startPendingDrag = useCallback(function startPendingDrag(actions, point) {\n    !(getPhase().type === 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected to move from IDLE to PENDING drag') : invariant() : void 0;\n    const longPressTimerId = setTimeout(startDragging, timeForLongPress);\n    setPhase({\n      type: 'PENDING',\n      point,\n      actions,\n      longPressTimerId\n    });\n    bindCapturingEvents();\n  }, [bindCapturingEvents, getPhase, setPhase, startDragging]);\n  useIsomorphicLayoutEffect(function mount() {\n    listenForCapture();\n    return function unmount() {\n      unbindEventsRef.current();\n      const phase = getPhase();\n      if (phase.type === 'PENDING') {\n        clearTimeout(phase.longPressTimerId);\n        setPhase(idle);\n      }\n    };\n  }, [getPhase, listenForCapture, setPhase]);\n  useIsomorphicLayoutEffect(function webkitHack() {\n    const unbind = bindEvents(window, [{\n      eventName: 'touchmove',\n      fn: () => {},\n      options: {\n        capture: false,\n        passive: false\n      }\n    }]);\n    return unbind;\n  }, []);\n}\n\nfunction useValidateSensorHooks(sensorHooks) {\n  useDev(() => {\n    const previousRef = usePrevious(sensorHooks);\n    useDevSetupWarning(() => {\n      !(previousRef.current.length === sensorHooks.length) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot change the amount of sensor hooks after mounting') : invariant(false) : void 0;\n    });\n  });\n}\n\nconst interactiveTagNames = ['input', 'button', 'textarea', 'select', 'option', 'optgroup', 'video', 'audio'];\nfunction isAnInteractiveElement(parent, current) {\n  if (current == null) {\n    return false;\n  }\n  const hasAnInteractiveTag = interactiveTagNames.includes(current.tagName.toLowerCase());\n  if (hasAnInteractiveTag) {\n    return true;\n  }\n  const attribute = current.getAttribute('contenteditable');\n  if (attribute === 'true' || attribute === '') {\n    return true;\n  }\n  if (current === parent) {\n    return false;\n  }\n  return isAnInteractiveElement(parent, current.parentElement);\n}\nfunction isEventInInteractiveElement(draggable, event) {\n  const target = event.target;\n  if (!isHtmlElement(target)) {\n    return false;\n  }\n  return isAnInteractiveElement(draggable, target);\n}\n\nvar getBorderBoxCenterPosition = el => getRect(el.getBoundingClientRect()).center;\n\nfunction isElement(el) {\n  return el instanceof getWindowFromEl(el).Element;\n}\n\nconst supportedMatchesName = (() => {\n  const base = 'matches';\n  if (typeof document === 'undefined') {\n    return base;\n  }\n  const candidates = [base, 'msMatchesSelector', 'webkitMatchesSelector'];\n  const value = candidates.find(name => name in Element.prototype);\n  return value || base;\n})();\nfunction closestPonyfill(el, selector) {\n  if (el == null) {\n    return null;\n  }\n  if (el[supportedMatchesName](selector)) {\n    return el;\n  }\n  return closestPonyfill(el.parentElement, selector);\n}\nfunction closest(el, selector) {\n  if (el.closest) {\n    return el.closest(selector);\n  }\n  return closestPonyfill(el, selector);\n}\n\nfunction getSelector(contextId) {\n  return `[${dragHandle.contextId}=\"${contextId}\"]`;\n}\nfunction findClosestDragHandleFromEvent(contextId, event) {\n  const target = event.target;\n  if (!isElement(target)) {\n    process.env.NODE_ENV !== \"production\" ? warning('event.target must be a Element') : void 0;\n    return null;\n  }\n  const selector = getSelector(contextId);\n  const handle = closest(target, selector);\n  if (!handle) {\n    return null;\n  }\n  if (!isHtmlElement(handle)) {\n    process.env.NODE_ENV !== \"production\" ? warning('drag handle must be a HTMLElement') : void 0;\n    return null;\n  }\n  return handle;\n}\nfunction tryGetClosestDraggableIdFromEvent(contextId, event) {\n  const handle = findClosestDragHandleFromEvent(contextId, event);\n  if (!handle) {\n    return null;\n  }\n  return handle.getAttribute(dragHandle.draggableId);\n}\n\nfunction findDraggable(contextId, draggableId) {\n  const selector = `[${draggable.contextId}=\"${contextId}\"]`;\n  const possible = querySelectorAll(document, selector);\n  const draggable$1 = possible.find(el => {\n    return el.getAttribute(draggable.id) === draggableId;\n  });\n  if (!draggable$1) {\n    return null;\n  }\n  if (!isHtmlElement(draggable$1)) {\n    process.env.NODE_ENV !== \"production\" ? warning('Draggable element is not a HTMLElement') : void 0;\n    return null;\n  }\n  return draggable$1;\n}\n\nfunction preventDefault(event) {\n  event.preventDefault();\n}\nfunction isActive({\n  expected,\n  phase,\n  isLockActive,\n  shouldWarn\n}) {\n  if (!isLockActive()) {\n    if (shouldWarn) {\n      process.env.NODE_ENV !== \"production\" ? warning(`\n        Cannot perform action.\n        The sensor no longer has an action lock.\n\n        Tips:\n\n        - Throw away your action handlers when forceStop() is called\n        - Check actions.isActive() if you really need to\n      `) : void 0;\n    }\n    return false;\n  }\n  if (expected !== phase) {\n    if (shouldWarn) {\n      process.env.NODE_ENV !== \"production\" ? warning(`\n        Cannot perform action.\n        The actions you used belong to an outdated phase\n\n        Current phase: ${expected}\n        You called an action from outdated phase: ${phase}\n\n        Tips:\n\n        - Do not use preDragActions actions after calling preDragActions.lift()\n      `) : void 0;\n    }\n    return false;\n  }\n  return true;\n}\nfunction canStart({\n  lockAPI,\n  store,\n  registry,\n  draggableId\n}) {\n  if (lockAPI.isClaimed()) {\n    return false;\n  }\n  const entry = registry.draggable.findById(draggableId);\n  if (!entry) {\n    process.env.NODE_ENV !== \"production\" ? warning(`Unable to find draggable with id: ${draggableId}`) : void 0;\n    return false;\n  }\n  if (!entry.options.isEnabled) {\n    return false;\n  }\n  if (!canStartDrag(store.getState(), draggableId)) {\n    return false;\n  }\n  return true;\n}\nfunction tryStart({\n  lockAPI,\n  contextId,\n  store,\n  registry,\n  draggableId,\n  forceSensorStop,\n  sourceEvent\n}) {\n  const shouldStart = canStart({\n    lockAPI,\n    store,\n    registry,\n    draggableId\n  });\n  if (!shouldStart) {\n    return null;\n  }\n  const entry = registry.draggable.getById(draggableId);\n  const el = findDraggable(contextId, entry.descriptor.id);\n  if (!el) {\n    process.env.NODE_ENV !== \"production\" ? warning(`Unable to find draggable element with id: ${draggableId}`) : void 0;\n    return null;\n  }\n  if (sourceEvent && !entry.options.canDragInteractiveElements && isEventInInteractiveElement(el, sourceEvent)) {\n    return null;\n  }\n  const lock = lockAPI.claim(forceSensorStop || noop$2);\n  let phase = 'PRE_DRAG';\n  function getShouldRespectForcePress() {\n    return entry.options.shouldRespectForcePress;\n  }\n  function isLockActive() {\n    return lockAPI.isActive(lock);\n  }\n  function tryDispatch(expected, getAction) {\n    if (isActive({\n      expected,\n      phase,\n      isLockActive,\n      shouldWarn: true\n    })) {\n      store.dispatch(getAction());\n    }\n  }\n  const tryDispatchWhenDragging = tryDispatch.bind(null, 'DRAGGING');\n  function lift(args) {\n    function completed() {\n      lockAPI.release();\n      phase = 'COMPLETED';\n    }\n    if (phase !== 'PRE_DRAG') {\n      completed();\n      process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot lift in phase ${phase}`) : invariant() ;\n    }\n    store.dispatch(lift$1(args.liftActionArgs));\n    phase = 'DRAGGING';\n    function finish(reason, options = {\n      shouldBlockNextClick: false\n    }) {\n      args.cleanup();\n      if (options.shouldBlockNextClick) {\n        const unbind = bindEvents(window, [{\n          eventName: 'click',\n          fn: preventDefault,\n          options: {\n            once: true,\n            passive: false,\n            capture: true\n          }\n        }]);\n        setTimeout(unbind);\n      }\n      completed();\n      store.dispatch(drop({\n        reason\n      }));\n    }\n    return {\n      isActive: () => isActive({\n        expected: 'DRAGGING',\n        phase,\n        isLockActive,\n        shouldWarn: false\n      }),\n      shouldRespectForcePress: getShouldRespectForcePress,\n      drop: options => finish('DROP', options),\n      cancel: options => finish('CANCEL', options),\n      ...args.actions\n    };\n  }\n  function fluidLift(clientSelection) {\n    const move$1 = rafSchd(client => {\n      tryDispatchWhenDragging(() => move({\n        client\n      }));\n    });\n    const api = lift({\n      liftActionArgs: {\n        id: draggableId,\n        clientSelection,\n        movementMode: 'FLUID'\n      },\n      cleanup: () => move$1.cancel(),\n      actions: {\n        move: move$1\n      }\n    });\n    return {\n      ...api,\n      move: move$1\n    };\n  }\n  function snapLift() {\n    const actions = {\n      moveUp: () => tryDispatchWhenDragging(moveUp),\n      moveRight: () => tryDispatchWhenDragging(moveRight),\n      moveDown: () => tryDispatchWhenDragging(moveDown),\n      moveLeft: () => tryDispatchWhenDragging(moveLeft)\n    };\n    return lift({\n      liftActionArgs: {\n        id: draggableId,\n        clientSelection: getBorderBoxCenterPosition(el),\n        movementMode: 'SNAP'\n      },\n      cleanup: noop$2,\n      actions\n    });\n  }\n  function abortPreDrag() {\n    const shouldRelease = isActive({\n      expected: 'PRE_DRAG',\n      phase,\n      isLockActive,\n      shouldWarn: true\n    });\n    if (shouldRelease) {\n      lockAPI.release();\n    }\n  }\n  const preDrag = {\n    isActive: () => isActive({\n      expected: 'PRE_DRAG',\n      phase,\n      isLockActive,\n      shouldWarn: false\n    }),\n    shouldRespectForcePress: getShouldRespectForcePress,\n    fluidLift,\n    snapLift,\n    abort: abortPreDrag\n  };\n  return preDrag;\n}\nconst defaultSensors = [useMouseSensor, useKeyboardSensor, useTouchSensor];\nfunction useSensorMarshal({\n  contextId,\n  store,\n  registry,\n  customSensors,\n  enableDefaultSensors\n}) {\n  const useSensors = [...(enableDefaultSensors ? defaultSensors : []), ...(customSensors || [])];\n  const lockAPI = useState(() => create())[0];\n  const tryAbandonLock = useCallback(function tryAbandonLock(previous, current) {\n    if (isDragging(previous) && !isDragging(current)) {\n      lockAPI.tryAbandon();\n    }\n  }, [lockAPI]);\n  useIsomorphicLayoutEffect(function listenToStore() {\n    let previous = store.getState();\n    const unsubscribe = store.subscribe(() => {\n      const current = store.getState();\n      tryAbandonLock(previous, current);\n      previous = current;\n    });\n    return unsubscribe;\n  }, [lockAPI, store, tryAbandonLock]);\n  useIsomorphicLayoutEffect(() => {\n    return lockAPI.tryAbandon;\n  }, [lockAPI.tryAbandon]);\n  const canGetLock = useCallback(draggableId => {\n    return canStart({\n      lockAPI,\n      registry,\n      store,\n      draggableId\n    });\n  }, [lockAPI, registry, store]);\n  const tryGetLock = useCallback((draggableId, forceStop, options) => tryStart({\n    lockAPI,\n    registry,\n    contextId,\n    store,\n    draggableId,\n    forceSensorStop: forceStop || null,\n    sourceEvent: options && options.sourceEvent ? options.sourceEvent : null\n  }), [contextId, lockAPI, registry, store]);\n  const findClosestDraggableId = useCallback(event => tryGetClosestDraggableIdFromEvent(contextId, event), [contextId]);\n  const findOptionsForDraggable = useCallback(id => {\n    const entry = registry.draggable.findById(id);\n    return entry ? entry.options : null;\n  }, [registry.draggable]);\n  const tryReleaseLock = useCallback(function tryReleaseLock() {\n    if (!lockAPI.isClaimed()) {\n      return;\n    }\n    lockAPI.tryAbandon();\n    if (store.getState().phase !== 'IDLE') {\n      store.dispatch(flush());\n    }\n  }, [lockAPI, store]);\n  const isLockClaimed = useCallback(() => lockAPI.isClaimed(), [lockAPI]);\n  const api = useMemo(() => ({\n    canGetLock,\n    tryGetLock,\n    findClosestDraggableId,\n    findOptionsForDraggable,\n    tryReleaseLock,\n    isLockClaimed\n  }), [canGetLock, tryGetLock, findClosestDraggableId, findOptionsForDraggable, tryReleaseLock, isLockClaimed]);\n  useValidateSensorHooks(useSensors);\n  for (let i = 0; i < useSensors.length; i++) {\n    useSensors[i](api);\n  }\n}\n\nconst createResponders = props => ({\n  onBeforeCapture: t => {\n    const onBeforeCapureCallback = () => {\n      if (props.onBeforeCapture) {\n        props.onBeforeCapture(t);\n      }\n    };\n    flushSync(onBeforeCapureCallback);\n  },\n  onBeforeDragStart: props.onBeforeDragStart,\n  onDragStart: props.onDragStart,\n  onDragEnd: props.onDragEnd,\n  onDragUpdate: props.onDragUpdate\n});\nconst createAutoScrollerOptions = props => ({\n  ...defaultAutoScrollerOptions,\n  ...props.autoScrollerOptions,\n  durationDampening: {\n    ...defaultAutoScrollerOptions.durationDampening,\n    ...props.autoScrollerOptions\n  }\n});\nfunction getStore(lazyRef) {\n  !lazyRef.current ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find store from lazy ref') : invariant() : void 0;\n  return lazyRef.current;\n}\nfunction App(props) {\n  const {\n    contextId,\n    setCallbacks,\n    sensors,\n    nonce,\n    dragHandleUsageInstructions\n  } = props;\n  const lazyStoreRef = useRef(null);\n  useStartupValidation();\n  const lastPropsRef = usePrevious(props);\n  const getResponders = useCallback(() => {\n    return createResponders(lastPropsRef.current);\n  }, [lastPropsRef]);\n  const getAutoScrollerOptions = useCallback(() => {\n    return createAutoScrollerOptions(lastPropsRef.current);\n  }, [lastPropsRef]);\n  const announce = useAnnouncer(contextId);\n  const dragHandleUsageInstructionsId = useHiddenTextElement({\n    contextId,\n    text: dragHandleUsageInstructions\n  });\n  const styleMarshal = useStyleMarshal(contextId, nonce);\n  const lazyDispatch = useCallback(action => {\n    getStore(lazyStoreRef).dispatch(action);\n  }, []);\n  const marshalCallbacks = useMemo(() => bindActionCreators({\n    publishWhileDragging,\n    updateDroppableScroll,\n    updateDroppableIsEnabled,\n    updateDroppableIsCombineEnabled,\n    collectionStarting\n  }, lazyDispatch), [lazyDispatch]);\n  const registry = useRegistry();\n  const dimensionMarshal = useMemo(() => {\n    return createDimensionMarshal(registry, marshalCallbacks);\n  }, [registry, marshalCallbacks]);\n  const autoScroller = useMemo(() => createAutoScroller({\n    scrollWindow,\n    scrollDroppable: dimensionMarshal.scrollDroppable,\n    getAutoScrollerOptions,\n    ...bindActionCreators({\n      move\n    }, lazyDispatch)\n  }), [dimensionMarshal.scrollDroppable, lazyDispatch, getAutoScrollerOptions]);\n  const focusMarshal = useFocusMarshal(contextId);\n  const store = useMemo(() => createStore({\n    announce,\n    autoScroller,\n    dimensionMarshal,\n    focusMarshal,\n    getResponders,\n    styleMarshal\n  }), [announce, autoScroller, dimensionMarshal, focusMarshal, getResponders, styleMarshal]);\n  if (process.env.NODE_ENV !== 'production') {\n    if (lazyStoreRef.current && lazyStoreRef.current !== store) {\n      process.env.NODE_ENV !== \"production\" ? warning('unexpected store change') : void 0;\n    }\n  }\n  lazyStoreRef.current = store;\n  const tryResetStore = useCallback(() => {\n    const current = getStore(lazyStoreRef);\n    const state = current.getState();\n    if (state.phase !== 'IDLE') {\n      current.dispatch(flush());\n    }\n  }, []);\n  const isDragging = useCallback(() => {\n    const state = getStore(lazyStoreRef).getState();\n    if (state.phase === 'DROP_ANIMATING') {\n      return true;\n    }\n    if (state.phase === 'IDLE') {\n      return false;\n    }\n    return state.isDragging;\n  }, []);\n  const appCallbacks = useMemo(() => ({\n    isDragging,\n    tryAbort: tryResetStore\n  }), [isDragging, tryResetStore]);\n  setCallbacks(appCallbacks);\n  const getCanLift = useCallback(id => canStartDrag(getStore(lazyStoreRef).getState(), id), []);\n  const getIsMovementAllowed = useCallback(() => isMovementAllowed(getStore(lazyStoreRef).getState()), []);\n  const appContext = useMemo(() => ({\n    marshal: dimensionMarshal,\n    focus: focusMarshal,\n    contextId,\n    canLift: getCanLift,\n    isMovementAllowed: getIsMovementAllowed,\n    dragHandleUsageInstructionsId,\n    registry\n  }), [contextId, dimensionMarshal, dragHandleUsageInstructionsId, focusMarshal, getCanLift, getIsMovementAllowed, registry]);\n  useSensorMarshal({\n    contextId,\n    store,\n    registry,\n    customSensors: sensors || null,\n    enableDefaultSensors: props.enableDefaultSensors !== false\n  });\n  useEffect(() => {\n    return tryResetStore;\n  }, [tryResetStore]);\n  return React.createElement(AppContext.Provider, {\n    value: appContext\n  }, React.createElement(Provider, {\n    context: StoreContext,\n    store: store\n  }, props.children));\n}\n\nfunction useUniqueContextId() {\n  return React.useId();\n}\n\nfunction DragDropContext(props) {\n  const contextId = useUniqueContextId();\n  const dragHandleUsageInstructions = props.dragHandleUsageInstructions || preset.dragHandleUsageInstructions;\n  return React.createElement(ErrorBoundary, null, setCallbacks => React.createElement(App, {\n    nonce: props.nonce,\n    contextId: contextId,\n    setCallbacks: setCallbacks,\n    dragHandleUsageInstructions: dragHandleUsageInstructions,\n    enableDefaultSensors: props.enableDefaultSensors,\n    sensors: props.sensors,\n    onBeforeCapture: props.onBeforeCapture,\n    onBeforeDragStart: props.onBeforeDragStart,\n    onDragStart: props.onDragStart,\n    onDragUpdate: props.onDragUpdate,\n    onDragEnd: props.onDragEnd,\n    autoScrollerOptions: props.autoScrollerOptions\n  }, props.children));\n}\n\nconst zIndexOptions = {\n  dragging: 5000,\n  dropAnimating: 4500\n};\nconst getDraggingTransition = (shouldAnimateDragMovement, dropping) => {\n  if (dropping) {\n    return transitions.drop(dropping.duration);\n  }\n  if (shouldAnimateDragMovement) {\n    return transitions.snap;\n  }\n  return transitions.fluid;\n};\nconst getDraggingOpacity = (isCombining, isDropAnimating) => {\n  if (!isCombining) {\n    return undefined;\n  }\n  return isDropAnimating ? combine.opacity.drop : combine.opacity.combining;\n};\nconst getShouldDraggingAnimate = dragging => {\n  if (dragging.forceShouldAnimate != null) {\n    return dragging.forceShouldAnimate;\n  }\n  return dragging.mode === 'SNAP';\n};\nfunction getDraggingStyle(dragging) {\n  const dimension = dragging.dimension;\n  const box = dimension.client;\n  const {\n    offset,\n    combineWith,\n    dropping\n  } = dragging;\n  const isCombining = Boolean(combineWith);\n  const shouldAnimate = getShouldDraggingAnimate(dragging);\n  const isDropAnimating = Boolean(dropping);\n  const transform = isDropAnimating ? transforms.drop(offset, isCombining) : transforms.moveTo(offset);\n  const style = {\n    position: 'fixed',\n    top: box.marginBox.top,\n    left: box.marginBox.left,\n    boxSizing: 'border-box',\n    width: box.borderBox.width,\n    height: box.borderBox.height,\n    transition: getDraggingTransition(shouldAnimate, dropping),\n    transform,\n    opacity: getDraggingOpacity(isCombining, isDropAnimating),\n    zIndex: isDropAnimating ? zIndexOptions.dropAnimating : zIndexOptions.dragging,\n    pointerEvents: 'none'\n  };\n  return style;\n}\nfunction getSecondaryStyle(secondary) {\n  return {\n    transform: transforms.moveTo(secondary.offset),\n    transition: secondary.shouldAnimateDisplacement ? undefined : 'none'\n  };\n}\nfunction getStyle$1(mapped) {\n  return mapped.type === 'DRAGGING' ? getDraggingStyle(mapped) : getSecondaryStyle(mapped);\n}\n\nfunction getDimension$1(descriptor, el, windowScroll = origin) {\n  const computedStyles = window.getComputedStyle(el);\n  const borderBox = el.getBoundingClientRect();\n  const client = calculateBox(borderBox, computedStyles);\n  const page = withScroll(client, windowScroll);\n  const placeholder = {\n    client,\n    tagName: el.tagName.toLowerCase(),\n    display: computedStyles.display\n  };\n  const displaceBy = {\n    x: client.marginBox.width,\n    y: client.marginBox.height\n  };\n  const dimension = {\n    descriptor,\n    placeholder,\n    displaceBy,\n    client,\n    page\n  };\n  return dimension;\n}\n\nfunction useDraggablePublisher(args) {\n  const uniqueId = useUniqueId('draggable');\n  const {\n    descriptor,\n    registry,\n    getDraggableRef,\n    canDragInteractiveElements,\n    shouldRespectForcePress,\n    isEnabled\n  } = args;\n  const options = useMemo(() => ({\n    canDragInteractiveElements,\n    shouldRespectForcePress,\n    isEnabled\n  }), [canDragInteractiveElements, isEnabled, shouldRespectForcePress]);\n  const getDimension = useCallback(windowScroll => {\n    const el = getDraggableRef();\n    !el ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot get dimension when no ref is set') : invariant() : void 0;\n    return getDimension$1(descriptor, el, windowScroll);\n  }, [descriptor, getDraggableRef]);\n  const entry = useMemo(() => ({\n    uniqueId,\n    descriptor,\n    options,\n    getDimension\n  }), [descriptor, getDimension, options, uniqueId]);\n  const publishedRef = useRef(entry);\n  const isFirstPublishRef = useRef(true);\n  useIsomorphicLayoutEffect(() => {\n    registry.draggable.register(publishedRef.current);\n    return () => registry.draggable.unregister(publishedRef.current);\n  }, [registry.draggable]);\n  useIsomorphicLayoutEffect(() => {\n    if (isFirstPublishRef.current) {\n      isFirstPublishRef.current = false;\n      return;\n    }\n    const last = publishedRef.current;\n    publishedRef.current = entry;\n    registry.draggable.update(entry, last);\n  }, [entry, registry.draggable]);\n}\n\nvar DroppableContext = React.createContext(null);\n\nfunction checkIsValidInnerRef(el) {\n  !(el && isHtmlElement(el)) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `\n    provided.innerRef has not been provided with a HTMLElement.\n\n    You can find a guide on using the innerRef callback functions at:\n    https://github.com/hello-pangea/dnd/blob/main/docs/guides/using-inner-ref.md\n  `) : invariant() : void 0;\n}\n\nfunction useValidation$1(props, contextId, getRef) {\n  useDevSetupWarning(() => {\n    function prefix(id) {\n      return `Draggable[id: ${id}]: `;\n    }\n    const id = props.draggableId;\n    !id ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Draggable requires a draggableId') : invariant(false) : void 0;\n    !(typeof id === 'string') ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Draggable requires a [string] draggableId.\n      Provided: [type: ${typeof id}] (value: ${id})`) : invariant(false) : void 0;\n    !Number.isInteger(props.index) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `${prefix(id)} requires an integer index prop`) : invariant(false) : void 0;\n    if (props.mapped.type === 'DRAGGING') {\n      return;\n    }\n    checkIsValidInnerRef(getRef());\n    if (props.isEnabled) {\n      !findDragHandle(contextId, id) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `${prefix(id)} Unable to find drag handle`) : invariant(false) : void 0;\n    }\n  });\n}\nfunction useClonePropValidation(isClone) {\n  useDev(() => {\n    const initialRef = useRef(isClone);\n    useDevSetupWarning(() => {\n      !(isClone === initialRef.current) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Draggable isClone prop value changed during component life') : invariant(false) : void 0;\n    }, [isClone]);\n  });\n}\n\nfunction useRequiredContext(Context) {\n  const result = useContext(Context);\n  !result ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find required context') : invariant() : void 0;\n  return result;\n}\n\nfunction preventHtml5Dnd(event) {\n  event.preventDefault();\n}\nconst Draggable = props => {\n  const ref = useRef(null);\n  const setRef = useCallback((el = null) => {\n    ref.current = el;\n  }, []);\n  const getRef = useCallback(() => ref.current, []);\n  const {\n    contextId,\n    dragHandleUsageInstructionsId,\n    registry\n  } = useRequiredContext(AppContext);\n  const {\n    type,\n    droppableId\n  } = useRequiredContext(DroppableContext);\n  const descriptor = useMemo(() => ({\n    id: props.draggableId,\n    index: props.index,\n    type,\n    droppableId\n  }), [props.draggableId, props.index, type, droppableId]);\n  const {\n    children,\n    draggableId,\n    isEnabled,\n    shouldRespectForcePress,\n    canDragInteractiveElements,\n    isClone,\n    mapped,\n    dropAnimationFinished: dropAnimationFinishedAction\n  } = props;\n  useValidation$1(props, contextId, getRef);\n  useClonePropValidation(isClone);\n  if (!isClone) {\n    const forPublisher = useMemo(() => ({\n      descriptor,\n      registry,\n      getDraggableRef: getRef,\n      canDragInteractiveElements,\n      shouldRespectForcePress,\n      isEnabled\n    }), [descriptor, registry, getRef, canDragInteractiveElements, shouldRespectForcePress, isEnabled]);\n    useDraggablePublisher(forPublisher);\n  }\n  const dragHandleProps = useMemo(() => isEnabled ? {\n    tabIndex: 0,\n    role: 'button',\n    'aria-describedby': dragHandleUsageInstructionsId,\n    'data-rfd-drag-handle-draggable-id': draggableId,\n    'data-rfd-drag-handle-context-id': contextId,\n    draggable: false,\n    onDragStart: preventHtml5Dnd\n  } : null, [contextId, dragHandleUsageInstructionsId, draggableId, isEnabled]);\n  const onMoveEnd = useCallback(event => {\n    if (mapped.type !== 'DRAGGING') {\n      return;\n    }\n    if (!mapped.dropping) {\n      return;\n    }\n    if (event.propertyName !== 'transform') {\n      return;\n    }\n    flushSync(dropAnimationFinishedAction);\n  }, [dropAnimationFinishedAction, mapped]);\n  const provided = useMemo(() => {\n    const style = getStyle$1(mapped);\n    const onTransitionEnd = mapped.type === 'DRAGGING' && mapped.dropping ? onMoveEnd : undefined;\n    const result = {\n      innerRef: setRef,\n      draggableProps: {\n        'data-rfd-draggable-context-id': contextId,\n        'data-rfd-draggable-id': draggableId,\n        style,\n        onTransitionEnd\n      },\n      dragHandleProps\n    };\n    return result;\n  }, [contextId, dragHandleProps, draggableId, mapped, onMoveEnd, setRef]);\n  const rubric = useMemo(() => ({\n    draggableId: descriptor.id,\n    type: descriptor.type,\n    source: {\n      index: descriptor.index,\n      droppableId: descriptor.droppableId\n    }\n  }), [descriptor.droppableId, descriptor.id, descriptor.index, descriptor.type]);\n  return React.createElement(React.Fragment, null, children(provided, mapped.snapshot, rubric));\n};\n\nvar isStrictEqual = (a, b) => a === b;\n\nvar whatIsDraggedOverFromResult = result => {\n  const {\n    combine,\n    destination\n  } = result;\n  if (destination) {\n    return destination.droppableId;\n  }\n  if (combine) {\n    return combine.droppableId;\n  }\n  return null;\n};\n\nconst getCombineWithFromResult = result => {\n  return result.combine ? result.combine.draggableId : null;\n};\nconst getCombineWithFromImpact = impact => {\n  return impact.at && impact.at.type === 'COMBINE' ? impact.at.combine.draggableId : null;\n};\nfunction getDraggableSelector() {\n  const memoizedOffset = memoizeOne((x, y) => ({\n    x,\n    y\n  }));\n  const getMemoizedSnapshot = memoizeOne((mode, isClone, draggingOver = null, combineWith = null, dropping = null) => ({\n    isDragging: true,\n    isClone,\n    isDropAnimating: Boolean(dropping),\n    dropAnimation: dropping,\n    mode,\n    draggingOver,\n    combineWith,\n    combineTargetFor: null\n  }));\n  const getMemoizedProps = memoizeOne((offset, mode, dimension, isClone, draggingOver = null, combineWith = null, forceShouldAnimate = null) => ({\n    mapped: {\n      type: 'DRAGGING',\n      dropping: null,\n      draggingOver,\n      combineWith,\n      mode,\n      offset,\n      dimension,\n      forceShouldAnimate,\n      snapshot: getMemoizedSnapshot(mode, isClone, draggingOver, combineWith, null)\n    }\n  }));\n  const selector = (state, ownProps) => {\n    if (isDragging(state)) {\n      if (state.critical.draggable.id !== ownProps.draggableId) {\n        return null;\n      }\n      const offset = state.current.client.offset;\n      const dimension = state.dimensions.draggables[ownProps.draggableId];\n      const draggingOver = whatIsDraggedOver(state.impact);\n      const combineWith = getCombineWithFromImpact(state.impact);\n      const forceShouldAnimate = state.forceShouldAnimate;\n      return getMemoizedProps(memoizedOffset(offset.x, offset.y), state.movementMode, dimension, ownProps.isClone, draggingOver, combineWith, forceShouldAnimate);\n    }\n    if (state.phase === 'DROP_ANIMATING') {\n      const completed = state.completed;\n      if (completed.result.draggableId !== ownProps.draggableId) {\n        return null;\n      }\n      const isClone = ownProps.isClone;\n      const dimension = state.dimensions.draggables[ownProps.draggableId];\n      const result = completed.result;\n      const mode = result.mode;\n      const draggingOver = whatIsDraggedOverFromResult(result);\n      const combineWith = getCombineWithFromResult(result);\n      const duration = state.dropDuration;\n      const dropping = {\n        duration,\n        curve: curves.drop,\n        moveTo: state.newHomeClientOffset,\n        opacity: combineWith ? combine.opacity.drop : null,\n        scale: combineWith ? combine.scale.drop : null\n      };\n      return {\n        mapped: {\n          type: 'DRAGGING',\n          offset: state.newHomeClientOffset,\n          dimension,\n          dropping,\n          draggingOver,\n          combineWith,\n          mode,\n          forceShouldAnimate: null,\n          snapshot: getMemoizedSnapshot(mode, isClone, draggingOver, combineWith, dropping)\n        }\n      };\n    }\n    return null;\n  };\n  return selector;\n}\nfunction getSecondarySnapshot(combineTargetFor = null) {\n  return {\n    isDragging: false,\n    isDropAnimating: false,\n    isClone: false,\n    dropAnimation: null,\n    mode: null,\n    draggingOver: null,\n    combineTargetFor,\n    combineWith: null\n  };\n}\nconst atRest = {\n  mapped: {\n    type: 'SECONDARY',\n    offset: origin,\n    combineTargetFor: null,\n    shouldAnimateDisplacement: true,\n    snapshot: getSecondarySnapshot(null)\n  }\n};\nfunction getSecondarySelector() {\n  const memoizedOffset = memoizeOne((x, y) => ({\n    x,\n    y\n  }));\n  const getMemoizedSnapshot = memoizeOne(getSecondarySnapshot);\n  const getMemoizedProps = memoizeOne((offset, combineTargetFor = null, shouldAnimateDisplacement) => ({\n    mapped: {\n      type: 'SECONDARY',\n      offset,\n      combineTargetFor,\n      shouldAnimateDisplacement,\n      snapshot: getMemoizedSnapshot(combineTargetFor)\n    }\n  }));\n  const getFallback = combineTargetFor => {\n    return combineTargetFor ? getMemoizedProps(origin, combineTargetFor, true) : null;\n  };\n  const getProps = (ownId, draggingId, impact, afterCritical) => {\n    const visualDisplacement = impact.displaced.visible[ownId];\n    const isAfterCriticalInVirtualList = Boolean(afterCritical.inVirtualList && afterCritical.effected[ownId]);\n    const combine = tryGetCombine(impact);\n    const combineTargetFor = combine && combine.draggableId === ownId ? draggingId : null;\n    if (!visualDisplacement) {\n      if (!isAfterCriticalInVirtualList) {\n        return getFallback(combineTargetFor);\n      }\n      if (impact.displaced.invisible[ownId]) {\n        return null;\n      }\n      const change = negate(afterCritical.displacedBy.point);\n      const offset = memoizedOffset(change.x, change.y);\n      return getMemoizedProps(offset, combineTargetFor, true);\n    }\n    if (isAfterCriticalInVirtualList) {\n      return getFallback(combineTargetFor);\n    }\n    const displaceBy = impact.displacedBy.point;\n    const offset = memoizedOffset(displaceBy.x, displaceBy.y);\n    return getMemoizedProps(offset, combineTargetFor, visualDisplacement.shouldAnimate);\n  };\n  const selector = (state, ownProps) => {\n    if (isDragging(state)) {\n      if (state.critical.draggable.id === ownProps.draggableId) {\n        return null;\n      }\n      return getProps(ownProps.draggableId, state.critical.draggable.id, state.impact, state.afterCritical);\n    }\n    if (state.phase === 'DROP_ANIMATING') {\n      const completed = state.completed;\n      if (completed.result.draggableId === ownProps.draggableId) {\n        return null;\n      }\n      return getProps(ownProps.draggableId, completed.result.draggableId, completed.impact, completed.afterCritical);\n    }\n    return null;\n  };\n  return selector;\n}\nconst makeMapStateToProps$1 = () => {\n  const draggingSelector = getDraggableSelector();\n  const secondarySelector = getSecondarySelector();\n  const selector = (state, ownProps) => draggingSelector(state, ownProps) || secondarySelector(state, ownProps) || atRest;\n  return selector;\n};\nconst mapDispatchToProps$1 = {\n  dropAnimationFinished: dropAnimationFinished\n};\nconst ConnectedDraggable = connect(makeMapStateToProps$1, mapDispatchToProps$1, null, {\n  context: StoreContext,\n  areStatePropsEqual: isStrictEqual\n})(Draggable);\n\nfunction PrivateDraggable(props) {\n  const droppableContext = useRequiredContext(DroppableContext);\n  const isUsingCloneFor = droppableContext.isUsingCloneFor;\n  if (isUsingCloneFor === props.draggableId && !props.isClone) {\n    return null;\n  }\n  return React.createElement(ConnectedDraggable, props);\n}\nfunction PublicDraggable(props) {\n  const isEnabled = typeof props.isDragDisabled === 'boolean' ? !props.isDragDisabled : true;\n  const canDragInteractiveElements = Boolean(props.disableInteractiveElementBlocking);\n  const shouldRespectForcePress = Boolean(props.shouldRespectForcePress);\n  return React.createElement(PrivateDraggable, _extends({}, props, {\n    isClone: false,\n    isEnabled: isEnabled,\n    canDragInteractiveElements: canDragInteractiveElements,\n    shouldRespectForcePress: shouldRespectForcePress\n  }));\n}\n\nconst isEqual = base => value => base === value;\nconst isScroll = isEqual('scroll');\nconst isAuto = isEqual('auto');\nconst isVisible = isEqual('visible');\nconst isEither = (overflow, fn) => fn(overflow.overflowX) || fn(overflow.overflowY);\nconst isBoth = (overflow, fn) => fn(overflow.overflowX) && fn(overflow.overflowY);\nconst isElementScrollable = el => {\n  const style = window.getComputedStyle(el);\n  const overflow = {\n    overflowX: style.overflowX,\n    overflowY: style.overflowY\n  };\n  return isEither(overflow, isScroll) || isEither(overflow, isAuto);\n};\nconst isBodyScrollable = () => {\n  if (process.env.NODE_ENV === 'production') {\n    return false;\n  }\n  const body = getBodyElement();\n  const html = document.documentElement;\n  !html ? process.env.NODE_ENV !== \"production\" ? invariant() : invariant() : void 0;\n  if (!isElementScrollable(body)) {\n    return false;\n  }\n  const htmlStyle = window.getComputedStyle(html);\n  const htmlOverflow = {\n    overflowX: htmlStyle.overflowX,\n    overflowY: htmlStyle.overflowY\n  };\n  if (isBoth(htmlOverflow, isVisible)) {\n    return false;\n  }\n  process.env.NODE_ENV !== \"production\" ? warning(`\n    We have detected that your <body> element might be a scroll container.\n    We have found no reliable way of detecting whether the <body> element is a scroll container.\n    Under most circumstances a <body> scroll bar will be on the <html> element (document.documentElement)\n\n    Because we cannot determine if the <body> is a scroll container, and generally it is not one,\n    we will be treating the <body> as *not* a scroll container\n\n    More information: https://github.com/hello-pangea/dnd/blob/main/docs/guides/how-we-detect-scroll-containers.md\n  `) : void 0;\n  return false;\n};\nconst getClosestScrollable = el => {\n  if (el == null) {\n    return null;\n  }\n  if (el === document.body) {\n    return isBodyScrollable() ? el : null;\n  }\n  if (el === document.documentElement) {\n    return null;\n  }\n  if (!isElementScrollable(el)) {\n    return getClosestScrollable(el.parentElement);\n  }\n  return el;\n};\n\nvar checkForNestedScrollContainers = scrollable => {\n  if (!scrollable) {\n    return;\n  }\n  const anotherScrollParent = getClosestScrollable(scrollable.parentElement);\n  if (!anotherScrollParent) {\n    return;\n  }\n  process.env.NODE_ENV !== \"production\" ? warning(`\n    Droppable: unsupported nested scroll container detected.\n    A Droppable can only have one scroll parent (which can be itself)\n    Nested scroll containers are currently not supported.\n\n    We hope to support nested scroll containers soon: https://github.com/atlassian/react-beautiful-dnd/issues/131\n  `) : void 0;\n};\n\nvar getScroll = el => ({\n  x: el.scrollLeft,\n  y: el.scrollTop\n});\n\nconst getIsFixed = el => {\n  if (!el) {\n    return false;\n  }\n  const style = window.getComputedStyle(el);\n  if (style.position === 'fixed') {\n    return true;\n  }\n  return getIsFixed(el.parentElement);\n};\nvar getEnv = start => {\n  const closestScrollable = getClosestScrollable(start);\n  const isFixedOnPage = getIsFixed(start);\n  return {\n    closestScrollable,\n    isFixedOnPage\n  };\n};\n\nvar getDroppableDimension = ({\n  descriptor,\n  isEnabled,\n  isCombineEnabled,\n  isFixedOnPage,\n  direction,\n  client,\n  page,\n  closest\n}) => {\n  const frame = (() => {\n    if (!closest) {\n      return null;\n    }\n    const {\n      scrollSize,\n      client: frameClient\n    } = closest;\n    const maxScroll = getMaxScroll({\n      scrollHeight: scrollSize.scrollHeight,\n      scrollWidth: scrollSize.scrollWidth,\n      height: frameClient.paddingBox.height,\n      width: frameClient.paddingBox.width\n    });\n    return {\n      pageMarginBox: closest.page.marginBox,\n      frameClient,\n      scrollSize,\n      shouldClipSubject: closest.shouldClipSubject,\n      scroll: {\n        initial: closest.scroll,\n        current: closest.scroll,\n        max: maxScroll,\n        diff: {\n          value: origin,\n          displacement: origin\n        }\n      }\n    };\n  })();\n  const axis = direction === 'vertical' ? vertical : horizontal;\n  const subject = getSubject({\n    page,\n    withPlaceholder: null,\n    axis,\n    frame\n  });\n  const dimension = {\n    descriptor,\n    isCombineEnabled,\n    isFixedOnPage,\n    axis,\n    isEnabled,\n    client,\n    page,\n    frame,\n    subject\n  };\n  return dimension;\n};\n\nconst getClient = (targetRef, closestScrollable) => {\n  const base = getBox(targetRef);\n  if (!closestScrollable) {\n    return base;\n  }\n  if (targetRef !== closestScrollable) {\n    return base;\n  }\n  const top = base.paddingBox.top - closestScrollable.scrollTop;\n  const left = base.paddingBox.left - closestScrollable.scrollLeft;\n  const bottom = top + closestScrollable.scrollHeight;\n  const right = left + closestScrollable.scrollWidth;\n  const paddingBox = {\n    top,\n    right,\n    bottom,\n    left\n  };\n  const borderBox = expand(paddingBox, base.border);\n  const client = createBox({\n    borderBox,\n    margin: base.margin,\n    border: base.border,\n    padding: base.padding\n  });\n  return client;\n};\nvar getDimension = ({\n  ref,\n  descriptor,\n  env,\n  windowScroll,\n  direction,\n  isDropDisabled,\n  isCombineEnabled,\n  shouldClipSubject\n}) => {\n  const closestScrollable = env.closestScrollable;\n  const client = getClient(ref, closestScrollable);\n  const page = withScroll(client, windowScroll);\n  const closest = (() => {\n    if (!closestScrollable) {\n      return null;\n    }\n    const frameClient = getBox(closestScrollable);\n    const scrollSize = {\n      scrollHeight: closestScrollable.scrollHeight,\n      scrollWidth: closestScrollable.scrollWidth\n    };\n    return {\n      client: frameClient,\n      page: withScroll(frameClient, windowScroll),\n      scroll: getScroll(closestScrollable),\n      scrollSize,\n      shouldClipSubject\n    };\n  })();\n  const dimension = getDroppableDimension({\n    descriptor,\n    isEnabled: !isDropDisabled,\n    isCombineEnabled,\n    isFixedOnPage: env.isFixedOnPage,\n    direction,\n    client,\n    page,\n    closest\n  });\n  return dimension;\n};\n\nconst immediate = {\n  passive: false\n};\nconst delayed = {\n  passive: true\n};\nvar getListenerOptions = options => options.shouldPublishImmediately ? immediate : delayed;\n\nconst getClosestScrollableFromDrag = dragging => dragging && dragging.env.closestScrollable || null;\nfunction useDroppablePublisher(args) {\n  const whileDraggingRef = useRef(null);\n  const appContext = useRequiredContext(AppContext);\n  const uniqueId = useUniqueId('droppable');\n  const {\n    registry,\n    marshal\n  } = appContext;\n  const previousRef = usePrevious(args);\n  const descriptor = useMemo(() => ({\n    id: args.droppableId,\n    type: args.type,\n    mode: args.mode\n  }), [args.droppableId, args.mode, args.type]);\n  const publishedDescriptorRef = useRef(descriptor);\n  const memoizedUpdateScroll = useMemo(() => memoizeOne((x, y) => {\n    !whileDraggingRef.current ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Can only update scroll when dragging') : invariant() : void 0;\n    const scroll = {\n      x,\n      y\n    };\n    marshal.updateDroppableScroll(descriptor.id, scroll);\n  }), [descriptor.id, marshal]);\n  const getClosestScroll = useCallback(() => {\n    const dragging = whileDraggingRef.current;\n    if (!dragging || !dragging.env.closestScrollable) {\n      return origin;\n    }\n    return getScroll(dragging.env.closestScrollable);\n  }, []);\n  const updateScroll = useCallback(() => {\n    const scroll = getClosestScroll();\n    memoizedUpdateScroll(scroll.x, scroll.y);\n  }, [getClosestScroll, memoizedUpdateScroll]);\n  const scheduleScrollUpdate = useMemo(() => rafSchd(updateScroll), [updateScroll]);\n  const onClosestScroll = useCallback(() => {\n    const dragging = whileDraggingRef.current;\n    const closest = getClosestScrollableFromDrag(dragging);\n    !(dragging && closest) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find scroll options while scrolling') : invariant() : void 0;\n    const options = dragging.scrollOptions;\n    if (options.shouldPublishImmediately) {\n      updateScroll();\n      return;\n    }\n    scheduleScrollUpdate();\n  }, [scheduleScrollUpdate, updateScroll]);\n  const getDimensionAndWatchScroll = useCallback((windowScroll, options) => {\n    !!whileDraggingRef.current ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot collect a droppable while a drag is occurring') : invariant() : void 0;\n    const previous = previousRef.current;\n    const ref = previous.getDroppableRef();\n    !ref ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot collect without a droppable ref') : invariant() : void 0;\n    const env = getEnv(ref);\n    const dragging = {\n      ref,\n      descriptor,\n      env,\n      scrollOptions: options\n    };\n    whileDraggingRef.current = dragging;\n    const dimension = getDimension({\n      ref,\n      descriptor,\n      env,\n      windowScroll,\n      direction: previous.direction,\n      isDropDisabled: previous.isDropDisabled,\n      isCombineEnabled: previous.isCombineEnabled,\n      shouldClipSubject: !previous.ignoreContainerClipping\n    });\n    const scrollable = env.closestScrollable;\n    if (scrollable) {\n      scrollable.setAttribute(scrollContainer.contextId, appContext.contextId);\n      scrollable.addEventListener('scroll', onClosestScroll, getListenerOptions(dragging.scrollOptions));\n      if (process.env.NODE_ENV !== 'production') {\n        checkForNestedScrollContainers(scrollable);\n      }\n    }\n    return dimension;\n  }, [appContext.contextId, descriptor, onClosestScroll, previousRef]);\n  const getScrollWhileDragging = useCallback(() => {\n    const dragging = whileDraggingRef.current;\n    const closest = getClosestScrollableFromDrag(dragging);\n    !(dragging && closest) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Can only recollect Droppable client for Droppables that have a scroll container') : invariant() : void 0;\n    return getScroll(closest);\n  }, []);\n  const dragStopped = useCallback(() => {\n    const dragging = whileDraggingRef.current;\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot stop drag when no active drag') : invariant() : void 0;\n    const closest = getClosestScrollableFromDrag(dragging);\n    whileDraggingRef.current = null;\n    if (!closest) {\n      return;\n    }\n    scheduleScrollUpdate.cancel();\n    closest.removeAttribute(scrollContainer.contextId);\n    closest.removeEventListener('scroll', onClosestScroll, getListenerOptions(dragging.scrollOptions));\n  }, [onClosestScroll, scheduleScrollUpdate]);\n  const scroll = useCallback(change => {\n    const dragging = whileDraggingRef.current;\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot scroll when there is no drag') : invariant() : void 0;\n    const closest = getClosestScrollableFromDrag(dragging);\n    !closest ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot scroll a droppable with no closest scrollable') : invariant() : void 0;\n    closest.scrollTop += change.y;\n    closest.scrollLeft += change.x;\n  }, []);\n  const callbacks = useMemo(() => {\n    return {\n      getDimensionAndWatchScroll,\n      getScrollWhileDragging,\n      dragStopped,\n      scroll\n    };\n  }, [dragStopped, getDimensionAndWatchScroll, getScrollWhileDragging, scroll]);\n  const entry = useMemo(() => ({\n    uniqueId,\n    descriptor,\n    callbacks\n  }), [callbacks, descriptor, uniqueId]);\n  useIsomorphicLayoutEffect(() => {\n    publishedDescriptorRef.current = entry.descriptor;\n    registry.droppable.register(entry);\n    return () => {\n      if (whileDraggingRef.current) {\n        process.env.NODE_ENV !== \"production\" ? warning('Unsupported: changing the droppableId or type of a Droppable during a drag') : void 0;\n        dragStopped();\n      }\n      registry.droppable.unregister(entry);\n    };\n  }, [callbacks, descriptor, dragStopped, entry, marshal, registry.droppable]);\n  useIsomorphicLayoutEffect(() => {\n    if (!whileDraggingRef.current) {\n      return;\n    }\n    marshal.updateDroppableIsEnabled(publishedDescriptorRef.current.id, !args.isDropDisabled);\n  }, [args.isDropDisabled, marshal]);\n  useIsomorphicLayoutEffect(() => {\n    if (!whileDraggingRef.current) {\n      return;\n    }\n    marshal.updateDroppableIsCombineEnabled(publishedDescriptorRef.current.id, args.isCombineEnabled);\n  }, [args.isCombineEnabled, marshal]);\n}\n\nfunction noop() {}\nconst empty = {\n  width: 0,\n  height: 0,\n  margin: noSpacing\n};\nconst getSize = ({\n  isAnimatingOpenOnMount,\n  placeholder,\n  animate\n}) => {\n  if (isAnimatingOpenOnMount) {\n    return empty;\n  }\n  if (animate === 'close') {\n    return empty;\n  }\n  return {\n    height: placeholder.client.borderBox.height,\n    width: placeholder.client.borderBox.width,\n    margin: placeholder.client.margin\n  };\n};\nconst getStyle = ({\n  isAnimatingOpenOnMount,\n  placeholder,\n  animate\n}) => {\n  const size = getSize({\n    isAnimatingOpenOnMount,\n    placeholder,\n    animate\n  });\n  return {\n    display: placeholder.display,\n    boxSizing: 'border-box',\n    width: size.width,\n    height: size.height,\n    marginTop: size.margin.top,\n    marginRight: size.margin.right,\n    marginBottom: size.margin.bottom,\n    marginLeft: size.margin.left,\n    flexShrink: '0',\n    flexGrow: '0',\n    pointerEvents: 'none',\n    transition: animate !== 'none' ? transitions.placeholder : null\n  };\n};\nconst Placeholder = props => {\n  const animateOpenTimerRef = useRef(null);\n  const tryClearAnimateOpenTimer = useCallback(() => {\n    if (!animateOpenTimerRef.current) {\n      return;\n    }\n    clearTimeout(animateOpenTimerRef.current);\n    animateOpenTimerRef.current = null;\n  }, []);\n  const {\n    animate,\n    onTransitionEnd,\n    onClose,\n    contextId\n  } = props;\n  const [isAnimatingOpenOnMount, setIsAnimatingOpenOnMount] = useState(props.animate === 'open');\n  useEffect(() => {\n    if (!isAnimatingOpenOnMount) {\n      return noop;\n    }\n    if (animate !== 'open') {\n      tryClearAnimateOpenTimer();\n      setIsAnimatingOpenOnMount(false);\n      return noop;\n    }\n    if (animateOpenTimerRef.current) {\n      return noop;\n    }\n    animateOpenTimerRef.current = setTimeout(() => {\n      animateOpenTimerRef.current = null;\n      setIsAnimatingOpenOnMount(false);\n    });\n    return tryClearAnimateOpenTimer;\n  }, [animate, isAnimatingOpenOnMount, tryClearAnimateOpenTimer]);\n  const onSizeChangeEnd = useCallback(event => {\n    if (event.propertyName !== 'height') {\n      return;\n    }\n    onTransitionEnd();\n    if (animate === 'close') {\n      onClose();\n    }\n  }, [animate, onClose, onTransitionEnd]);\n  const style = getStyle({\n    isAnimatingOpenOnMount,\n    animate: props.animate,\n    placeholder: props.placeholder\n  });\n  return React.createElement(props.placeholder.tagName, {\n    style,\n    'data-rfd-placeholder-context-id': contextId,\n    onTransitionEnd: onSizeChangeEnd,\n    ref: props.innerRef\n  });\n};\nvar Placeholder$1 = React.memo(Placeholder);\n\nfunction isBoolean(value) {\n  return typeof value === 'boolean';\n}\nfunction runChecks(args, checks) {\n  checks.forEach(check => check(args));\n}\nconst shared = [function required({\n  props\n}) {\n  !props.droppableId ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'A Droppable requires a droppableId prop') : invariant() : void 0;\n  !(typeof props.droppableId === 'string') ? process.env.NODE_ENV !== \"production\" ? invariant(false, `A Droppable requires a [string] droppableId. Provided: [${typeof props.droppableId}]`) : invariant() : void 0;\n}, function boolean({\n  props\n}) {\n  !isBoolean(props.isDropDisabled) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'isDropDisabled must be a boolean') : invariant() : void 0;\n  !isBoolean(props.isCombineEnabled) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'isCombineEnabled must be a boolean') : invariant() : void 0;\n  !isBoolean(props.ignoreContainerClipping) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'ignoreContainerClipping must be a boolean') : invariant() : void 0;\n}, function ref({\n  getDroppableRef\n}) {\n  checkIsValidInnerRef(getDroppableRef());\n}];\nconst standard = [function placeholder({\n  props,\n  getPlaceholderRef\n}) {\n  if (!props.placeholder) {\n    return;\n  }\n  const ref = getPlaceholderRef();\n  if (ref) {\n    return;\n  }\n  process.env.NODE_ENV !== \"production\" ? warning(`\n      Droppable setup issue [droppableId: \"${props.droppableId}\"]:\n      DroppableProvided > placeholder could not be found.\n\n      Please be sure to add the {provided.placeholder} React Node as a child of your Droppable.\n      More information: https://github.com/hello-pangea/dnd/blob/main/docs/api/droppable.md\n    `) : void 0;\n}];\nconst virtual = [function hasClone({\n  props\n}) {\n  !props.renderClone ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Must provide a clone render function (renderClone) for virtual lists') : invariant() : void 0;\n}, function hasNoPlaceholder({\n  getPlaceholderRef\n}) {\n  !!getPlaceholderRef() ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected virtual list to not have a placeholder') : invariant() : void 0;\n}];\nfunction useValidation(args) {\n  useDevSetupWarning(() => {\n    runChecks(args, shared);\n    if (args.props.mode === 'standard') {\n      runChecks(args, standard);\n    }\n    if (args.props.mode === 'virtual') {\n      runChecks(args, virtual);\n    }\n  });\n}\n\nclass AnimateInOut extends React.PureComponent {\n  constructor(...args) {\n    super(...args);\n    this.state = {\n      isVisible: Boolean(this.props.on),\n      data: this.props.on,\n      animate: this.props.shouldAnimate && this.props.on ? 'open' : 'none'\n    };\n    this.onClose = () => {\n      if (this.state.animate !== 'close') {\n        return;\n      }\n      this.setState({\n        isVisible: false\n      });\n    };\n  }\n  static getDerivedStateFromProps(props, state) {\n    if (!props.shouldAnimate) {\n      return {\n        isVisible: Boolean(props.on),\n        data: props.on,\n        animate: 'none'\n      };\n    }\n    if (props.on) {\n      return {\n        isVisible: true,\n        data: props.on,\n        animate: 'open'\n      };\n    }\n    if (state.isVisible) {\n      return {\n        isVisible: true,\n        data: state.data,\n        animate: 'close'\n      };\n    }\n    return {\n      isVisible: false,\n      animate: 'close',\n      data: null\n    };\n  }\n  render() {\n    if (!this.state.isVisible) {\n      return null;\n    }\n    const provided = {\n      onClose: this.onClose,\n      data: this.state.data,\n      animate: this.state.animate\n    };\n    return this.props.children(provided);\n  }\n}\n\nconst Droppable = props => {\n  const appContext = useContext(AppContext);\n  !appContext ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find app context') : invariant() : void 0;\n  const {\n    contextId,\n    isMovementAllowed\n  } = appContext;\n  const droppableRef = useRef(null);\n  const placeholderRef = useRef(null);\n  const {\n    children,\n    droppableId,\n    type,\n    mode,\n    direction,\n    ignoreContainerClipping,\n    isDropDisabled,\n    isCombineEnabled,\n    snapshot,\n    useClone,\n    updateViewportMaxScroll,\n    getContainerForClone\n  } = props;\n  const getDroppableRef = useCallback(() => droppableRef.current, []);\n  const setDroppableRef = useCallback((value = null) => {\n    droppableRef.current = value;\n  }, []);\n  const getPlaceholderRef = useCallback(() => placeholderRef.current, []);\n  const setPlaceholderRef = useCallback((value = null) => {\n    placeholderRef.current = value;\n  }, []);\n  useValidation({\n    props,\n    getDroppableRef,\n    getPlaceholderRef\n  });\n  const onPlaceholderTransitionEnd = useCallback(() => {\n    if (isMovementAllowed()) {\n      updateViewportMaxScroll({\n        maxScroll: getMaxWindowScroll()\n      });\n    }\n  }, [isMovementAllowed, updateViewportMaxScroll]);\n  useDroppablePublisher({\n    droppableId,\n    type,\n    mode,\n    direction,\n    isDropDisabled,\n    isCombineEnabled,\n    ignoreContainerClipping,\n    getDroppableRef\n  });\n  const placeholder = useMemo(() => React.createElement(AnimateInOut, {\n    on: props.placeholder,\n    shouldAnimate: props.shouldAnimatePlaceholder\n  }, ({\n    onClose,\n    data,\n    animate\n  }) => React.createElement(Placeholder$1, {\n    placeholder: data,\n    onClose: onClose,\n    innerRef: setPlaceholderRef,\n    animate: animate,\n    contextId: contextId,\n    onTransitionEnd: onPlaceholderTransitionEnd\n  })), [contextId, onPlaceholderTransitionEnd, props.placeholder, props.shouldAnimatePlaceholder, setPlaceholderRef]);\n  const provided = useMemo(() => ({\n    innerRef: setDroppableRef,\n    placeholder,\n    droppableProps: {\n      'data-rfd-droppable-id': droppableId,\n      'data-rfd-droppable-context-id': contextId\n    }\n  }), [contextId, droppableId, placeholder, setDroppableRef]);\n  const isUsingCloneFor = useClone ? useClone.dragging.draggableId : null;\n  const droppableContext = useMemo(() => ({\n    droppableId,\n    type,\n    isUsingCloneFor\n  }), [droppableId, isUsingCloneFor, type]);\n  function getClone() {\n    if (!useClone) {\n      return null;\n    }\n    const {\n      dragging,\n      render\n    } = useClone;\n    const node = React.createElement(PrivateDraggable, {\n      draggableId: dragging.draggableId,\n      index: dragging.source.index,\n      isClone: true,\n      isEnabled: true,\n      shouldRespectForcePress: false,\n      canDragInteractiveElements: true\n    }, (draggableProvided, draggableSnapshot) => render(draggableProvided, draggableSnapshot, dragging));\n    return ReactDOM.createPortal(node, getContainerForClone());\n  }\n  return React.createElement(DroppableContext.Provider, {\n    value: droppableContext\n  }, children(provided, snapshot), getClone());\n};\n\nfunction getBody() {\n  !document.body ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'document.body is not ready') : invariant() : void 0;\n  return document.body;\n}\nconst defaultProps = {\n  mode: 'standard',\n  type: 'DEFAULT',\n  direction: 'vertical',\n  isDropDisabled: false,\n  isCombineEnabled: false,\n  ignoreContainerClipping: false,\n  renderClone: null,\n  getContainerForClone: getBody\n};\nconst attachDefaultPropsToOwnProps = ownProps => {\n  let mergedProps = {\n    ...ownProps\n  };\n  let defaultPropKey;\n  for (defaultPropKey in defaultProps) {\n    if (ownProps[defaultPropKey] === undefined) {\n      mergedProps = {\n        ...mergedProps,\n        [defaultPropKey]: defaultProps[defaultPropKey]\n      };\n    }\n  }\n  return mergedProps;\n};\nconst isMatchingType = (type, critical) => type === critical.droppable.type;\nconst getDraggable = (critical, dimensions) => dimensions.draggables[critical.draggable.id];\nconst makeMapStateToProps = () => {\n  const idleWithAnimation = {\n    placeholder: null,\n    shouldAnimatePlaceholder: true,\n    snapshot: {\n      isDraggingOver: false,\n      draggingOverWith: null,\n      draggingFromThisWith: null,\n      isUsingPlaceholder: false\n    },\n    useClone: null\n  };\n  const idleWithoutAnimation = {\n    ...idleWithAnimation,\n    shouldAnimatePlaceholder: false\n  };\n  const getDraggableRubric = memoizeOne(descriptor => ({\n    draggableId: descriptor.id,\n    type: descriptor.type,\n    source: {\n      index: descriptor.index,\n      droppableId: descriptor.droppableId\n    }\n  }));\n  const getMapProps = memoizeOne((id, isEnabled, isDraggingOverForConsumer, isDraggingOverForImpact, dragging, renderClone) => {\n    const draggableId = dragging.descriptor.id;\n    const isHome = dragging.descriptor.droppableId === id;\n    if (isHome) {\n      const useClone = renderClone ? {\n        render: renderClone,\n        dragging: getDraggableRubric(dragging.descriptor)\n      } : null;\n      const snapshot = {\n        isDraggingOver: isDraggingOverForConsumer,\n        draggingOverWith: isDraggingOverForConsumer ? draggableId : null,\n        draggingFromThisWith: draggableId,\n        isUsingPlaceholder: true\n      };\n      return {\n        placeholder: dragging.placeholder,\n        shouldAnimatePlaceholder: false,\n        snapshot,\n        useClone\n      };\n    }\n    if (!isEnabled) {\n      return idleWithoutAnimation;\n    }\n    if (!isDraggingOverForImpact) {\n      return idleWithAnimation;\n    }\n    const snapshot = {\n      isDraggingOver: isDraggingOverForConsumer,\n      draggingOverWith: draggableId,\n      draggingFromThisWith: null,\n      isUsingPlaceholder: true\n    };\n    return {\n      placeholder: dragging.placeholder,\n      shouldAnimatePlaceholder: true,\n      snapshot,\n      useClone: null\n    };\n  });\n  const selector = (state, ownProps) => {\n    const ownPropsWithDefaultProps = attachDefaultPropsToOwnProps(ownProps);\n    const id = ownPropsWithDefaultProps.droppableId;\n    const type = ownPropsWithDefaultProps.type;\n    const isEnabled = !ownPropsWithDefaultProps.isDropDisabled;\n    const renderClone = ownPropsWithDefaultProps.renderClone;\n    if (isDragging(state)) {\n      const critical = state.critical;\n      if (!isMatchingType(type, critical)) {\n        return idleWithoutAnimation;\n      }\n      const dragging = getDraggable(critical, state.dimensions);\n      const isDraggingOver = whatIsDraggedOver(state.impact) === id;\n      return getMapProps(id, isEnabled, isDraggingOver, isDraggingOver, dragging, renderClone);\n    }\n    if (state.phase === 'DROP_ANIMATING') {\n      const completed = state.completed;\n      if (!isMatchingType(type, completed.critical)) {\n        return idleWithoutAnimation;\n      }\n      const dragging = getDraggable(completed.critical, state.dimensions);\n      return getMapProps(id, isEnabled, whatIsDraggedOverFromResult(completed.result) === id, whatIsDraggedOver(completed.impact) === id, dragging, renderClone);\n    }\n    if (state.phase === 'IDLE' && state.completed && !state.shouldFlush) {\n      const completed = state.completed;\n      if (!isMatchingType(type, completed.critical)) {\n        return idleWithoutAnimation;\n      }\n      const wasOver = whatIsDraggedOver(completed.impact) === id;\n      const wasCombining = Boolean(completed.impact.at && completed.impact.at.type === 'COMBINE');\n      const isHome = completed.critical.droppable.id === id;\n      if (wasOver) {\n        return wasCombining ? idleWithAnimation : idleWithoutAnimation;\n      }\n      if (isHome) {\n        return idleWithAnimation;\n      }\n      return idleWithoutAnimation;\n    }\n    return idleWithoutAnimation;\n  };\n  return selector;\n};\nconst mapDispatchToProps = {\n  updateViewportMaxScroll: updateViewportMaxScroll\n};\nconst ConnectedDroppable = connect(makeMapStateToProps, mapDispatchToProps, (stateProps, dispatchProps, ownProps) => {\n  return {\n    ...attachDefaultPropsToOwnProps(ownProps),\n    ...stateProps,\n    ...dispatchProps\n  };\n}, {\n  context: StoreContext,\n  areStatePropsEqual: isStrictEqual\n})(Droppable);\n\nexport { DragDropContext, PublicDraggable as Draggable, ConnectedDroppable as Droppable, useKeyboardSensor, useMouseSensor, useTouchSensor };\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,MAAM,iBAAiB,oDAAyB;AAChD,MAAM,gBAAgB;AACtB,MAAM,sBAAsB;AAC5B,MAAM,UAAU,CAAA,QAAS,MAAM,OAAO,CAAC,eAAe,KAAK,OAAO,CAAC,qBAAqB,IAAI,IAAI;AAChG,MAAM,gBAAgB,CAAA,UAAW,QAAQ,CAAC;;;IAGtC,EAAE,QAAQ,SAAS;;;AAGvB,CAAC;AACD,MAAM,sBAAsB,CAAA,UAAW;QAAC,cAAc;QAAU;QAAwD;QAAoB;KAAkB;AAC9J,MAAM,iBAAiB;AACvB,SAAS,IAAI,IAAI,EAAE,OAAO;IACxB,uCAAoB;;IAEpB;IACA,IAAI,OAAO,WAAW,eAAe,MAAM,CAAC,eAAe,EAAE;QAC3D;IACF;IACA,OAAO,CAAC,KAAK,IAAI,oBAAoB;AACvC;AACA,MAAM,UAAU,IAAI,IAAI,CAAC,MAAM;AAC/B,MAAM,QAAQ,IAAI,IAAI,CAAC,MAAM;AAE7B,SAAS,UAAU;AAEnB,SAAS,WAAW,MAAM,EAAE,WAAW;IACrC,OAAO;QACL,GAAG,MAAM;QACT,GAAG,WAAW;IAChB;AACF;AACA,SAAS,WAAW,EAAE,EAAE,QAAQ,EAAE,aAAa;IAC7C,MAAM,aAAa,SAAS,GAAG,CAAC,CAAA;QAC9B,MAAM,UAAU,WAAW,eAAe,QAAQ,OAAO;QACzD,GAAG,gBAAgB,CAAC,QAAQ,SAAS,EAAE,QAAQ,EAAE,EAAE;QACnD,OAAO,SAAS;YACd,GAAG,mBAAmB,CAAC,QAAQ,SAAS,EAAE,QAAQ,EAAE,EAAE;QACxD;IACF;IACA,OAAO,SAAS;QACd,WAAW,OAAO,CAAC,CAAA;YACjB;QACF;IACF;AACF;AAEA,MAAM,eAAe,oDAAyB;AAC9C,MAAM,WAAW;AACjB,MAAM,qBAAqB;AAAO;AAClC,aAAa,SAAS,CAAC,QAAQ,GAAG,SAAS;IACzC,OAAO,IAAI,CAAC,OAAO;AACrB;AACA,SAAS,UAAU,SAAS,EAAE,OAAO;IACnC,uCAAkB;;IAElB,OAAO;QACL,MAAM,IAAI,aAAa,GAAG,SAAS,EAAE,EAAE,WAAW,IAAI;IACxD;AACF;AAEA,MAAM,sBAAsB,qMAAA,CAAA,UAAK,CAAC,SAAS;IACzC,YAAY,GAAG,IAAI,CAAE;QACnB,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,aAAa,GAAG,CAAA;YACnB,MAAM,YAAY,IAAI,CAAC,YAAY;YACnC,IAAI,UAAU,UAAU,IAAI;gBAC1B,UAAU,QAAQ;gBAClB,uCAAwC,QAAQ,CAAC;;;MAGnD,CAAC;YACD;YACA,MAAM,MAAM,MAAM,KAAK;YACvB,IAAI,eAAe,cAAc;gBAC/B,MAAM,cAAc;gBACpB,wCAA2C;oBACzC,MAAM,IAAI,OAAO;gBACnB;YACF;QACF;QACA,IAAI,CAAC,YAAY,GAAG;YAClB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACnB,MAAM,IAAI,MAAM;YAClB;YACA,OAAO,IAAI,CAAC,SAAS;QACvB;QACA,IAAI,CAAC,YAAY,GAAG,CAAA;YAClB,IAAI,CAAC,SAAS,GAAG;QACnB;IACF;IACA,oBAAoB;QAClB,IAAI,CAAC,MAAM,GAAG,WAAW,QAAQ;YAAC;gBAChC,WAAW;gBACX,IAAI,IAAI,CAAC,aAAa;YACxB;SAAE;IACJ;IACA,kBAAkB,GAAG,EAAE;QACrB,IAAI,eAAe,cAAc;YAC/B,wCAA2C;gBACzC,MAAM,IAAI,OAAO;YACnB;YACA,IAAI,CAAC,QAAQ,CAAC,CAAC;YACf;QACF;QACA,MAAM;IACR;IACA,uBAAuB;QACrB,IAAI,CAAC,MAAM;IACb;IACA,SAAS;QACP,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY;IAC9C;AACF;AAEA,MAAM,8BAA8B,CAAC;;;;AAIrC,CAAC;AACD,MAAM,WAAW,CAAA,QAAS,QAAQ;AAClC,MAAM,cAAc,CAAA,QAAS,CAAC;sCACQ,EAAE,SAAS,MAAM,MAAM,CAAC,KAAK,EAAE;AACrE,CAAC;AACD,MAAM,eAAe,CAAC,QAAQ;IAC5B,MAAM,eAAe,OAAO,WAAW,KAAK,YAAY,WAAW;IACnE,MAAM,gBAAgB,SAAS,OAAO,KAAK;IAC3C,MAAM,cAAc,SAAS,YAAY,KAAK;IAC9C,IAAI,cAAc;QAChB,OAAO,CAAC;4CACgC,EAAE,cAAc;kBAC1C,EAAE,YAAY;IAC5B,CAAC;IACH;IACA,OAAO,CAAC;0CACgC,EAAE,cAAc;YAC9C,EAAE,OAAO,WAAW,CAAC;YACrB,EAAE,YAAY,WAAW,CAAC;gBACtB,EAAE,YAAY;EAC5B,CAAC;AACH;AACA,MAAM,cAAc,CAAC,IAAI,QAAQ;IAC/B,MAAM,aAAa,OAAO,WAAW,KAAK,QAAQ,WAAW;IAC7D,IAAI,YAAY;QACd,OAAO,CAAC;eACG,EAAE,GAAG;6BACS,EAAE,QAAQ,WAAW,EAAE;IAClD;IACA,OAAO,CAAC;eACK,EAAE,GAAG;cACN,EAAE,OAAO,WAAW,CAAC;6BACN,EAAE,QAAQ,WAAW,CAAC;cACrC,EAAE,QAAQ,WAAW,CAAC;IAChC,CAAC;AACL;AACA,MAAM,eAAe,CAAA;IACnB,MAAM,WAAW,OAAO,WAAW;IACnC,IAAI,UAAU;QACZ,OAAO,aAAa,OAAO,MAAM,EAAE;IACrC;IACA,MAAM,UAAU,OAAO,OAAO;IAC9B,IAAI,SAAS;QACX,OAAO,YAAY,OAAO,WAAW,EAAE,OAAO,MAAM,EAAE;IACxD;IACA,OAAO;AACT;AACA,MAAM,kBAAkB,CAAA,SAAU,CAAC;;KAE9B,EAAE,SAAS,OAAO,KAAK,EAAE;AAC9B,CAAC;AACD,MAAM,YAAY,CAAA;IAChB,IAAI,OAAO,MAAM,KAAK,UAAU;QAC9B,OAAO,CAAC;;MAEN,EAAE,gBAAgB,OAAO,MAAM,EAAE;IACnC,CAAC;IACH;IACA,MAAM,WAAW,OAAO,WAAW;IACnC,MAAM,UAAU,OAAO,OAAO;IAC9B,IAAI,UAAU;QACZ,OAAO,CAAC;;MAEN,EAAE,aAAa,OAAO,MAAM,EAAE,UAAU;IAC1C,CAAC;IACH;IACA,IAAI,SAAS;QACX,OAAO,CAAC;;MAEN,EAAE,YAAY,OAAO,WAAW,EAAE,OAAO,MAAM,EAAE,SAAS;IAC5D,CAAC;IACH;IACA,OAAO,CAAC;;IAEN,EAAE,gBAAgB,OAAO,MAAM,EAAE;EACnC,CAAC;AACH;AACA,MAAM,SAAS;IACb;IACA;IACA;IACA;AACF;AAEA,SAAS,UAAU,KAAK,EAAE,MAAM;IAC9B,IAAI,UAAU,QAAQ;QACpB,OAAO;IACT;IACA,IAAI,OAAO,KAAK,CAAC,UAAU,OAAO,KAAK,CAAC,SAAS;QAC/C,OAAO;IACT;IACA,OAAO;AACT;AACA,SAAS,eAAe,SAAS,EAAE,UAAU;IAC3C,IAAI,UAAU,MAAM,KAAK,WAAW,MAAM,EAAE;QAC1C,OAAO;IACT;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QACzC,IAAI,CAAC,UAAU,SAAS,CAAC,EAAE,EAAE,UAAU,CAAC,EAAE,GAAG;YAC3C,OAAO;QACT;IACF;IACA,OAAO;AACT;AAEA,SAAS,QAAQ,SAAS,EAAE,MAAM;IAChC,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAM,CAAC;YAC9B;YACA,QAAQ;QACV,CAAC,EAAE,CAAC,EAAE;IACN,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,WAAW,WAAW,OAAO,IAAI,QAAQ,UAAU,UAAU,OAAO,CAAC,MAAM,IAAI,eAAe,QAAQ,UAAU,OAAO,CAAC,MAAM;IACpI,MAAM,QAAQ,WAAW,UAAU,OAAO,GAAG;QAC3C;QACA,QAAQ;IACV;IACA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW,OAAO,GAAG;QACrB,UAAU,OAAO,GAAG;IACtB,GAAG;QAAC;KAAM;IACV,OAAO,MAAM,MAAM;AACrB;AACA,SAAS,YAAY,QAAQ,EAAE,MAAM;IACnC,OAAO,QAAQ,IAAM,UAAU;AACjC;AAEA,MAAM,SAAS;IACb,GAAG;IACH,GAAG;AACL;AACA,MAAM,MAAM,CAAC,QAAQ,SAAW,CAAC;QAC/B,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC;QACtB,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC;IACxB,CAAC;AACD,MAAM,WAAW,CAAC,QAAQ,SAAW,CAAC;QACpC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC;QACtB,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC;IACxB,CAAC;AACD,MAAM,YAAY,CAAC,QAAQ,SAAW,OAAO,CAAC,KAAK,OAAO,CAAC,IAAI,OAAO,CAAC,KAAK,OAAO,CAAC;AACpF,MAAM,SAAS,CAAA,QAAS,CAAC;QACvB,GAAG,MAAM,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG;QAC9B,GAAG,MAAM,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG;IAChC,CAAC;AACD,MAAM,QAAQ,CAAC,MAAM,OAAO,aAAa,CAAC;IACxC,IAAI,SAAS,KAAK;QAChB,OAAO;YACL,GAAG;YACH,GAAG;QACL;IACF;IACA,OAAO;QACL,GAAG;QACH,GAAG;IACL;AACF;AACA,MAAM,WAAW,CAAC,QAAQ,SAAW,KAAK,IAAI,CAAC,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,KAAK;AACrG,MAAM,YAAY,CAAC,QAAQ,SAAW,KAAK,GAAG,IAAI,OAAO,GAAG,CAAC,CAAA,QAAS,SAAS,QAAQ;AACvF,MAAM,QAAQ,CAAA,KAAM,CAAA,QAAS,CAAC;YAC5B,GAAG,GAAG,MAAM,CAAC;YACb,GAAG,GAAG,MAAM,CAAC;QACf,CAAC;AAED,IAAI,cAAc,CAAC,OAAO;IACxB,MAAM,SAAS,CAAA,GAAA,yKAAA,CAAA,UAAO,AAAD,EAAE;QACrB,KAAK,KAAK,GAAG,CAAC,QAAQ,GAAG,EAAE,MAAM,GAAG;QACpC,OAAO,KAAK,GAAG,CAAC,QAAQ,KAAK,EAAE,MAAM,KAAK;QAC1C,QAAQ,KAAK,GAAG,CAAC,QAAQ,MAAM,EAAE,MAAM,MAAM;QAC7C,MAAM,KAAK,GAAG,CAAC,QAAQ,IAAI,EAAE,MAAM,IAAI;IACzC;IACA,IAAI,OAAO,KAAK,IAAI,KAAK,OAAO,MAAM,IAAI,GAAG;QAC3C,OAAO;IACT;IACA,OAAO;AACT;AAEA,MAAM,mBAAmB,CAAC,SAAS,QAAU,CAAC;QAC5C,KAAK,QAAQ,GAAG,GAAG,MAAM,CAAC;QAC1B,MAAM,QAAQ,IAAI,GAAG,MAAM,CAAC;QAC5B,QAAQ,QAAQ,MAAM,GAAG,MAAM,CAAC;QAChC,OAAO,QAAQ,KAAK,GAAG,MAAM,CAAC;IAChC,CAAC;AACD,MAAM,aAAa,CAAA,UAAW;QAAC;YAC7B,GAAG,QAAQ,IAAI;YACf,GAAG,QAAQ,GAAG;QAChB;QAAG;YACD,GAAG,QAAQ,KAAK;YAChB,GAAG,QAAQ,GAAG;QAChB;QAAG;YACD,GAAG,QAAQ,IAAI;YACf,GAAG,QAAQ,MAAM;QACnB;QAAG;YACD,GAAG,QAAQ,KAAK;YAChB,GAAG,QAAQ,MAAM;QACnB;KAAE;AACF,MAAM,YAAY;IAChB,KAAK;IACL,OAAO;IACP,QAAQ;IACR,MAAM;AACR;AAEA,MAAM,WAAW,CAAC,QAAQ;IACxB,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IACA,OAAO,iBAAiB,QAAQ,MAAM,MAAM,CAAC,IAAI,CAAC,YAAY;AAChE;AACA,MAAM,WAAW,CAAC,QAAQ,MAAM;IAC9B,IAAI,mBAAmB,gBAAgB,WAAW,EAAE;QAClD,OAAO;YACL,GAAG,MAAM;YACT,CAAC,KAAK,GAAG,CAAC,EAAE,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,gBAAgB,WAAW,CAAC,KAAK,IAAI,CAAC;QACvE;IACF;IACA,OAAO;AACT;AACA,MAAM,OAAO,CAAC,QAAQ;IACpB,IAAI,SAAS,MAAM,iBAAiB,EAAE;QACpC,OAAO,YAAY,MAAM,aAAa,EAAE;IAC1C;IACA,OAAO,CAAA,GAAA,yKAAA,CAAA,UAAO,AAAD,EAAE;AACjB;AACA,IAAI,aAAa,CAAC,EAChB,IAAI,EACJ,eAAe,EACf,IAAI,EACJ,KAAK,EACN;IACC,MAAM,WAAW,SAAS,KAAK,SAAS,EAAE;IAC1C,MAAM,YAAY,SAAS,UAAU,MAAM;IAC3C,MAAM,UAAU,KAAK,WAAW;IAChC,OAAO;QACL;QACA;QACA,QAAQ;IACV;AACF;AAEA,IAAI,kBAAkB,CAAC,WAAW;IAChC,CAAC,UAAU,KAAK,GAAG,uCAAwC,qDAA4B,KAAK;IAC5F,MAAM,aAAa,UAAU,KAAK;IAClC,MAAM,aAAa,SAAS,WAAW,WAAW,MAAM,CAAC,OAAO;IAChE,MAAM,qBAAqB,OAAO;IAClC,MAAM,QAAQ;QACZ,GAAG,UAAU;QACb,QAAQ;YACN,SAAS,WAAW,MAAM,CAAC,OAAO;YAClC,SAAS;YACT,MAAM;gBACJ,OAAO;gBACP,cAAc;YAChB;YACA,KAAK,WAAW,MAAM,CAAC,GAAG;QAC5B;IACF;IACA,MAAM,UAAU,WAAW;QACzB,MAAM,UAAU,OAAO,CAAC,IAAI;QAC5B,iBAAiB,UAAU,OAAO,CAAC,eAAe;QAClD,MAAM,UAAU,IAAI;QACpB;IACF;IACA,MAAM,SAAS;QACb,GAAG,SAAS;QACZ;QACA;IACF;IACA,OAAO;AACT;AAEA,SAAS,WAAW,QAAQ,EAAE,UAAU,cAAc;IACpD,IAAI,QAAQ;IACZ,SAAS,SAAS,GAAG,OAAO;QAC1B,IAAI,SAAS,MAAM,QAAQ,KAAK,IAAI,IAAI,QAAQ,SAAS,MAAM,QAAQ,GAAG;YACxE,OAAO,MAAM,UAAU;QACzB;QACA,MAAM,aAAa,SAAS,KAAK,CAAC,IAAI,EAAE;QACxC,QAAQ;YACN;YACA,UAAU;YACV,UAAU,IAAI;QAChB;QACA,OAAO;IACT;IACA,SAAS,KAAK,GAAG,SAAS;QACxB,QAAQ;IACV;IACA,OAAO;AACT;AAEA,MAAM,iBAAiB,WAAW,CAAA,aAAc,WAAW,MAAM,CAAC,CAAC,UAAU;QAC3E,QAAQ,CAAC,QAAQ,UAAU,CAAC,EAAE,CAAC,GAAG;QAClC,OAAO;IACT,GAAG,CAAC;AACJ,MAAM,iBAAiB,WAAW,CAAA,aAAc,WAAW,MAAM,CAAC,CAAC,UAAU;QAC3E,QAAQ,CAAC,QAAQ,UAAU,CAAC,EAAE,CAAC,GAAG;QAClC,OAAO;IACT,GAAG,CAAC;AACJ,MAAM,kBAAkB,WAAW,CAAA,aAAc,OAAO,MAAM,CAAC;AAC/D,MAAM,kBAAkB,WAAW,CAAA,aAAc,OAAO,MAAM,CAAC;AAE/D,IAAI,+BAA+B,WAAW,CAAC,aAAa;IAC1D,MAAM,SAAS,gBAAgB,YAAY,MAAM,CAAC,CAAA,YAAa,gBAAgB,UAAU,UAAU,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,UAAU,CAAC,KAAK,GAAG,EAAE,UAAU,CAAC,KAAK;IACvK,OAAO;AACT;AAEA,SAAS,kBAAkB,MAAM;IAC/B,IAAI,OAAO,EAAE,IAAI,OAAO,EAAE,CAAC,IAAI,KAAK,WAAW;QAC7C,OAAO,OAAO,EAAE,CAAC,WAAW;IAC9B;IACA,OAAO;AACT;AACA,SAAS,cAAc,MAAM;IAC3B,IAAI,OAAO,EAAE,IAAI,OAAO,EAAE,CAAC,IAAI,KAAK,WAAW;QAC7C,OAAO,OAAO,EAAE,CAAC,OAAO;IAC1B;IACA,OAAO;AACT;AAEA,IAAI,0BAA0B,WAAW,CAAC,QAAQ,OAAS,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,UAAU,CAAC,EAAE,KAAK,OAAO,UAAU,CAAC,EAAE;AAE1H,IAAI,oBAAoB,CAAC,EACvB,eAAe,EACf,SAAS,EACT,WAAW,EACX,iBAAiB,EACjB,cAAc,EACf;IACC,IAAI,CAAC,YAAY,gBAAgB,EAAE;QACjC,OAAO;IACT;IACA,MAAM,WAAW,kBAAkB;IACnC,IAAI,CAAC,UAAU;QACb,OAAO;IACT;IACA,SAAS,UAAU,MAAM;QACvB,MAAM,KAAK;YACT,MAAM;YACN,SAAS;gBACP,aAAa;gBACb,aAAa,YAAY,UAAU,CAAC,EAAE;YACxC;QACF;QACA,OAAO;YACL,GAAG,cAAc;YACjB;QACF;IACF;IACA,MAAM,MAAM,eAAe,SAAS,CAAC,GAAG;IACxC,MAAM,YAAY,IAAI,MAAM,GAAG,GAAG,CAAC,EAAE,GAAG;IACxC,IAAI,iBAAiB;QACnB,OAAO,YAAY,UAAU,aAAa;IAC5C;IACA,MAAM,mBAAmB,wBAAwB,WAAW;IAC5D,IAAI,CAAC,WAAW;QACd,IAAI,CAAC,iBAAiB,MAAM,EAAE;YAC5B,OAAO;QACT;QACA,MAAM,OAAO,gBAAgB,CAAC,iBAAiB,MAAM,GAAG,EAAE;QAC1D,OAAO,UAAU,KAAK,UAAU,CAAC,EAAE;IACrC;IACA,MAAM,iBAAiB,iBAAiB,SAAS,CAAC,CAAA,IAAK,EAAE,UAAU,CAAC,EAAE,KAAK;IAC3E,CAAC,CAAC,mBAAmB,CAAC,CAAC,IAAI,uCAAwC,UAAU,OAAO,iFAAwD,KAAK;IACjJ,MAAM,gBAAgB,iBAAiB;IACvC,IAAI,gBAAgB,GAAG;QACrB,OAAO;IACT;IACA,MAAM,SAAS,gBAAgB,CAAC,cAAc;IAC9C,OAAO,UAAU,OAAO,UAAU,CAAC,EAAE;AACvC;AAEA,IAAI,WAAW,CAAC,WAAW,cAAgB,UAAU,UAAU,CAAC,WAAW,KAAK,YAAY,UAAU,CAAC,EAAE;AAEzG,MAAM,gBAAgB;IACpB,OAAO;IACP,OAAO;AACT;AACA,MAAM,cAAc;IAClB,WAAW,CAAC;IACZ,SAAS,CAAC;IACV,KAAK,EAAE;AACT;AACA,MAAM,WAAW;IACf,WAAW;IACX,aAAa;IACb,IAAI;AACN;AAEA,IAAI,WAAW,CAAC,YAAY,aAAe,CAAA,QAAS,cAAc,SAAS,SAAS;AAEpF,IAAI,iCAAiC,CAAA;IACnC,MAAM,mBAAmB,SAAS,MAAM,GAAG,EAAE,MAAM,MAAM;IACzD,MAAM,qBAAqB,SAAS,MAAM,IAAI,EAAE,MAAM,KAAK;IAC3D,OAAO,CAAA;QACL,MAAM,cAAc,iBAAiB,QAAQ,GAAG,KAAK,iBAAiB,QAAQ,MAAM,KAAK,mBAAmB,QAAQ,IAAI,KAAK,mBAAmB,QAAQ,KAAK;QAC7J,IAAI,aAAa;YACf,OAAO;QACT;QACA,MAAM,+BAA+B,iBAAiB,QAAQ,GAAG,KAAK,iBAAiB,QAAQ,MAAM;QACrG,MAAM,iCAAiC,mBAAmB,QAAQ,IAAI,KAAK,mBAAmB,QAAQ,KAAK;QAC3G,MAAM,uBAAuB,gCAAgC;QAC7D,IAAI,sBAAsB;YACxB,OAAO;QACT;QACA,MAAM,qBAAqB,QAAQ,GAAG,GAAG,MAAM,GAAG,IAAI,QAAQ,MAAM,GAAG,MAAM,MAAM;QACnF,MAAM,uBAAuB,QAAQ,IAAI,GAAG,MAAM,IAAI,IAAI,QAAQ,KAAK,GAAG,MAAM,KAAK;QACrF,MAAM,0BAA0B,sBAAsB;QACtD,IAAI,yBAAyB;YAC3B,OAAO;QACT;QACA,MAAM,0BAA0B,sBAAsB,kCAAkC,wBAAwB;QAChH,OAAO;IACT;AACF;AAEA,IAAI,+BAA+B,CAAA;IACjC,MAAM,mBAAmB,SAAS,MAAM,GAAG,EAAE,MAAM,MAAM;IACzD,MAAM,qBAAqB,SAAS,MAAM,IAAI,EAAE,MAAM,KAAK;IAC3D,OAAO,CAAA;QACL,MAAM,cAAc,iBAAiB,QAAQ,GAAG,KAAK,iBAAiB,QAAQ,MAAM,KAAK,mBAAmB,QAAQ,IAAI,KAAK,mBAAmB,QAAQ,KAAK;QAC7J,OAAO;IACT;AACF;AAEA,MAAM,WAAW;IACf,WAAW;IACX,MAAM;IACN,eAAe;IACf,OAAO;IACP,KAAK;IACL,MAAM;IACN,gBAAgB;IAChB,cAAc;IACd,eAAe;AACjB;AACA,MAAM,aAAa;IACjB,WAAW;IACX,MAAM;IACN,eAAe;IACf,OAAO;IACP,KAAK;IACL,MAAM;IACN,gBAAgB;IAChB,cAAc;IACd,eAAe;AACjB;AAEA,IAAI,qCAAqC,CAAA,OAAQ,CAAA;QAC/C,MAAM,mBAAmB,SAAS,MAAM,GAAG,EAAE,MAAM,MAAM;QACzD,MAAM,qBAAqB,SAAS,MAAM,IAAI,EAAE,MAAM,KAAK;QAC3D,OAAO,CAAA;YACL,IAAI,SAAS,UAAU;gBACrB,OAAO,iBAAiB,QAAQ,GAAG,KAAK,iBAAiB,QAAQ,MAAM;YACzE;YACA,OAAO,mBAAmB,QAAQ,IAAI,KAAK,mBAAmB,QAAQ,KAAK;QAC7E;IACF;AAEA,MAAM,wBAAwB,CAAC,QAAQ;IACrC,MAAM,eAAe,YAAY,KAAK,GAAG,YAAY,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,GAAG;IACtF,OAAO,iBAAiB,QAAQ;AAClC;AACA,MAAM,uBAAuB,CAAC,QAAQ,aAAa;IACjD,IAAI,CAAC,YAAY,OAAO,CAAC,MAAM,EAAE;QAC/B,OAAO;IACT;IACA,OAAO,wBAAwB,YAAY,OAAO,CAAC,MAAM,EAAE;AAC7D;AACA,MAAM,sBAAsB,CAAC,QAAQ,UAAU,0BAA4B,wBAAwB,UAAU;AAC7G,MAAM,cAAc,CAAC,EACnB,QAAQ,aAAa,EACrB,WAAW,EACX,QAAQ,EACR,yBAAyB,EACzB,uBAAuB,EACxB;IACC,MAAM,kBAAkB,4BAA4B,sBAAsB,eAAe,eAAe;IACxG,OAAO,qBAAqB,iBAAiB,aAAa,4BAA4B,oBAAoB,iBAAiB,UAAU;AACvI;AACA,MAAM,qBAAqB,CAAA,OAAQ,YAAY;QAC7C,GAAG,IAAI;QACP,yBAAyB;IAC3B;AACA,MAAM,mBAAmB,CAAA,OAAQ,YAAY;QAC3C,GAAG,IAAI;QACP,yBAAyB;IAC3B;AACA,MAAM,yBAAyB,CAAA,OAAQ,YAAY;QACjD,GAAG,IAAI;QACP,yBAAyB,mCAAmC,KAAK,WAAW,CAAC,IAAI;IACnF;AAEA,MAAM,mBAAmB,CAAC,IAAI,MAAM;IAClC,IAAI,OAAO,uBAAuB,WAAW;QAC3C,OAAO;IACT;IACA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IACA,MAAM,EACJ,SAAS,EACT,OAAO,EACR,GAAG;IACJ,IAAI,SAAS,CAAC,GAAG,EAAE;QACjB,OAAO;IACT;IACA,MAAM,WAAW,OAAO,CAAC,GAAG;IAC5B,OAAO,WAAW,SAAS,aAAa,GAAG;AAC7C;AACA,SAAS,UAAU,SAAS,EAAE,WAAW;IACvC,MAAM,YAAY,UAAU,IAAI,CAAC,SAAS;IAC1C,MAAM,WAAW;QACf,KAAK,YAAY,KAAK,CAAC,CAAC;QACxB,OAAO;QACP,QAAQ;QACR,MAAM,YAAY,KAAK,CAAC,CAAC;IAC3B;IACA,OAAO,CAAA,GAAA,yKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,yKAAA,CAAA,SAAM,AAAD,EAAE,WAAW;AACnC;AACA,SAAS,sBAAsB,EAC7B,aAAa,EACb,WAAW,EACX,WAAW,EACX,QAAQ,EACR,kBAAkB,EAClB,IAAI,EACL;IACC,OAAO,cAAc,MAAM,CAAC,SAAS,QAAQ,MAAM,EAAE,SAAS;QAC5D,MAAM,SAAS,UAAU,WAAW;QACpC,MAAM,KAAK,UAAU,UAAU,CAAC,EAAE;QAClC,OAAO,GAAG,CAAC,IAAI,CAAC;QAChB,MAAM,YAAY,mBAAmB;YACnC;YACA;YACA;YACA,2BAA2B;QAC7B;QACA,IAAI,CAAC,WAAW;YACd,OAAO,SAAS,CAAC,UAAU,UAAU,CAAC,EAAE,CAAC,GAAG;YAC5C,OAAO;QACT;QACA,MAAM,gBAAgB,iBAAiB,IAAI,MAAM;QACjD,MAAM,eAAe;YACnB,aAAa;YACb;QACF;QACA,OAAO,OAAO,CAAC,GAAG,GAAG;QACrB,OAAO;IACT,GAAG;QACD,KAAK,EAAE;QACP,SAAS,CAAC;QACV,WAAW,CAAC;IACd;AACF;AAEA,SAAS,mBAAmB,UAAU,EAAE,OAAO;IAC7C,IAAI,CAAC,WAAW,MAAM,EAAE;QACtB,OAAO;IACT;IACA,MAAM,kBAAkB,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,CAAC,UAAU,CAAC,KAAK;IAC1E,OAAO,QAAQ,UAAU,GAAG,kBAAkB,kBAAkB;AAClE;AACA,SAAS,QAAQ,EACf,iBAAiB,EACjB,UAAU,EACV,WAAW,EACX,WAAW,EACZ;IACC,MAAM,WAAW,mBAAmB,mBAAmB;QACrD;IACF;IACA,OAAO;QACL,WAAW;QACX;QACA,IAAI;YACF,MAAM;YACN,aAAa;gBACX,aAAa,YAAY,UAAU,CAAC,EAAE;gBACtC,OAAO;YACT;QACF;IACF;AACF;AACA,SAAS,uBAAuB,EAC9B,SAAS,EACT,iBAAiB,EACjB,WAAW,EACX,QAAQ,EACR,WAAW,EACX,IAAI,EACJ,KAAK,EACL,kBAAkB,EACnB;IACC,MAAM,aAAa,SAAS,WAAW;IACvC,IAAI,SAAS,MAAM;QACjB,OAAO,QAAQ;YACb;YACA;YACA;YACA;QACF;IACF;IACA,MAAM,QAAQ,kBAAkB,IAAI,CAAC,CAAA,OAAQ,KAAK,UAAU,CAAC,KAAK,KAAK;IACvE,IAAI,CAAC,OAAO;QACV,OAAO,QAAQ;YACb;YACA;YACA;YACA;QACF;IACF;IACA,MAAM,kBAAkB,wBAAwB,WAAW;IAC3D,MAAM,YAAY,kBAAkB,OAAO,CAAC;IAC5C,MAAM,WAAW,gBAAgB,KAAK,CAAC;IACvC,MAAM,YAAY,sBAAsB;QACtC,eAAe;QACf;QACA;QACA;QACA,UAAU,SAAS,KAAK;QACxB;IACF;IACA,OAAO;QACL;QACA;QACA,IAAI;YACF,MAAM;YACN,aAAa;gBACX,aAAa,YAAY,UAAU,CAAC,EAAE;gBACtC;YACF;QACF;IACF;AACF;AAEA,SAAS,sBAAsB,WAAW,EAAE,aAAa;IACvD,OAAO,QAAQ,cAAc,QAAQ,CAAC,YAAY;AACpD;AAEA,IAAI,cAAc,CAAC,EACjB,eAAe,EACf,WAAW,EACX,UAAU,EACV,OAAO,EACP,aAAa,EACd;IACC,IAAI,CAAC,YAAY,gBAAgB,EAAE;QACjC,OAAO;IACT;IACA,MAAM,YAAY,QAAQ,WAAW;IACrC,MAAM,cAAc,UAAU,CAAC,UAAU;IACzC,MAAM,mBAAmB,YAAY,UAAU,CAAC,KAAK;IACrD,MAAM,mCAAmC,sBAAsB,WAAW;IAC1E,IAAI,kCAAkC;QACpC,IAAI,iBAAiB;YACnB,OAAO;QACT;QACA,OAAO,mBAAmB;IAC5B;IACA,IAAI,iBAAiB;QACnB,OAAO,mBAAmB;IAC5B;IACA,OAAO;AACT;AAEA,IAAI,cAAc,CAAC,EACjB,eAAe,EACf,YAAY,EACZ,iBAAiB,EACjB,QAAQ,EACT;IACC,IAAI,CAAC,kBAAkB,MAAM,EAAE;QAC7B,OAAO;IACT;IACA,MAAM,eAAe,SAAS,KAAK;IACnC,MAAM,gBAAgB,kBAAkB,eAAe,IAAI,eAAe;IAC1E,MAAM,aAAa,iBAAiB,CAAC,EAAE,CAAC,UAAU,CAAC,KAAK;IACxD,MAAM,YAAY,iBAAiB,CAAC,kBAAkB,MAAM,GAAG,EAAE,CAAC,UAAU,CAAC,KAAK;IAClF,MAAM,aAAa,eAAe,YAAY,YAAY;IAC1D,IAAI,gBAAgB,YAAY;QAC9B,OAAO;IACT;IACA,IAAI,gBAAgB,YAAY;QAC9B,OAAO;IACT;IACA,OAAO;AACT;AAEA,IAAI,kBAAkB,CAAC,EACrB,eAAe,EACf,YAAY,EACZ,SAAS,EACT,UAAU,EACV,WAAW,EACX,iBAAiB,EACjB,cAAc,EACd,QAAQ,EACR,aAAa,EACd;IACC,MAAM,QAAQ,eAAe,EAAE;IAC/B,CAAC,QAAQ,uCAAwC,UAAU,OAAO,sGAA6E,KAAK;IACpJ,IAAI,MAAM,IAAI,KAAK,WAAW;QAC5B,MAAM,WAAW,YAAY;YAC3B;YACA;YACA,UAAU,MAAM,WAAW;YAC3B;QACF;QACA,IAAI,YAAY,MAAM;YACpB,OAAO;QACT;QACA,OAAO,uBAAuB;YAC5B;YACA;YACA;YACA;YACA,MAAM,eAAe,SAAS;YAC9B,aAAa,eAAe,WAAW;YACvC,OAAO;QACT;IACF;IACA,MAAM,WAAW,YAAY;QAC3B;QACA;QACA,WAAW,eAAe,SAAS;QACnC;QACA,SAAS,MAAM,OAAO;QACtB;IACF;IACA,IAAI,YAAY,MAAM;QACpB,OAAO;IACT;IACA,OAAO,uBAAuB;QAC5B;QACA;QACA;QACA;QACA,MAAM,eAAe,SAAS;QAC9B,aAAa,eAAe,WAAW;QACvC,OAAO;IACT;AACF;AAEA,IAAI,8BAA8B,CAAC,EACjC,SAAS,EACT,aAAa,EACb,WAAW,EACX,WAAW,EACZ;IACC,MAAM,cAAc,QAAQ,UAAU,OAAO,CAAC,YAAY,IAAI,UAAU,SAAS,CAAC,YAAY;IAC9F,IAAI,sBAAsB,aAAa,gBAAgB;QACrD,OAAO,cAAc,SAAS,OAAO,YAAY,KAAK;IACxD;IACA,OAAO,cAAc,YAAY,KAAK,GAAG;AAC3C;AAEA,IAAI,gBAAgB,CAAC,EACnB,aAAa,EACb,MAAM,EACN,UAAU,EACX;IACC,MAAM,UAAU,cAAc;IAC9B,CAAC,UAAU,uCAAwC,qDAA4B,KAAK;IACpF,MAAM,cAAc,QAAQ,WAAW;IACvC,MAAM,SAAS,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM;IAC5D,MAAM,aAAa,4BAA4B;QAC7C,WAAW,OAAO,SAAS;QAC3B;QACA;QACA,aAAa,OAAO,WAAW;IACjC;IACA,OAAO,IAAI,QAAQ;AACrB;AAEA,MAAM,qCAAqC,CAAC,MAAM,MAAQ,IAAI,MAAM,CAAC,KAAK,KAAK,CAAC,GAAG,IAAI,SAAS,CAAC,KAAK,IAAI,CAAC,GAAG;AAC9G,MAAM,mCAAmC,CAAC,MAAM,MAAQ,IAAI,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,IAAI,SAAS,CAAC,KAAK,IAAI,CAAC,GAAG;AAC1G,MAAM,8BAA8B,CAAC,MAAM,QAAQ,WAAa,MAAM,CAAC,KAAK,cAAc,CAAC,GAAG,SAAS,MAAM,CAAC,KAAK,cAAc,CAAC,GAAG,SAAS,SAAS,CAAC,KAAK,aAAa,CAAC,GAAG;AAC9K,MAAM,UAAU,CAAC,EACf,IAAI,EACJ,cAAc,EACd,QAAQ,EACT,GAAK,MAAM,KAAK,IAAI,EAAE,eAAe,SAAS,CAAC,KAAK,GAAG,CAAC,GAAG,mCAAmC,MAAM,WAAW,4BAA4B,MAAM,eAAe,SAAS,EAAE;AAC5K,MAAM,WAAW,CAAC,EAChB,IAAI,EACJ,cAAc,EACd,QAAQ,EACT,GAAK,MAAM,KAAK,IAAI,EAAE,eAAe,SAAS,CAAC,KAAK,KAAK,CAAC,GAAG,iCAAiC,MAAM,WAAW,4BAA4B,MAAM,eAAe,SAAS,EAAE;AAC5K,MAAM,cAAc,CAAC,EACnB,IAAI,EACJ,QAAQ,EACR,QAAQ,EACT,GAAK,MAAM,KAAK,IAAI,EAAE,SAAS,UAAU,CAAC,KAAK,KAAK,CAAC,GAAG,mCAAmC,MAAM,WAAW,4BAA4B,MAAM,SAAS,UAAU,EAAE;AAEpK,IAAI,iBAAiB,CAAC,EACpB,MAAM,EACN,SAAS,EACT,UAAU,EACV,SAAS,EACT,aAAa,EACd;IACC,MAAM,oBAAoB,6BAA6B,UAAU,UAAU,CAAC,EAAE,EAAE;IAChF,MAAM,gBAAgB,UAAU,IAAI;IACpC,MAAM,OAAO,UAAU,IAAI;IAC3B,IAAI,CAAC,kBAAkB,MAAM,EAAE;QAC7B,OAAO,YAAY;YACjB;YACA,UAAU,UAAU,IAAI;YACxB,UAAU;QACZ;IACF;IACA,MAAM,EACJ,SAAS,EACT,WAAW,EACZ,GAAG;IACJ,MAAM,eAAe,UAAU,GAAG,CAAC,EAAE;IACrC,IAAI,cAAc;QAChB,MAAM,UAAU,UAAU,CAAC,aAAa;QACxC,IAAI,sBAAsB,cAAc,gBAAgB;YACtD,OAAO,SAAS;gBACd;gBACA,gBAAgB,QAAQ,IAAI;gBAC5B,UAAU;YACZ;QACF;QACA,MAAM,mBAAmB,CAAA,GAAA,yKAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,IAAI,EAAE,YAAY,KAAK;QAC/D,OAAO,SAAS;YACd;YACA,gBAAgB;YAChB,UAAU;QACZ;IACF;IACA,MAAM,OAAO,iBAAiB,CAAC,kBAAkB,MAAM,GAAG,EAAE;IAC5D,IAAI,KAAK,UAAU,CAAC,EAAE,KAAK,UAAU,UAAU,CAAC,EAAE,EAAE;QAClD,OAAO,cAAc,SAAS,CAAC,MAAM;IACvC;IACA,IAAI,sBAAsB,KAAK,UAAU,CAAC,EAAE,EAAE,gBAAgB;QAC5D,MAAM,OAAO,CAAA,GAAA,yKAAA,CAAA,SAAM,AAAD,EAAE,KAAK,IAAI,EAAE,OAAO,cAAc,WAAW,CAAC,KAAK;QACrE,OAAO,QAAQ;YACb;YACA,gBAAgB;YAChB,UAAU;QACZ;IACF;IACA,OAAO,QAAQ;QACb;QACA,gBAAgB,KAAK,IAAI;QACzB,UAAU;IACZ;AACF;AAEA,IAAI,4BAA4B,CAAC,WAAW;IAC1C,MAAM,QAAQ,UAAU,KAAK;IAC7B,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IACA,OAAO,IAAI,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,YAAY;AAClD;AAEA,MAAM,wCAAwC,CAAC,EAC7C,MAAM,EACN,SAAS,EACT,SAAS,EACT,UAAU,EACV,aAAa,EACd;IACC,MAAM,WAAW,UAAU,IAAI,CAAC,SAAS,CAAC,MAAM;IAChD,MAAM,KAAK,OAAO,EAAE;IACpB,IAAI,CAAC,WAAW;QACd,OAAO;IACT;IACA,IAAI,CAAC,IAAI;QACP,OAAO;IACT;IACA,IAAI,GAAG,IAAI,KAAK,WAAW;QACzB,OAAO,eAAe;YACpB;YACA;YACA;YACA;YACA;QACF;IACF;IACA,OAAO,cAAc;QACnB;QACA;QACA;IACF;AACF;AACA,IAAI,mCAAmC,CAAA;IACrC,MAAM,sBAAsB,sCAAsC;IAClE,MAAM,YAAY,KAAK,SAAS;IAChC,MAAM,mBAAmB,YAAY,0BAA0B,WAAW,uBAAuB;IACjG,OAAO;AACT;AAEA,IAAI,iBAAiB,CAAC,UAAU;IAC9B,MAAM,OAAO,SAAS,WAAW,SAAS,MAAM,CAAC,OAAO;IACxD,MAAM,eAAe,OAAO;IAC5B,MAAM,QAAQ,CAAA,GAAA,yKAAA,CAAA,UAAO,AAAD,EAAE;QACpB,KAAK,UAAU,CAAC;QAChB,QAAQ,UAAU,CAAC,GAAG,SAAS,KAAK,CAAC,MAAM;QAC3C,MAAM,UAAU,CAAC;QACjB,OAAO,UAAU,CAAC,GAAG,SAAS,KAAK,CAAC,KAAK;IAC3C;IACA,MAAM,UAAU;QACd;QACA,QAAQ;YACN,SAAS,SAAS,MAAM,CAAC,OAAO;YAChC,KAAK,SAAS,MAAM,CAAC,GAAG;YACxB,SAAS;YACT,MAAM;gBACJ,OAAO;gBACP;YACF;QACF;IACF;IACA,OAAO;AACT;AAEA,SAAS,gBAAgB,GAAG,EAAE,UAAU;IACtC,OAAO,IAAI,GAAG,CAAC,CAAA,KAAM,UAAU,CAAC,GAAG;AACrC;AACA,SAAS,cAAc,EAAE,EAAE,MAAM;IAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACtC,MAAM,eAAe,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG;QAC1C,IAAI,cAAc;YAChB,OAAO;QACT;IACF;IACA,OAAO;AACT;AACA,IAAI,wBAAwB,CAAC,EAC3B,MAAM,EACN,QAAQ,EACR,WAAW,EACX,UAAU,EACV,eAAe,EAChB;IACC,MAAM,mBAAmB,eAAe,UAAU,IAAI,SAAS,MAAM,CAAC,OAAO,EAAE;IAC/E,MAAM,oBAAoB,YAAY,KAAK,GAAG,gBAAgB,aAAa,IAAI,YAAY,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,oBAAoB;IACrI,MAAM,OAAO,OAAO,SAAS;IAC7B,MAAM,qBAAqB,sBAAsB;QAC/C,eAAe,gBAAgB,KAAK,GAAG,EAAE;QACzC;QACA,aAAa,OAAO,WAAW;QAC/B,UAAU,iBAAiB,KAAK;QAChC;QACA,oBAAoB;IACtB;IACA,MAAM,sBAAsB,sBAAsB;QAChD,eAAe,gBAAgB,KAAK,GAAG,EAAE;QACzC,aAAa;QACb,aAAa,OAAO,WAAW;QAC/B,UAAU,SAAS,KAAK;QACxB;QACA,oBAAoB;IACtB;IACA,MAAM,YAAY,CAAC;IACnB,MAAM,UAAU,CAAC;IACjB,MAAM,SAAS;QAAC;QAAM;QAAoB;KAAoB;IAC9D,KAAK,GAAG,CAAC,OAAO,CAAC,CAAA;QACf,MAAM,eAAe,cAAc,IAAI;QACvC,IAAI,cAAc;YAChB,OAAO,CAAC,GAAG,GAAG;YACd;QACF;QACA,SAAS,CAAC,GAAG,GAAG;IAClB;IACA,MAAM,YAAY;QAChB,GAAG,MAAM;QACT,WAAW;YACT,KAAK,KAAK,GAAG;YACb;YACA;QACF;IACF;IACA,OAAO;AACT;AAEA,IAAI,2BAA2B,CAAC,UAAU,QAAU,IAAI,SAAS,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE;AAE3F,IAAI,mCAAmC,CAAC,EACtC,mBAAmB,EACnB,SAAS,EACT,QAAQ,EACT;IACC,MAAM,0BAA0B,yBAAyB,UAAU;IACnE,MAAM,SAAS,SAAS,yBAAyB,UAAU,IAAI,CAAC,SAAS,CAAC,MAAM;IAChF,OAAO,IAAI,UAAU,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE;AAChD;AAEA,IAAI,gCAAgC,CAAC,EACnC,SAAS,EACT,WAAW,EACX,sBAAsB,EACtB,QAAQ,EACR,yBAAyB,EACzB,iBAAiB,KAAK,EACvB;IACC,MAAM,eAAe,SAAS,wBAAwB,UAAU,IAAI,CAAC,SAAS,CAAC,MAAM;IACrF,MAAM,UAAU,iBAAiB,UAAU,IAAI,CAAC,SAAS,EAAE;IAC3D,MAAM,OAAO;QACX,QAAQ;QACR;QACA;QACA;IACF;IACA,OAAO,iBAAiB,uBAAuB,QAAQ,iBAAiB;AAC1E;AAEA,IAAI,kBAAkB,CAAC,EACrB,eAAe,EACf,SAAS,EACT,WAAW,EACX,UAAU,EACV,cAAc,EACd,QAAQ,EACR,2BAA2B,EAC3B,uBAAuB,EACvB,aAAa,EACd;IACC,IAAI,CAAC,YAAY,SAAS,EAAE;QAC1B,OAAO;IACT;IACA,MAAM,oBAAoB,6BAA6B,YAAY,UAAU,CAAC,EAAE,EAAE;IAClF,MAAM,eAAe,SAAS,WAAW;IACzC,MAAM,SAAS,kBAAkB;QAC/B;QACA;QACA;QACA;QACA;IACF,MAAM,gBAAgB;QACpB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IACA,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IACA,MAAM,sBAAsB,iCAAiC;QAC3D;QACA;QACA,WAAW;QACX;QACA;IACF;IACA,MAAM,yBAAyB,8BAA8B;QAC3D;QACA;QACA,wBAAwB;QACxB,UAAU,SAAS,KAAK;QACxB,2BAA2B;QAC3B,gBAAgB;IAClB;IACA,IAAI,wBAAwB;QAC1B,MAAM,kBAAkB,iCAAiC;YACvD;YACA;YACA;QACF;QACA,OAAO;YACL;YACA;YACA,mBAAmB;QACrB;IACF;IACA,MAAM,WAAW,SAAS,qBAAqB;IAC/C,MAAM,WAAW,sBAAsB;QACrC;QACA;QACA;QACA;QACA,iBAAiB;IACnB;IACA,OAAO;QACL,iBAAiB;QACjB,QAAQ;QACR,mBAAmB;IACrB;AACF;AAEA,MAAM,iBAAiB,CAAA;IACrB,MAAM,OAAO,UAAU,OAAO,CAAC,MAAM;IACrC,CAAC,OAAO,uCAAwC,UAAU,OAAO,mFAA0D,KAAK;IAChI,OAAO;AACT;AACA,IAAI,4BAA4B,CAAC,EAC/B,eAAe,EACf,mBAAmB,EACnB,MAAM,EACN,UAAU,EACV,QAAQ,EACT;IACC,MAAM,SAAS,OAAO,OAAO,CAAC,MAAM;IACpC,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IACA,MAAM,OAAO,OAAO,IAAI;IACxB,MAAM,yBAAyB,SAAS,MAAM,CAAC,KAAK,KAAK,CAAC,EAAE,MAAM,CAAC,KAAK,GAAG,CAAC;IAC5E,MAAM,aAAa,gBAAgB,YAAY,MAAM,CAAC,CAAA,YAAa,cAAc,QAAQ,MAAM,CAAC,CAAA,YAAa,UAAU,SAAS,EAAE,MAAM,CAAC,CAAA,YAAa,QAAQ,UAAU,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,CAAA,YAAa,+BAA+B,SAAS,KAAK,EAAE,eAAe,aAAa,MAAM,CAAC,CAAA;QAC7R,MAAM,iBAAiB,eAAe;QACtC,IAAI,iBAAiB;YACnB,OAAO,MAAM,CAAC,KAAK,YAAY,CAAC,GAAG,cAAc,CAAC,KAAK,YAAY,CAAC;QACtE;QACA,OAAO,cAAc,CAAC,KAAK,cAAc,CAAC,GAAG,MAAM,CAAC,KAAK,cAAc,CAAC;IAC1E,GAAG,MAAM,CAAC,CAAA;QACR,MAAM,iBAAiB,eAAe;QACtC,MAAM,8BAA8B,SAAS,cAAc,CAAC,KAAK,KAAK,CAAC,EAAE,cAAc,CAAC,KAAK,GAAG,CAAC;QACjG,OAAO,uBAAuB,cAAc,CAAC,KAAK,KAAK,CAAC,KAAK,uBAAuB,cAAc,CAAC,KAAK,GAAG,CAAC,KAAK,4BAA4B,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,4BAA4B,MAAM,CAAC,KAAK,GAAG,CAAC;IAClN,GAAG,IAAI,CAAC,CAAC,GAAG;QACV,MAAM,QAAQ,eAAe,EAAE,CAAC,KAAK,cAAc,CAAC;QACpD,MAAM,SAAS,eAAe,EAAE,CAAC,KAAK,cAAc,CAAC;QACrD,IAAI,iBAAiB;YACnB,OAAO,QAAQ;QACjB;QACA,OAAO,SAAS;IAClB,GAAG,MAAM,CAAC,CAAC,WAAW,OAAO,QAAU,eAAe,UAAU,CAAC,KAAK,cAAc,CAAC,KAAK,eAAe,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,cAAc,CAAC;IACvI,IAAI,CAAC,WAAW,MAAM,EAAE;QACtB,OAAO;IACT;IACA,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,OAAO,UAAU,CAAC,EAAE;IACtB;IACA,MAAM,WAAW,WAAW,MAAM,CAAC,CAAA;QACjC,MAAM,oBAAoB,SAAS,eAAe,UAAU,CAAC,KAAK,KAAK,CAAC,EAAE,eAAe,UAAU,CAAC,KAAK,GAAG,CAAC;QAC7G,OAAO,kBAAkB,mBAAmB,CAAC,KAAK,IAAI,CAAC;IACzD;IACA,IAAI,SAAS,MAAM,KAAK,GAAG;QACzB,OAAO,QAAQ,CAAC,EAAE;IACpB;IACA,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,GAAG,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE;IAClG;IACA,OAAO,WAAW,IAAI,CAAC,CAAC,GAAG;QACzB,MAAM,QAAQ,UAAU,qBAAqB,WAAW,eAAe;QACvE,MAAM,SAAS,UAAU,qBAAqB,WAAW,eAAe;QACxE,IAAI,UAAU,QAAQ;YACpB,OAAO,QAAQ;QACjB;QACA,OAAO,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC,GAAG,eAAe,EAAE,CAAC,KAAK,KAAK,CAAC;IACtE,EAAE,CAAC,EAAE;AACP;AAEA,MAAM,gCAAgC,CAAC,WAAW;IAChD,MAAM,WAAW,UAAU,IAAI,CAAC,SAAS,CAAC,MAAM;IAChD,OAAO,sBAAsB,UAAU,UAAU,CAAC,EAAE,EAAE,iBAAiB,SAAS,UAAU,cAAc,WAAW,CAAC,KAAK,IAAI;AAC/H;AACA,MAAM,0BAA0B,CAAC,WAAW;IAC1C,MAAM,WAAW,UAAU,IAAI,CAAC,SAAS;IACzC,OAAO,sBAAsB,UAAU,UAAU,CAAC,EAAE,EAAE,iBAAiB,iBAAiB,UAAU,OAAO,cAAc,WAAW,CAAC,KAAK,KAAK;AAC/I;AAEA,IAAI,sBAAsB,CAAC,EACzB,mBAAmB,EACnB,QAAQ,EACR,WAAW,EACX,iBAAiB,EACjB,aAAa,EACd;IACC,MAAM,SAAS,kBAAkB,MAAM,CAAC,CAAA,YAAa,iBAAiB;YACpE,QAAQ,wBAAwB,WAAW;YAC3C;YACA,UAAU,SAAS,KAAK;YACxB,2BAA2B;QAC7B,IAAI,IAAI,CAAC,CAAC,GAAG;QACX,MAAM,cAAc,SAAS,qBAAqB,0BAA0B,aAAa,8BAA8B,GAAG;QAC1H,MAAM,cAAc,SAAS,qBAAqB,0BAA0B,aAAa,8BAA8B,GAAG;QAC1H,IAAI,cAAc,aAAa;YAC7B,OAAO,CAAC;QACV;QACA,IAAI,cAAc,aAAa;YAC7B,OAAO;QACT;QACA,OAAO,EAAE,UAAU,CAAC,KAAK,GAAG,EAAE,UAAU,CAAC,KAAK;IAChD;IACA,OAAO,MAAM,CAAC,EAAE,IAAI;AACtB;AAEA,IAAI,iBAAiB,WAAW,SAAS,eAAe,IAAI,EAAE,UAAU;IACtE,MAAM,eAAe,UAAU,CAAC,KAAK,IAAI,CAAC;IAC1C,OAAO;QACL,OAAO;QACP,OAAO,MAAM,KAAK,IAAI,EAAE;IAC1B;AACF;AAEA,MAAM,kCAAkC,CAAC,WAAW,iBAAiB;IACnE,MAAM,OAAO,UAAU,IAAI;IAC3B,IAAI,UAAU,UAAU,CAAC,IAAI,KAAK,WAAW;QAC3C,OAAO,MAAM,KAAK,IAAI,EAAE,eAAe,CAAC,KAAK,IAAI,CAAC;IACpD;IACA,MAAM,iBAAiB,UAAU,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,IAAI,CAAC;IACnE,MAAM,kBAAkB,6BAA6B,UAAU,UAAU,CAAC,EAAE,EAAE;IAC9E,MAAM,YAAY,gBAAgB,MAAM,CAAC,CAAC,KAAK,YAAc,MAAM,UAAU,MAAM,CAAC,SAAS,CAAC,KAAK,IAAI,CAAC,EAAE;IAC1G,MAAM,gBAAgB,YAAY,eAAe,CAAC,KAAK,IAAI,CAAC;IAC5D,MAAM,gBAAgB,gBAAgB;IACtC,IAAI,iBAAiB,GAAG;QACtB,OAAO;IACT;IACA,OAAO,MAAM,KAAK,IAAI,EAAE;AAC1B;AACA,MAAM,gBAAgB,CAAC,OAAO,MAAQ,CAAC;QACrC,GAAG,KAAK;QACR,QAAQ;YACN,GAAG,MAAM,MAAM;YACf;QACF;IACF,CAAC;AACD,MAAM,iBAAiB,CAAC,WAAW,WAAW;IAC5C,MAAM,QAAQ,UAAU,KAAK;IAC7B,CAAC,CAAC,SAAS,WAAW,aAAa,uCAAwC,UAAU,OAAO,0FAAiE,KAAK;IAClK,CAAC,CAAC,UAAU,OAAO,CAAC,eAAe,GAAG,uCAAwC,UAAU,OAAO,6GAAoF,KAAK;IACxL,MAAM,kBAAkB,eAAe,UAAU,IAAI,EAAE,UAAU,UAAU,EAAE,KAAK;IAClF,MAAM,iBAAiB,gCAAgC,WAAW,iBAAiB;IACnF,MAAM,QAAQ;QACZ;QACA,aAAa;QACb,mBAAmB,UAAU,KAAK,GAAG,UAAU,KAAK,CAAC,MAAM,CAAC,GAAG,GAAG;IACpE;IACA,IAAI,CAAC,OAAO;QACV,MAAM,UAAU,WAAW;YACzB,MAAM,UAAU,OAAO,CAAC,IAAI;YAC5B,iBAAiB;YACjB,MAAM,UAAU,IAAI;YACpB,OAAO,UAAU,KAAK;QACxB;QACA,OAAO;YACL,GAAG,SAAS;YACZ;QACF;IACF;IACA,MAAM,YAAY,iBAAiB,IAAI,MAAM,MAAM,CAAC,GAAG,EAAE,kBAAkB,MAAM,MAAM,CAAC,GAAG;IAC3F,MAAM,WAAW,cAAc,OAAO;IACtC,MAAM,UAAU,WAAW;QACzB,MAAM,UAAU,OAAO,CAAC,IAAI;QAC5B,iBAAiB;QACjB,MAAM,UAAU,IAAI;QACpB,OAAO;IACT;IACA,OAAO;QACL,GAAG,SAAS;QACZ;QACA,OAAO;IACT;AACF;AACA,MAAM,oBAAoB,CAAA;IACxB,MAAM,QAAQ,UAAU,OAAO,CAAC,eAAe;IAC/C,CAAC,QAAQ,uCAAwC,UAAU,OAAO,uGAA8E,KAAK;IACrJ,MAAM,QAAQ,UAAU,KAAK;IAC7B,IAAI,CAAC,OAAO;QACV,MAAM,UAAU,WAAW;YACzB,MAAM,UAAU,OAAO,CAAC,IAAI;YAC5B,MAAM,UAAU,IAAI;YACpB,OAAO;YACP,iBAAiB;QACnB;QACA,OAAO;YACL,GAAG,SAAS;YACZ;QACF;IACF;IACA,MAAM,eAAe,MAAM,iBAAiB;IAC5C,CAAC,eAAe,uCAAwC,UAAU,OAAO,iIAAwG,KAAK;IACtL,MAAM,WAAW,cAAc,OAAO;IACtC,MAAM,UAAU,WAAW;QACzB,MAAM,UAAU,OAAO,CAAC,IAAI;QAC5B,MAAM,UAAU,IAAI;QACpB,OAAO;QACP,iBAAiB;IACnB;IACA,OAAO;QACL,GAAG,SAAS;QACZ;QACA,OAAO;IACT;AACF;AAEA,IAAI,qBAAqB,CAAC,EACxB,2BAA2B,EAC3B,cAAc,EACd,iBAAiB,EACjB,SAAS,EACT,UAAU,EACV,WAAW,EACX,QAAQ,EACR,aAAa,EACd;IACC,IAAI,CAAC,gBAAgB;QACnB,IAAI,kBAAkB,MAAM,EAAE;YAC5B,OAAO;QACT;QACA,MAAM,WAAW;YACf,WAAW;YACX,aAAa;YACb,IAAI;gBACF,MAAM;gBACN,aAAa;oBACX,aAAa,YAAY,UAAU,CAAC,EAAE;oBACtC,OAAO;gBACT;YACF;QACF;QACA,MAAM,8BAA8B,iCAAiC;YACnE,QAAQ;YACR;YACA,WAAW;YACX;YACA;QACF;QACA,MAAM,kBAAkB,SAAS,WAAW,eAAe,cAAc,eAAe,aAAa,WAAW;QAChH,MAAM,yBAAyB,8BAA8B;YAC3D;YACA,aAAa;YACb,wBAAwB;YACxB,UAAU,SAAS,KAAK;YACxB,2BAA2B;YAC3B,gBAAgB;QAClB;QACA,OAAO,yBAAyB,WAAW;IAC7C;IACA,MAAM,sBAAsB,QAAQ,2BAA2B,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,IAAI,eAAe,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC;IACrJ,MAAM,gBAAgB,CAAC;QACrB,MAAM,aAAa,eAAe,UAAU,CAAC,KAAK;QAClD,IAAI,eAAe,UAAU,CAAC,EAAE,KAAK,UAAU,UAAU,CAAC,EAAE,EAAE;YAC5D,OAAO;QACT;QACA,IAAI,qBAAqB;YACvB,OAAO;QACT;QACA,OAAO,aAAa;IACtB,CAAC;IACD,MAAM,cAAc,eAAe,YAAY,IAAI,EAAE,UAAU,UAAU;IACzE,OAAO,uBAAuB;QAC5B;QACA;QACA;QACA;QACA;QACA,MAAM;QACN,OAAO;IACT;AACF;AAEA,IAAI,gBAAgB,CAAC,EACnB,eAAe,EACf,2BAA2B,EAC3B,SAAS,EACT,MAAM,EACN,UAAU,EACV,UAAU,EACV,QAAQ,EACR,aAAa,EACd;IACC,MAAM,cAAc,0BAA0B;QAC5C;QACA,qBAAqB;QACrB,QAAQ;QACR;QACA;IACF;IACA,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IACA,MAAM,oBAAoB,6BAA6B,YAAY,UAAU,CAAC,EAAE,EAAE;IAClF,MAAM,iBAAiB,oBAAoB;QACzC,qBAAqB;QACrB;QACA;QACA;QACA;IACF;IACA,MAAM,SAAS,mBAAmB;QAChC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IACA,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IACA,MAAM,sBAAsB,iCAAiC;QAC3D;QACA;QACA,WAAW;QACX;QACA;IACF;IACA,MAAM,kBAAkB,iCAAiC;QACvD;QACA;QACA;IACF;IACA,OAAO;QACL;QACA;QACA,mBAAmB;IACrB;AACF;AAEA,IAAI,oBAAoB,CAAA;IACtB,MAAM,KAAK,OAAO,EAAE;IACpB,IAAI,CAAC,IAAI;QACP,OAAO;IACT;IACA,IAAI,GAAG,IAAI,KAAK,WAAW;QACzB,OAAO,GAAG,WAAW,CAAC,WAAW;IACnC;IACA,OAAO,GAAG,OAAO,CAAC,WAAW;AAC/B;AAEA,MAAM,qBAAqB,CAAC,QAAQ;IAClC,MAAM,KAAK,kBAAkB;IAC7B,OAAO,KAAK,UAAU,CAAC,GAAG,GAAG;AAC/B;AACA,IAAI,kBAAkB,CAAC,EACrB,KAAK,EACL,IAAI,EACL;IACC,MAAM,iBAAiB,mBAAmB,MAAM,MAAM,EAAE,MAAM,UAAU,CAAC,UAAU;IACnF,MAAM,4BAA4B,QAAQ;IAC1C,MAAM,OAAO,MAAM,UAAU,CAAC,UAAU,CAAC,MAAM,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;IACrE,MAAM,SAAS,kBAAkB;IACjC,MAAM,YAAY,OAAO,IAAI,CAAC,SAAS;IACvC,MAAM,qBAAqB,cAAc,cAAc,CAAC,SAAS,aAAa,SAAS,WAAW,KAAK,cAAc,gBAAgB,CAAC,SAAS,eAAe,SAAS,YAAY;IACnL,IAAI,sBAAsB,CAAC,2BAA2B;QACpD,OAAO;IACT;IACA,MAAM,kBAAkB,SAAS,eAAe,SAAS;IACzD,MAAM,YAAY,MAAM,UAAU,CAAC,UAAU,CAAC,MAAM,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;IAC1E,MAAM,8BAA8B,MAAM,OAAO,CAAC,IAAI,CAAC,eAAe;IACtE,MAAM,EACJ,UAAU,EACV,UAAU,EACX,GAAG,MAAM,UAAU;IACpB,OAAO,qBAAqB,gBAAgB;QAC1C;QACA;QACA;QACA,aAAa;QACb;QACA,UAAU,MAAM,QAAQ;QACxB,yBAAyB,MAAM,OAAO,CAAC,MAAM,CAAC,SAAS;QACvD,gBAAgB,MAAM,MAAM;QAC5B,eAAe,MAAM,aAAa;IACpC,KAAK,cAAc;QACjB;QACA;QACA;QACA;QACA;QACA;QACA,UAAU,MAAM,QAAQ;QACxB,eAAe,MAAM,aAAa;IACpC;AACF;AAEA,SAAS,kBAAkB,KAAK;IAC9B,OAAO,MAAM,KAAK,KAAK,cAAc,MAAM,KAAK,KAAK;AACvD;AAEA,SAAS,kBAAkB,KAAK;IAC9B,MAAM,mBAAmB,SAAS,MAAM,GAAG,EAAE,MAAM,MAAM;IACzD,MAAM,qBAAqB,SAAS,MAAM,IAAI,EAAE,MAAM,KAAK;IAC3D,OAAO,SAAS,IAAI,KAAK;QACvB,OAAO,iBAAiB,MAAM,CAAC,KAAK,mBAAmB,MAAM,CAAC;IAChE;AACF;AAEA,SAAS,cAAc,KAAK,EAAE,MAAM;IAClC,OAAO,MAAM,IAAI,GAAG,OAAO,KAAK,IAAI,MAAM,KAAK,GAAG,OAAO,IAAI,IAAI,MAAM,GAAG,GAAG,OAAO,MAAM,IAAI,MAAM,MAAM,GAAG,OAAO,GAAG;AACzH;AACA,SAAS,gBAAgB,EACvB,aAAa,EACb,SAAS,EACT,UAAU,EACX;IACC,MAAM,cAAc,UAAU,IAAI,CAAC,SAAS,CAAC,MAAM;IACnD,MAAM,SAAS,WAAW,GAAG,CAAC,CAAA;QAC5B,MAAM,OAAO,UAAU,IAAI;QAC3B,MAAM,SAAS,MAAM,UAAU,IAAI,CAAC,IAAI,EAAE,cAAc,MAAM,CAAC,KAAK,IAAI,CAAC,EAAE,UAAU,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,aAAa,CAAC;QAC9H,OAAO;YACL,IAAI,UAAU,UAAU,CAAC,EAAE;YAC3B,UAAU,SAAS,aAAa;QAClC;IACF,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;IACzC,OAAO,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG;AACpC;AACA,SAAS,iBAAiB,EACxB,aAAa,EACb,SAAS,EACT,UAAU,EACX;IACC,MAAM,aAAa,gBAAgB,YAAY,MAAM,CAAC,CAAA;QACpD,IAAI,CAAC,KAAK,SAAS,EAAE;YACnB,OAAO;QACT;QACA,MAAM,SAAS,KAAK,OAAO,CAAC,MAAM;QAClC,IAAI,CAAC,QAAQ;YACX,OAAO;QACT;QACA,IAAI,CAAC,cAAc,eAAe,SAAS;YACzC,OAAO;QACT;QACA,IAAI,kBAAkB,QAAQ,cAAc,MAAM,GAAG;YACnD,OAAO;QACT;QACA,MAAM,OAAO,KAAK,IAAI;QACtB,MAAM,cAAc,OAAO,MAAM,CAAC,KAAK,aAAa,CAAC;QACrD,MAAM,iBAAiB,aAAa,CAAC,KAAK,cAAc,CAAC;QACzD,MAAM,eAAe,aAAa,CAAC,KAAK,YAAY,CAAC;QACrD,MAAM,cAAc,SAAS,MAAM,CAAC,KAAK,cAAc,CAAC,EAAE,MAAM,CAAC,KAAK,YAAY,CAAC;QACnF,MAAM,mBAAmB,YAAY;QACrC,MAAM,iBAAiB,YAAY;QACnC,IAAI,CAAC,oBAAoB,CAAC,gBAAgB;YACxC,OAAO;QACT;QACA,IAAI,kBAAkB;YACpB,OAAO,iBAAiB;QAC1B;QACA,OAAO,eAAe;IACxB;IACA,IAAI,CAAC,WAAW,MAAM,EAAE;QACtB,OAAO;IACT;IACA,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,OAAO,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE;IACpC;IACA,OAAO,gBAAgB;QACrB;QACA;QACA;IACF;AACF;AAEA,MAAM,uBAAuB,CAAC,MAAM,QAAU,CAAA,GAAA,yKAAA,CAAA,UAAO,AAAD,EAAE,iBAAiB,MAAM;AAE7E,IAAI,sBAAsB,CAAC,WAAW;IACpC,MAAM,QAAQ,UAAU,KAAK;IAC7B,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IACA,OAAO,qBAAqB,MAAM,MAAM,MAAM,CAAC,IAAI,CAAC,KAAK;AAC3D;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,EAAE,EACH;IACC,OAAO,QAAQ,UAAU,OAAO,CAAC,GAAG,IAAI,UAAU,SAAS,CAAC,GAAG;AACjE;AAEA,SAAS,QAAQ,EACf,SAAS,EACT,OAAO,EACP,UAAU,EACX;IACC,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IACA,IAAI,CAAC,YAAY;QACf,OAAO,QAAQ,UAAU,CAAC,KAAK;IACjC;IACA,IAAI,QAAQ,UAAU,CAAC,KAAK,GAAG,UAAU,UAAU,CAAC,KAAK,EAAE;QACzD,OAAO,QAAQ,UAAU,CAAC,KAAK,GAAG;IACpC;IACA,OAAO,QAAQ,UAAU,CAAC,KAAK;AACjC;AACA,IAAI,mBAAmB,CAAC,EACtB,kCAAkC,UAAU,EAC5C,SAAS,EACT,WAAW,EACX,iBAAiB,EACjB,IAAI,EACJ,QAAQ,EACR,aAAa,EACd;IACC,MAAM,OAAO,YAAY,IAAI;IAC7B,MAAM,cAAc,eAAe,YAAY,IAAI,EAAE,UAAU,UAAU;IACzE,MAAM,eAAe,YAAY,KAAK;IACtC,MAAM,cAAc,UAAU,CAAC,KAAK,KAAK,CAAC;IAC1C,MAAM,YAAY,UAAU,CAAC,KAAK,GAAG,CAAC;IACtC,MAAM,kBAAkB,wBAAwB,WAAW;IAC3D,MAAM,UAAU,gBAAgB,IAAI,CAAC,CAAA;QACnC,MAAM,KAAK,MAAM,UAAU,CAAC,EAAE;QAC9B,MAAM,cAAc,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC;QAC1D,MAAM,0BAA0B,sBAAsB,IAAI;QAC1D,MAAM,cAAc,eAAe;YACjC,WAAW;YACX;QACF;QACA,IAAI,yBAAyB;YAC3B,IAAI,aAAa;gBACf,OAAO,aAAa;YACtB;YACA,OAAO,cAAc,cAAc;QACrC;QACA,IAAI,aAAa;YACf,OAAO,aAAa,cAAc;QACpC;QACA,OAAO,cAAc;IACvB,MAAM;IACN,MAAM,WAAW,QAAQ;QACvB;QACA;QACA,YAAY,SAAS,WAAW;IAClC;IACA,OAAO,uBAAuB;QAC5B;QACA;QACA;QACA;QACA;QACA;QACA,OAAO;IACT;AACF;AAEA,MAAM,0BAA0B;AAChC,IAAI,mBAAmB,CAAC,EACtB,SAAS,EACT,kCAAkC,UAAU,EAC5C,cAAc,EACd,WAAW,EACX,iBAAiB,EACjB,aAAa,EACd;IACC,IAAI,CAAC,YAAY,gBAAgB,EAAE;QACjC,OAAO;IACT;IACA,MAAM,OAAO,YAAY,IAAI;IAC7B,MAAM,cAAc,eAAe,YAAY,IAAI,EAAE,UAAU,UAAU;IACzE,MAAM,eAAe,YAAY,KAAK;IACtC,MAAM,cAAc,UAAU,CAAC,KAAK,KAAK,CAAC;IAC1C,MAAM,YAAY,UAAU,CAAC,KAAK,GAAG,CAAC;IACtC,MAAM,kBAAkB,wBAAwB,WAAW;IAC3D,MAAM,cAAc,gBAAgB,IAAI,CAAC,CAAA;QACvC,MAAM,KAAK,MAAM,UAAU,CAAC,EAAE;QAC9B,MAAM,YAAY,MAAM,IAAI,CAAC,SAAS;QACtC,MAAM,YAAY,SAAS,CAAC,KAAK,IAAI,CAAC;QACtC,MAAM,YAAY,YAAY;QAC9B,MAAM,0BAA0B,sBAAsB,IAAI;QAC1D,MAAM,cAAc,eAAe;YACjC,WAAW,eAAe,SAAS;YACnC;QACF;QACA,IAAI,yBAAyB;YAC3B,IAAI,aAAa;gBACf,OAAO,YAAY,SAAS,CAAC,KAAK,KAAK,CAAC,GAAG,aAAa,YAAY,SAAS,CAAC,KAAK,GAAG,CAAC,GAAG;YAC5F;YACA,OAAO,cAAc,SAAS,CAAC,KAAK,KAAK,CAAC,GAAG,eAAe,aAAa,cAAc,SAAS,CAAC,KAAK,GAAG,CAAC,GAAG,eAAe;QAC9H;QACA,IAAI,aAAa;YACf,OAAO,YAAY,SAAS,CAAC,KAAK,KAAK,CAAC,GAAG,eAAe,aAAa,YAAY,SAAS,CAAC,KAAK,GAAG,CAAC,GAAG,eAAe;QAC1H;QACA,OAAO,cAAc,SAAS,CAAC,KAAK,KAAK,CAAC,GAAG,aAAa,cAAc,SAAS,CAAC,KAAK,GAAG,CAAC,GAAG;IAChG;IACA,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IACA,MAAM,SAAS;QACb;QACA,WAAW,eAAe,SAAS;QACnC,IAAI;YACF,MAAM;YACN,SAAS;gBACP,aAAa,YAAY,UAAU,CAAC,EAAE;gBACtC,aAAa,YAAY,UAAU,CAAC,EAAE;YACxC;QACF;IACF;IACA,OAAO;AACT;AAEA,IAAI,gBAAgB,CAAC,EACnB,UAAU,EACV,SAAS,EACT,UAAU,EACV,UAAU,EACV,cAAc,EACd,QAAQ,EACR,aAAa,EACd;IACC,MAAM,gBAAgB,qBAAqB,UAAU,IAAI,CAAC,SAAS,EAAE;IACrE,MAAM,gBAAgB,iBAAiB;QACrC;QACA;QACA;IACF;IACA,IAAI,CAAC,eAAe;QAClB,OAAO;IACT;IACA,MAAM,cAAc,UAAU,CAAC,cAAc;IAC7C,MAAM,oBAAoB,6BAA6B,YAAY,UAAU,CAAC,EAAE,EAAE;IAClF,MAAM,mCAAmC,oBAAoB,aAAa;IAC1E,OAAO,iBAAiB;QACtB;QACA;QACA;QACA;QACA;QACA;IACF,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;QACA,MAAM,eAAe,SAAS;QAC9B;QACA;IACF;AACF;AAEA,IAAI,oBAAoB,CAAC,YAAY,UAAY,CAAC;QAChD,GAAG,UAAU;QACb,CAAC,QAAQ,UAAU,CAAC,EAAE,CAAC,EAAE;IAC3B,CAAC;AAED,MAAM,yBAAyB,CAAC,EAC9B,cAAc,EACd,MAAM,EACN,UAAU,EACX;IACC,MAAM,OAAO,kBAAkB;IAC/B,MAAM,MAAM,kBAAkB;IAC9B,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IACA,IAAI,SAAS,KAAK;QAChB,OAAO;IACT;IACA,MAAM,gBAAgB,UAAU,CAAC,KAAK;IACtC,IAAI,CAAC,cAAc,OAAO,CAAC,eAAe,EAAE;QAC1C,OAAO;IACT;IACA,MAAM,UAAU,kBAAkB;IAClC,OAAO,kBAAkB,YAAY;AACvC;AACA,IAAI,wBAAwB,CAAC,EAC3B,SAAS,EACT,UAAU,EACV,UAAU,EACV,cAAc,EACd,MAAM,EACP;IACC,MAAM,UAAU,uBAAuB;QACrC;QACA;QACA;IACF;IACA,MAAM,SAAS,kBAAkB;IACjC,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IACA,MAAM,YAAY,UAAU,CAAC,OAAO;IACpC,IAAI,SAAS,WAAW,YAAY;QAClC,OAAO;IACT;IACA,IAAI,UAAU,OAAO,CAAC,eAAe,EAAE;QACrC,OAAO;IACT;IACA,MAAM,UAAU,eAAe,WAAW,WAAW;IACrD,OAAO,kBAAkB,SAAS;AACpC;AAEA,IAAI,SAAS,CAAC,EACZ,KAAK,EACL,iBAAiB,qBAAqB,EACtC,YAAY,gBAAgB,EAC5B,UAAU,cAAc,EACxB,QAAQ,YAAY,EACpB,iBAAiB,EAClB;IACC,MAAM,WAAW,kBAAkB,MAAM,QAAQ;IACjD,MAAM,aAAa,oBAAoB,MAAM,UAAU;IACvD,MAAM,kBAAkB,yBAAyB,MAAM,OAAO,CAAC,MAAM,CAAC,SAAS;IAC/E,MAAM,SAAS,SAAS,iBAAiB,MAAM,OAAO,CAAC,MAAM,CAAC,SAAS;IACvE,MAAM,SAAS;QACb;QACA,WAAW;QACX,iBAAiB,IAAI,MAAM,OAAO,CAAC,MAAM,CAAC,eAAe,EAAE;IAC7D;IACA,MAAM,OAAO;QACX,WAAW,IAAI,OAAO,SAAS,EAAE,SAAS,MAAM,CAAC,OAAO;QACxD,iBAAiB,IAAI,OAAO,eAAe,EAAE,SAAS,MAAM,CAAC,OAAO;QACpE,QAAQ,IAAI,OAAO,MAAM,EAAE,SAAS,MAAM,CAAC,IAAI,CAAC,KAAK;IACvD;IACA,MAAM,UAAU;QACd;QACA;IACF;IACA,IAAI,MAAM,KAAK,KAAK,cAAc;QAChC,OAAO;YACL,GAAG,KAAK;YACR;YACA;YACA;QACF;IACF;IACA,MAAM,YAAY,WAAW,UAAU,CAAC,MAAM,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;IACpE,MAAM,YAAY,gBAAgB,cAAc;QAC9C,YAAY,KAAK,MAAM;QACvB;QACA,YAAY,WAAW,UAAU;QACjC,YAAY,WAAW,UAAU;QACjC,gBAAgB,MAAM,MAAM;QAC5B;QACA,eAAe,MAAM,aAAa;IACpC;IACA,MAAM,0BAA0B,sBAAsB;QACpD;QACA,QAAQ;QACR,gBAAgB,MAAM,MAAM;QAC5B,YAAY,WAAW,UAAU;QACjC,YAAY,WAAW,UAAU;IACnC;IACA,MAAM,SAAS;QACb,GAAG,KAAK;QACR;QACA,YAAY;YACV,YAAY,WAAW,UAAU;YACjC,YAAY;QACd;QACA,QAAQ;QACR;QACA,mBAAmB,qBAAqB;QACxC,oBAAoB,oBAAoB,QAAQ;IAClD;IACA,OAAO;AACT;AAEA,SAAS,cAAc,GAAG,EAAE,UAAU;IACpC,OAAO,IAAI,GAAG,CAAC,CAAA,KAAM,UAAU,CAAC,GAAG;AACrC;AACA,IAAI,YAAY,CAAC,EACf,MAAM,EACN,QAAQ,EACR,UAAU,EACV,WAAW,EACX,kBAAkB,EACnB;IACC,MAAM,OAAO,OAAO,SAAS;IAC7B,MAAM,gBAAgB,cAAc,KAAK,GAAG,EAAE;IAC9C,MAAM,YAAY,sBAAsB;QACtC;QACA;QACA,aAAa,OAAO,WAAW;QAC/B,UAAU,SAAS,KAAK;QACxB;QACA;IACF;IACA,OAAO;QACL,GAAG,MAAM;QACT;IACF;AACF;AAEA,IAAI,2BAA2B,CAAC,EAC9B,MAAM,EACN,SAAS,EACT,SAAS,EACT,UAAU,EACV,QAAQ,EACR,aAAa,EACd;IACC,MAAM,sBAAsB,iCAAiC;QAC3D;QACA;QACA;QACA;QACA;IACF;IACA,OAAO,iCAAiC;QACtC;QACA;QACA;IACF;AACF;AAEA,IAAI,cAAc,CAAC,EACjB,KAAK,EACL,YAAY,gBAAgB,EAC5B,UAAU,cAAc,EACzB;IACC,CAAC,CAAC,MAAM,YAAY,KAAK,MAAM,IAAI,uCAAwC,qDAA4B,KAAK;IAC5G,MAAM,uBAAuB,MAAM,MAAM;IACzC,MAAM,WAAW,kBAAkB,MAAM,QAAQ;IACjD,MAAM,aAAa,oBAAoB,MAAM,UAAU;IACvD,MAAM,EACJ,UAAU,EACV,UAAU,EACX,GAAG;IACJ,MAAM,YAAY,UAAU,CAAC,MAAM,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;IACzD,MAAM,SAAS,kBAAkB;IACjC,CAAC,SAAS,uCAAwC,UAAU,OAAO,6FAAoE,KAAK;IAC5I,MAAM,cAAc,UAAU,CAAC,OAAO;IACtC,MAAM,SAAS,UAAU;QACvB,QAAQ;QACR;QACA;QACA;IACF;IACA,MAAM,kBAAkB,yBAAyB;QAC/C;QACA;QACA,WAAW;QACX;QACA;QACA,eAAe,MAAM,aAAa;IACpC;IACA,OAAO,OAAO;QACZ;QACA;QACA;QACA;QACA;IACF;AACF;AAEA,IAAI,kBAAkB,CAAA,aAAc,CAAC;QACnC,OAAO,WAAW,KAAK;QACvB,aAAa,WAAW,WAAW;IACrC,CAAC;AAED,IAAI,gBAAgB,CAAC,EACnB,SAAS,EACT,IAAI,EACJ,UAAU,EACV,QAAQ,EACT;IACC,MAAM,cAAc,eAAe,KAAK,IAAI,EAAE,UAAU,UAAU;IAClE,MAAM,aAAa,6BAA6B,KAAK,UAAU,CAAC,EAAE,EAAE;IACpE,MAAM,WAAW,WAAW,OAAO,CAAC;IACpC,CAAC,CAAC,aAAa,CAAC,CAAC,IAAI,uCAAwC,UAAU,OAAO,sFAA6D,KAAK;IAChJ,MAAM,gBAAgB,WAAW,KAAK,CAAC,WAAW;IAClD,MAAM,WAAW,cAAc,MAAM,CAAC,CAAC,UAAU;QAC/C,QAAQ,CAAC,KAAK,UAAU,CAAC,EAAE,CAAC,GAAG;QAC/B,OAAO;IACT,GAAG,CAAC;IACJ,MAAM,gBAAgB;QACpB,eAAe,KAAK,UAAU,CAAC,IAAI,KAAK;QACxC;QACA;IACF;IACA,MAAM,YAAY,sBAAsB;QACtC;QACA,aAAa;QACb;QACA,MAAM;QACN,UAAU,SAAS,KAAK;QACxB,oBAAoB;IACtB;IACA,MAAM,SAAS;QACb;QACA;QACA,IAAI;YACF,MAAM;YACN,aAAa,gBAAgB,UAAU,UAAU;QACnD;IACF;IACA,OAAO;QACL;QACA;IACF;AACF;AAEA,IAAI,oBAAoB,CAAC,YAAY,UAAY,CAAC;QAChD,YAAY,WAAW,UAAU;QACjC,YAAY,kBAAkB,WAAW,UAAU,EAAE;IACvD,CAAC;AAED,MAAM,QAAQ,CAAA;IACZ,wCAA2C;QACzC;YACE;QACF;IACF;AACF;AACA,MAAM,SAAS,CAAA;IACb,wCAA2C;QACzC;YACE;QACF;IACF;AACF;AAEA,IAAI,kBAAkB,CAAC,EACrB,SAAS,EACT,QAAQ,QAAQ,EAChB,mBAAmB,EACpB;IACC,MAAM,SAAS,CAAA,GAAA,yKAAA,CAAA,SAAM,AAAD,EAAE,UAAU,MAAM,EAAE;IACxC,MAAM,OAAO,CAAA,GAAA,yKAAA,CAAA,aAAU,AAAD,EAAE,QAAQ;IAChC,MAAM,QAAQ;QACZ,GAAG,SAAS;QACZ,aAAa;YACX,GAAG,UAAU,WAAW;YACxB;QACF;QACA;QACA;IACF;IACA,OAAO;AACT;AAEA,IAAI,WAAW,CAAA;IACb,MAAM,QAAQ,UAAU,KAAK;IAC7B,CAAC,QAAQ,uCAAwC,UAAU,OAAO,+EAAsD,KAAK;IAC7H,OAAO;AACT;AAEA,IAAI,kCAAkC,CAAC,EACrC,SAAS,EACT,iBAAiB,EACjB,QAAQ,EACT;IACC,MAAM,qBAAqB,SAAS,MAAM,CAAC,IAAI,CAAC,KAAK;IACrD,OAAO,UAAU,GAAG,CAAC,CAAA;QACnB,MAAM,cAAc,UAAU,UAAU,CAAC,WAAW;QACpD,MAAM,WAAW,iBAAiB,CAAC,YAAY;QAC/C,MAAM,QAAQ,SAAS;QACvB,MAAM,wBAAwB,MAAM,MAAM,CAAC,IAAI,CAAC,KAAK;QACrD,MAAM,cAAc,IAAI,oBAAoB;QAC5C,MAAM,QAAQ,gBAAgB;YAC5B;YACA,QAAQ;YACR,qBAAqB,SAAS,MAAM,CAAC,OAAO;QAC9C;QACA,OAAO;IACT;AACF;AAEA,IAAI,gCAAgC,CAAC,EACnC,KAAK,EACL,SAAS,EACV;IACC;IACA,MAAM,mBAAmB,UAAU,QAAQ,CAAC,GAAG,CAAC,CAAA;QAC9C,MAAM,WAAW,MAAM,UAAU,CAAC,UAAU,CAAC,OAAO,WAAW,CAAC;QAChE,MAAM,WAAW,gBAAgB,UAAU,OAAO,MAAM;QACxD,OAAO;IACT;IACA,MAAM,aAAa;QACjB,GAAG,MAAM,UAAU,CAAC,UAAU;QAC9B,GAAG,eAAe,iBAAiB;IACrC;IACA,MAAM,mBAAmB,eAAe,gCAAgC;QACtE,WAAW,UAAU,SAAS;QAC9B,mBAAmB;QACnB,UAAU,MAAM,QAAQ;IAC1B;IACA,MAAM,aAAa;QACjB,GAAG,MAAM,UAAU,CAAC,UAAU;QAC9B,GAAG,gBAAgB;IACrB;IACA,UAAU,QAAQ,CAAC,OAAO,CAAC,CAAA;QACzB,OAAO,UAAU,CAAC,GAAG;IACvB;IACA,MAAM,aAAa;QACjB;QACA;IACF;IACA,MAAM,YAAY,kBAAkB,MAAM,MAAM;IAChD,MAAM,UAAU,YAAY,WAAW,UAAU,CAAC,UAAU,GAAG;IAC/D,MAAM,YAAY,WAAW,UAAU,CAAC,MAAM,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;IACpE,MAAM,OAAO,WAAW,UAAU,CAAC,MAAM,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;IAC/D,MAAM,EACJ,QAAQ,YAAY,EACpB,aAAa,EACd,GAAG,cAAc;QAChB;QACA;QACA;QACA,UAAU,MAAM,QAAQ;IAC1B;IACA,MAAM,iBAAiB,WAAW,QAAQ,gBAAgB,GAAG,MAAM,MAAM,GAAG;IAC5E,MAAM,SAAS,cAAc;QAC3B,YAAY,MAAM,OAAO,CAAC,IAAI,CAAC,MAAM;QACrC,WAAW,WAAW,UAAU,CAAC,MAAM,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;QAC7D,YAAY,WAAW,UAAU;QACjC,YAAY,WAAW,UAAU;QACjC;QACA,UAAU,MAAM,QAAQ;QACxB;IACF;IACA;IACA,MAAM,gBAAgB;QACpB,GAAG,KAAK;QACR,OAAO;QACP;QACA;QACA;QACA;QACA,oBAAoB;IACtB;IACA,IAAI,MAAM,KAAK,KAAK,cAAc;QAChC,OAAO;IACT;IACA,MAAM,cAAc;QAClB,GAAG,aAAa;QAChB,OAAO;QACP,QAAQ,MAAM,MAAM;QACpB,WAAW;IACb;IACA,OAAO;AACT;AAEA,MAAM,aAAa,CAAA,QAAS,MAAM,YAAY,KAAK;AACnD,MAAM,sBAAsB,CAAC,OAAO,SAAS;IAC3C,MAAM,aAAa,kBAAkB,MAAM,UAAU,EAAE;IACvD,IAAI,CAAC,WAAW,UAAU,mBAAmB;QAC3C,OAAO,OAAO;YACZ;YACA;QACF;IACF;IACA,OAAO,YAAY;QACjB;QACA;IACF;AACF;AACA,SAAS,wBAAwB,KAAK;IACpC,IAAI,MAAM,UAAU,IAAI,MAAM,YAAY,KAAK,QAAQ;QACrD,OAAO;YACL,GAAG,KAAK;YACR,mBAAmB;QACrB;IACF;IACA,OAAO;AACT;AACA,MAAM,SAAS;IACb,OAAO;IACP,WAAW;IACX,aAAa;AACf;AACA,IAAI,UAAU,CAAC,QAAQ,MAAM,EAAE;IAC7B,IAAI,OAAO,IAAI,KAAK,SAAS;QAC3B,OAAO;YACL,GAAG,MAAM;YACT,aAAa;QACf;IACF;IACA,IAAI,OAAO,IAAI,KAAK,mBAAmB;QACrC,CAAC,CAAC,MAAM,KAAK,KAAK,MAAM,IAAI,uCAAwC,UAAU,OAAO,yFAAgE,KAAK;QAC1J,MAAM,EACJ,QAAQ,EACR,eAAe,EACf,QAAQ,EACR,UAAU,EACV,YAAY,EACb,GAAG,OAAO,OAAO;QAClB,MAAM,YAAY,WAAW,UAAU,CAAC,SAAS,SAAS,CAAC,EAAE,CAAC;QAC9D,MAAM,OAAO,WAAW,UAAU,CAAC,SAAS,SAAS,CAAC,EAAE,CAAC;QACzD,MAAM,SAAS;YACb,WAAW;YACX,iBAAiB,UAAU,MAAM,CAAC,SAAS,CAAC,MAAM;YAClD,QAAQ;QACV;QACA,MAAM,UAAU;YACd;YACA,MAAM;gBACJ,WAAW,IAAI,OAAO,SAAS,EAAE,SAAS,MAAM,CAAC,OAAO;gBACxD,iBAAiB,IAAI,OAAO,SAAS,EAAE,SAAS,MAAM,CAAC,OAAO;gBAC9D,QAAQ,IAAI,OAAO,SAAS,EAAE,SAAS,MAAM,CAAC,IAAI,CAAC,KAAK;YAC1D;QACF;QACA,MAAM,wBAAwB,gBAAgB,WAAW,UAAU,EAAE,KAAK,CAAC,CAAA,OAAQ,CAAC,KAAK,aAAa;QACtG,MAAM,EACJ,MAAM,EACN,aAAa,EACd,GAAG,cAAc;YAChB;YACA;YACA,YAAY,WAAW,UAAU;YACjC;QACF;QACA,MAAM,SAAS;YACb,OAAO;YACP,YAAY;YACZ;YACA;YACA;YACA;YACA,SAAS;YACT;YACA;YACA;YACA,cAAc;YACd;YACA,mBAAmB;YACnB,oBAAoB;QACtB;QACA,OAAO;IACT;IACA,IAAI,OAAO,IAAI,KAAK,uBAAuB;QACzC,IAAI,MAAM,KAAK,KAAK,gBAAgB,MAAM,KAAK,KAAK,gBAAgB;YAClE,OAAO;QACT;QACA,CAAC,CAAC,MAAM,KAAK,KAAK,UAAU,IAAI,uCAAwC,UAAU,OAAO,CAAC,mCAAmC,EAAE,MAAM,KAAK,EAAE,2CAAkB,KAAK;QACnK,MAAM,SAAS;YACb,GAAG,KAAK;YACR,OAAO;QACT;QACA,OAAO;IACT;IACA,IAAI,OAAO,IAAI,KAAK,0BAA0B;QAC5C,CAAC,CAAC,MAAM,KAAK,KAAK,gBAAgB,MAAM,KAAK,KAAK,cAAc,IAAI,uCAAwC,UAAU,OAAO,CAAC,WAAW,EAAE,OAAO,IAAI,CAAC,mBAAmB,EAAE,MAAM,KAAK,EAAE,2CAAkB,KAAK;QAChN,OAAO,8BAA8B;YACnC;YACA,WAAW,OAAO,OAAO;QAC3B;IACF;IACA,IAAI,OAAO,IAAI,KAAK,QAAQ;QAC1B,IAAI,MAAM,KAAK,KAAK,gBAAgB;YAClC,OAAO;QACT;QACA,CAAC,kBAAkB,SAAS,uCAAwC,UAAU,OAAO,GAAG,OAAO,IAAI,CAAC,wBAAwB,EAAE,MAAM,KAAK,EAAE,2CAAkB,KAAK;QAClK,MAAM,EACJ,QAAQ,eAAe,EACxB,GAAG,OAAO,OAAO;QAClB,IAAI,UAAU,iBAAiB,MAAM,OAAO,CAAC,MAAM,CAAC,SAAS,GAAG;YAC9D,OAAO;QACT;QACA,OAAO,OAAO;YACZ;YACA;YACA,QAAQ,WAAW,SAAS,MAAM,MAAM,GAAG;QAC7C;IACF;IACA,IAAI,OAAO,IAAI,KAAK,2BAA2B;QAC7C,IAAI,MAAM,KAAK,KAAK,gBAAgB;YAClC,OAAO,wBAAwB;QACjC;QACA,IAAI,MAAM,KAAK,KAAK,cAAc;YAChC,OAAO,wBAAwB;QACjC;QACA,CAAC,kBAAkB,SAAS,uCAAwC,UAAU,OAAO,GAAG,OAAO,IAAI,CAAC,wBAAwB,EAAE,MAAM,KAAK,EAAE,2CAAkB,KAAK;QAClK,MAAM,EACJ,EAAE,EACF,SAAS,EACV,GAAG,OAAO,OAAO;QAClB,MAAM,SAAS,MAAM,UAAU,CAAC,UAAU,CAAC,GAAG;QAC9C,IAAI,CAAC,QAAQ;YACX,OAAO;QACT;QACA,MAAM,WAAW,gBAAgB,QAAQ;QACzC,OAAO,oBAAoB,OAAO,UAAU;IAC9C;IACA,IAAI,OAAO,IAAI,KAAK,+BAA+B;QACjD,IAAI,MAAM,KAAK,KAAK,gBAAgB;YAClC,OAAO;QACT;QACA,CAAC,kBAAkB,SAAS,uCAAwC,UAAU,OAAO,CAAC,2CAA2C,EAAE,MAAM,KAAK,EAAE,2CAAkB,KAAK;QACvK,MAAM,EACJ,EAAE,EACF,SAAS,EACV,GAAG,OAAO,OAAO;QAClB,MAAM,SAAS,MAAM,UAAU,CAAC,UAAU,CAAC,GAAG;QAC9C,CAAC,SAAS,uCAAwC,UAAU,OAAO,CAAC,0BAA0B,EAAE,GAAG,6BAA6B,CAAC,2CAAkB,KAAK;QACxJ,CAAC,CAAC,OAAO,SAAS,KAAK,SAAS,IAAI,uCAAwC,UAAU,OAAO,CAAC,qCAAqC,EAAE,OAAO,WAAW;wBACnI,EAAE,OAAO,OAAO,SAAS,GAAG,2CAAkB,KAAK;QACvE,MAAM,UAAU;YACd,GAAG,MAAM;YACT;QACF;QACA,OAAO,oBAAoB,OAAO,SAAS;IAC7C;IACA,IAAI,OAAO,IAAI,KAAK,uCAAuC;QACzD,IAAI,MAAM,KAAK,KAAK,gBAAgB;YAClC,OAAO;QACT;QACA,CAAC,kBAAkB,SAAS,uCAAwC,UAAU,OAAO,CAAC,2CAA2C,EAAE,MAAM,KAAK,EAAE,2CAAkB,KAAK;QACvK,MAAM,EACJ,EAAE,EACF,gBAAgB,EACjB,GAAG,OAAO,OAAO;QAClB,MAAM,SAAS,MAAM,UAAU,CAAC,UAAU,CAAC,GAAG;QAC9C,CAAC,SAAS,uCAAwC,UAAU,OAAO,CAAC,0BAA0B,EAAE,GAAG,sCAAsC,CAAC,2CAAkB,KAAK;QACjK,CAAC,CAAC,OAAO,gBAAgB,KAAK,gBAAgB,IAAI,uCAAwC,UAAU,OAAO,CAAC,4CAA4C,EAAE,OAAO,kBAAkB;wBAC/J,EAAE,OAAO,OAAO,gBAAgB,GAAG,2CAAkB,KAAK;QAC9E,MAAM,UAAU;YACd,GAAG,MAAM;YACT;QACF;QACA,OAAO,oBAAoB,OAAO,SAAS;IAC7C;IACA,IAAI,OAAO,IAAI,KAAK,yBAAyB;QAC3C,IAAI,MAAM,KAAK,KAAK,kBAAkB,MAAM,KAAK,KAAK,kBAAkB;YACtE,OAAO;QACT;QACA,CAAC,kBAAkB,SAAS,uCAAwC,UAAU,OAAO,CAAC,+BAA+B,EAAE,MAAM,KAAK,EAAE,2CAAkB,KAAK;QAC3J,CAAC,MAAM,qBAAqB,GAAG,uCAAwC,UAAU,OAAO,wGAA+E,KAAK;QAC5K,MAAM,YAAY,OAAO,OAAO,CAAC,SAAS;QAC1C,IAAI,UAAU,MAAM,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,YAAY;YACvD,OAAO,wBAAwB;QACjC;QACA,MAAM,WAAW,eAAe,MAAM,QAAQ,EAAE;QAChD,IAAI,WAAW,QAAQ;YACrB,OAAO,YAAY;gBACjB;gBACA;YACF;QACF;QACA,OAAO,OAAO;YACZ;YACA;QACF;IACF;IACA,IAAI,OAAO,IAAI,KAAK,8BAA8B;QAChD,IAAI,CAAC,kBAAkB,QAAQ;YAC7B,OAAO;QACT;QACA,MAAM,YAAY,OAAO,OAAO,CAAC,SAAS;QAC1C,IAAI,UAAU,WAAW,MAAM,QAAQ,CAAC,MAAM,CAAC,GAAG,GAAG;YACnD,OAAO;QACT;QACA,MAAM,gBAAgB;YACpB,GAAG,MAAM,QAAQ;YACjB,QAAQ;gBACN,GAAG,MAAM,QAAQ,CAAC,MAAM;gBACxB,KAAK;YACP;QACF;QACA,OAAO;YACL,GAAG,KAAK;YACR,UAAU;QACZ;IACF;IACA,IAAI,OAAO,IAAI,KAAK,aAAa,OAAO,IAAI,KAAK,eAAe,OAAO,IAAI,KAAK,eAAe,OAAO,IAAI,KAAK,cAAc;QAC3H,IAAI,MAAM,KAAK,KAAK,gBAAgB,MAAM,KAAK,KAAK,gBAAgB;YAClE,OAAO;QACT;QACA,CAAC,CAAC,MAAM,KAAK,KAAK,UAAU,IAAI,uCAAwC,UAAU,OAAO,GAAG,OAAO,IAAI,CAAC,qCAAqC,CAAC,2CAAkB,KAAK;QACrK,MAAM,SAAS,gBAAgB;YAC7B;YACA,MAAM,OAAO,IAAI;QACnB;QACA,IAAI,CAAC,QAAQ;YACX,OAAO;QACT;QACA,OAAO,OAAO;YACZ;YACA,QAAQ,OAAO,MAAM;YACrB,iBAAiB,OAAO,eAAe;YACvC,mBAAmB,OAAO,iBAAiB;QAC7C;IACF;IACA,IAAI,OAAO,IAAI,KAAK,gBAAgB;QAClC,MAAM,SAAS,OAAO,OAAO,CAAC,MAAM;QACpC,CAAC,CAAC,MAAM,KAAK,KAAK,YAAY,IAAI,uCAAwC,UAAU,OAAO,gHAAuF,KAAK;QACvL,MAAM,WAAW;YACf,GAAG,KAAK;YACR,OAAO;YACP,WAAW;YACX;QACF;QACA,OAAO;IACT;IACA,IAAI,OAAO,IAAI,KAAK,gBAAgB;QAClC,MAAM,EACJ,SAAS,EACT,YAAY,EACZ,mBAAmB,EACpB,GAAG,OAAO,OAAO;QAClB,CAAC,CAAC,MAAM,KAAK,KAAK,cAAc,MAAM,KAAK,KAAK,cAAc,IAAI,uCAAwC,UAAU,OAAO,CAAC,+BAA+B,EAAE,MAAM,KAAK,EAAE,2CAAkB,KAAK;QACjM,MAAM,SAAS;YACb,OAAO;YACP;YACA;YACA;YACA,YAAY,MAAM,UAAU;QAC9B;QACA,OAAO;IACT;IACA,IAAI,OAAO,IAAI,KAAK,iBAAiB;QACnC,MAAM,EACJ,SAAS,EACV,GAAG,OAAO,OAAO;QAClB,OAAO;YACL,OAAO;YACP;YACA,aAAa;QACf;IACF;IACA,OAAO;AACT;AAEA,SAAS,MAAM,MAAM,EAAE,SAAS;IAC9B,OAAO,kBAAkB,UAAU,UAAU,UAAU,OAAO,IAAI,KAAK;AACzE;AACA,MAAM,uBAAuB,CAAA,OAAQ,CAAC;QACpC,MAAM;QACN,SAAS;IACX,CAAC;AACD,MAAM,SAAS,CAAA,OAAQ,CAAC;QACtB,MAAM;QACN,SAAS;IACX,CAAC;AACD,MAAM,iBAAiB,CAAA,OAAQ,CAAC;QAC9B,MAAM;QACN,SAAS;IACX,CAAC;AACD,MAAM,uBAAuB,CAAA,OAAQ,CAAC;QACpC,MAAM;QACN,SAAS;IACX,CAAC;AACD,MAAM,qBAAqB,IAAM,CAAC;QAChC,MAAM;QACN,SAAS;IACX,CAAC;AACD,MAAM,wBAAwB,CAAA,OAAQ,CAAC;QACrC,MAAM;QACN,SAAS;IACX,CAAC;AACD,MAAM,2BAA2B,CAAA,OAAQ,CAAC;QACxC,MAAM;QACN,SAAS;IACX,CAAC;AACD,MAAM,kCAAkC,CAAA,OAAQ,CAAC;QAC/C,MAAM;QACN,SAAS;IACX,CAAC;AACD,MAAM,OAAO,CAAA,OAAQ,CAAC;QACpB,MAAM;QACN,SAAS;IACX,CAAC;AACD,MAAM,qBAAqB,CAAA,OAAQ,CAAC;QAClC,MAAM;QACN,SAAS;IACX,CAAC;AACD,MAAM,0BAA0B,CAAA,OAAQ,CAAC;QACvC,MAAM;QACN,SAAS;IACX,CAAC;AACD,MAAM,SAAS,IAAM,CAAC;QACpB,MAAM;QACN,SAAS;IACX,CAAC;AACD,MAAM,WAAW,IAAM,CAAC;QACtB,MAAM;QACN,SAAS;IACX,CAAC;AACD,MAAM,YAAY,IAAM,CAAC;QACvB,MAAM;QACN,SAAS;IACX,CAAC;AACD,MAAM,WAAW,IAAM,CAAC;QACtB,MAAM;QACN,SAAS;IACX,CAAC;AACD,MAAM,QAAQ,IAAM,CAAC;QACnB,MAAM;QACN,SAAS;IACX,CAAC;AACD,MAAM,cAAc,CAAA,OAAQ,CAAC;QAC3B,MAAM;QACN,SAAS;IACX,CAAC;AACD,MAAM,eAAe,CAAA,OAAQ,CAAC;QAC5B,MAAM;QACN,SAAS;IACX,CAAC;AACD,MAAM,OAAO,CAAA,OAAQ,CAAC;QACpB,MAAM;QACN,SAAS;IACX,CAAC;AACD,MAAM,cAAc,CAAA,OAAQ,CAAC;QAC3B,MAAM;QACN,SAAS;IACX,CAAC;AACD,MAAM,wBAAwB,IAAM,CAAC;QACnC,MAAM;QACN,SAAS;IACX,CAAC;AAED,SAAS,aAAa,iBAAiB;IACrC,IAAI,kBAAkB,MAAM,IAAI,GAAG;QACjC;IACF;IACA,MAAM,UAAU,kBAAkB,GAAG,CAAC,CAAA,IAAK,EAAE,UAAU,CAAC,KAAK;IAC7D,MAAM,SAAS,CAAC;IAChB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QACvC,MAAM,UAAU,OAAO,CAAC,EAAE;QAC1B,MAAM,WAAW,OAAO,CAAC,IAAI,EAAE;QAC/B,IAAI,YAAY,WAAW,GAAG;YAC5B,MAAM,CAAC,QAAQ,GAAG;QACpB;IACF;IACA,IAAI,CAAC,OAAO,IAAI,CAAC,QAAQ,MAAM,EAAE;QAC/B;IACF;IACA,MAAM,YAAY,QAAQ,GAAG,CAAC,CAAA;QAC5B,MAAM,WAAW,QAAQ,MAAM,CAAC,MAAM;QACtC,OAAO,WAAW,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,GAAG,GAAG,OAAO;IAC/C,GAAG,IAAI,CAAC;IACR,uCAAwC,QAAQ,CAAC;;;;;IAK/C,EAAE,UAAU;EACd,CAAC;AACH;AACA,SAAS,mBAAmB,QAAQ,EAAE,UAAU;IAC9C,wCAA2C;QACzC,MAAM,oBAAoB,6BAA6B,SAAS,SAAS,CAAC,EAAE,EAAE,WAAW,UAAU;QACnG,aAAa;IACf;AACF;AAEA,IAAI,OAAO,CAAA,UAAW,CAAC,EACrB,QAAQ,EACR,QAAQ,EACT,GAAK,CAAA,OAAQ,CAAA;gBACZ,IAAI,CAAC,MAAM,QAAQ,SAAS;oBAC1B,KAAK;oBACL;gBACF;gBACA,MAAM,EACJ,EAAE,EACF,eAAe,EACf,YAAY,EACb,GAAG,OAAO,OAAO;gBAClB,MAAM,UAAU;gBAChB,IAAI,QAAQ,KAAK,KAAK,kBAAkB;oBACtC,SAAS,aAAa;wBACpB,WAAW,QAAQ,SAAS;oBAC9B;gBACF;gBACA,CAAC,CAAC,WAAW,KAAK,KAAK,MAAM,IAAI,uCAAwC,UAAU,OAAO,6EAAoD,KAAK;gBACnJ,SAAS;gBACT,SAAS,qBAAqB;oBAC5B,aAAa;oBACb;gBACF;gBACA,MAAM,gBAAgB;oBACpB,0BAA0B,iBAAiB;gBAC7C;gBACA,MAAM,UAAU;oBACd,aAAa;oBACb;gBACF;gBACA,MAAM,EACJ,QAAQ,EACR,UAAU,EACV,QAAQ,EACT,GAAG,QAAQ,eAAe,CAAC;gBAC5B,mBAAmB,UAAU;gBAC7B,SAAS,eAAe;oBACtB;oBACA;oBACA;oBACA;oBACA;gBACF;YACF;AAEA,IAAI,QAAQ,CAAA,UAAW,IAAM,CAAA,OAAQ,CAAA;gBACnC,IAAI,MAAM,QAAQ,oBAAoB;oBACpC,QAAQ,QAAQ;gBAClB;gBACA,IAAI,MAAM,QAAQ,iBAAiB;oBACjC,QAAQ,QAAQ,CAAC,OAAO,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM;gBACzD;gBACA,IAAI,MAAM,QAAQ,YAAY,MAAM,QAAQ,kBAAkB;oBAC5D,QAAQ,OAAO;gBACjB;gBACA,KAAK;YACP;AAEA,MAAM,SAAS;IACb,aAAa;IACb,MAAM;AACR;AACA,MAAM,UAAU;IACd,SAAS;QACP,MAAM;QACN,WAAW;IACb;IACA,OAAO;QACL,MAAM;IACR;AACF;AACA,MAAM,UAAU;IACd,aAAa;IACb,aAAa;IACb,aAAa;AACf;AACA,MAAM,oBAAoB,GAAG,QAAQ,WAAW,CAAC,EAAE,EAAE,OAAO,WAAW,EAAE;AACzE,MAAM,cAAc;IAClB,OAAO,CAAC,QAAQ,EAAE,mBAAmB;IACrC,MAAM,CAAC,UAAU,EAAE,kBAAkB,UAAU,EAAE,mBAAmB;IACpE,MAAM,CAAA;QACJ,MAAM,SAAS,GAAG,SAAS,EAAE,EAAE,OAAO,IAAI,EAAE;QAC5C,OAAO,CAAC,UAAU,EAAE,OAAO,UAAU,EAAE,QAAQ;IACjD;IACA,aAAa,CAAC,UAAU,EAAE,mBAAmB;IAC7C,aAAa,CAAC,OAAO,EAAE,kBAAkB,QAAQ,EAAE,kBAAkB,SAAS,EAAE,mBAAmB;AACrG;AACA,MAAM,SAAS,CAAA,SAAU,UAAU,QAAQ,UAAU,YAAY,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC;AAC1G,MAAM,aAAa;IACjB;IACA,MAAM,CAAC,QAAQ;QACb,MAAM,YAAY,OAAO;QACzB,IAAI,CAAC,WAAW;YACd,OAAO;QACT;QACA,IAAI,CAAC,aAAa;YAChB,OAAO;QACT;QACA,OAAO,GAAG,UAAU,OAAO,EAAE,QAAQ,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;IACpD;AACF;AAEA,MAAM,EACJ,WAAW,EACX,WAAW,EACZ,GAAG;AACJ,MAAM,gBAAgB,cAAc;AACpC,MAAM,wBAAwB;AAC9B,MAAM,qBAAqB;AAC3B,IAAI,kBAAkB,CAAC,EACrB,OAAO,EACP,WAAW,EACX,MAAM,EACP;IACC,MAAM,aAAa,SAAS,SAAS;IACrC,IAAI,cAAc,GAAG;QACnB,OAAO;IACT;IACA,IAAI,cAAc,uBAAuB;QACvC,OAAO;IACT;IACA,MAAM,aAAa,aAAa;IAChC,MAAM,WAAW,cAAc,gBAAgB;IAC/C,MAAM,eAAe,WAAW,WAAW,WAAW,qBAAqB;IAC3E,OAAO,OAAO,aAAa,OAAO,CAAC;AACrC;AAEA,IAAI,yBAAyB,CAAC,EAC5B,MAAM,EACN,SAAS,EACT,UAAU,EACV,QAAQ,EACR,aAAa,EACd;IACC,MAAM,EACJ,UAAU,EACV,UAAU,EACX,GAAG;IACJ,MAAM,cAAc,kBAAkB;IACtC,MAAM,cAAc,cAAc,UAAU,CAAC,YAAY,GAAG;IAC5D,MAAM,OAAO,UAAU,CAAC,UAAU,UAAU,CAAC,WAAW,CAAC;IACzD,MAAM,kBAAkB,yBAAyB;QAC/C;QACA;QACA;QACA;QACA,WAAW,eAAe;QAC1B;IACF;IACA,MAAM,SAAS,SAAS,iBAAiB,UAAU,MAAM,CAAC,SAAS,CAAC,MAAM;IAC1E,OAAO;AACT;AAEA,IAAI,gBAAgB,CAAC,EACnB,UAAU,EACV,MAAM,EACN,UAAU,EACV,IAAI,EACJ,QAAQ,EACR,YAAY,EACb;IACC,IAAI,CAAC,WAAW,EAAE,IAAI,WAAW,QAAQ;QACvC,MAAM,uBAAuB,UAAU;YACrC;YACA,QAAQ;YACR,aAAa;YACb;YACA,oBAAoB;QACtB;QACA,OAAO;YACL,QAAQ;YACR,wBAAwB;QAC1B;IACF;IACA,IAAI,WAAW,EAAE,CAAC,IAAI,KAAK,WAAW;QACpC,OAAO;YACL,QAAQ;YACR,wBAAwB;QAC1B;IACF;IACA,MAAM,kBAAkB;QACtB,GAAG,UAAU;QACb,WAAW;IACb;IACA,OAAO;QACL,QAAQ;QACR,wBAAwB;IAC1B;AACF;AAEA,MAAM,iBAAiB,CAAC,EACtB,QAAQ,EACR,QAAQ,EACT,GAAK,CAAA,OAAQ,CAAA;YACZ,IAAI,CAAC,MAAM,QAAQ,SAAS;gBAC1B,KAAK;gBACL;YACF;YACA,MAAM,QAAQ;YACd,MAAM,SAAS,OAAO,OAAO,CAAC,MAAM;YACpC,IAAI,MAAM,KAAK,KAAK,cAAc;gBAChC,SAAS,YAAY;oBACnB;gBACF;gBACA;YACF;YACA,IAAI,MAAM,KAAK,KAAK,QAAQ;gBAC1B;YACF;YACA,MAAM,mBAAmB,MAAM,KAAK,KAAK,kBAAkB,MAAM,SAAS;YAC1E,CAAC,CAAC,mBAAmB,uCAAwC,UAAU,OAAO,wGAA+E,KAAK;YAClK,CAAC,CAAC,MAAM,KAAK,KAAK,cAAc,MAAM,KAAK,KAAK,cAAc,IAAI,uCAAwC,UAAU,OAAO,CAAC,sBAAsB,EAAE,MAAM,KAAK,EAAE,2CAAkB,KAAK;YACxL,MAAM,WAAW,MAAM,QAAQ;YAC/B,MAAM,aAAa,MAAM,UAAU;YACnC,MAAM,YAAY,WAAW,UAAU,CAAC,MAAM,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACpE,MAAM,EACJ,MAAM,EACN,sBAAsB,EACvB,GAAG,cAAc;gBAChB;gBACA,YAAY,MAAM,MAAM;gBACxB,eAAe,MAAM,aAAa;gBAClC,cAAc,MAAM,YAAY;gBAChC,MAAM,MAAM,UAAU,CAAC,UAAU,CAAC,MAAM,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC9D,UAAU,MAAM,QAAQ;gBACxB,YAAY,MAAM,UAAU,CAAC,UAAU;YACzC;YACA,MAAM,cAAc,yBAAyB,kBAAkB,UAAU;YACzE,MAAM,UAAU,yBAAyB,cAAc,UAAU;YACjE,MAAM,SAAS;gBACb,OAAO,SAAS,SAAS,CAAC,KAAK;gBAC/B,aAAa,SAAS,SAAS,CAAC,EAAE;YACpC;YACA,MAAM,SAAS;gBACb,aAAa,UAAU,UAAU,CAAC,EAAE;gBACpC,MAAM,UAAU,UAAU,CAAC,IAAI;gBAC/B;gBACA;gBACA,MAAM,MAAM,YAAY;gBACxB;gBACA;YACF;YACA,MAAM,sBAAsB,uBAAuB;gBACjD;gBACA;gBACA;gBACA,UAAU,MAAM,QAAQ;gBACxB,eAAe,MAAM,aAAa;YACpC;YACA,MAAM,YAAY;gBAChB,UAAU,MAAM,QAAQ;gBACxB,eAAe,MAAM,aAAa;gBAClC;gBACA;YACF;YACA,MAAM,sBAAsB,CAAC,UAAU,MAAM,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,wBAAwB,QAAQ,OAAO,OAAO;YAClH,IAAI,CAAC,qBAAqB;gBACxB,SAAS,aAAa;oBACpB;gBACF;gBACA;YACF;YACA,MAAM,eAAe,gBAAgB;gBACnC,SAAS,MAAM,OAAO,CAAC,MAAM,CAAC,MAAM;gBACpC,aAAa;gBACb;YACF;YACA,MAAM,OAAO;gBACX;gBACA;gBACA;YACF;YACA,SAAS,YAAY;QACvB;AAEA,IAAI,kBAAkB,IAAM,CAAC;QAC3B,GAAG,OAAO,WAAW;QACrB,GAAG,OAAO,WAAW;IACvB,CAAC;AAED,SAAS,uBAAuB,MAAM;IACpC,OAAO;QACL,WAAW;QACX,SAAS;YACP,SAAS;YACT,SAAS;QACX;QACA,IAAI,CAAA;YACF,IAAI,MAAM,MAAM,KAAK,UAAU,MAAM,MAAM,KAAK,OAAO,QAAQ,EAAE;gBAC/D;YACF;YACA;QACF;IACF;AACF;AACA,SAAS,kBAAkB,EACzB,cAAc,EACf;IACC,SAAS;QACP,eAAe;IACjB;IACA,MAAM,YAAY,CAAA,GAAA,yJAAA,CAAA,UAAO,AAAD,EAAE;IAC1B,MAAM,UAAU,uBAAuB;IACvC,IAAI,SAAS;IACb,SAAS;QACP,OAAO,WAAW;IACpB;IACA,SAAS;QACP,CAAC,CAAC,aAAa,uCAAwC,UAAU,OAAO,6FAAoE,KAAK;QACjJ,SAAS,WAAW,QAAQ;YAAC;SAAQ;IACvC;IACA,SAAS;QACP,CAAC,aAAa,uCAAwC,UAAU,OAAO,wFAA+D,KAAK;QAC3I,UAAU,MAAM;QAChB;QACA,SAAS;IACX;IACA,OAAO;QACL;QACA;QACA;IACF;AACF;AAEA,MAAM,eAAe,CAAA,SAAU,MAAM,QAAQ,oBAAoB,MAAM,QAAQ,mBAAmB,MAAM,QAAQ;AAChH,MAAM,iBAAiB,CAAA;IACrB,MAAM,WAAW,kBAAkB;QACjC,gBAAgB,CAAA;YACd,MAAM,QAAQ,CAAC,mBAAmB;gBAChC;YACF;QACF;IACF;IACA,OAAO,CAAA,OAAQ,CAAA;YACb,IAAI,CAAC,SAAS,QAAQ,MAAM,MAAM,QAAQ,oBAAoB;gBAC5D,SAAS,KAAK;YAChB;YACA,IAAI,SAAS,QAAQ,MAAM,aAAa,SAAS;gBAC/C,SAAS,IAAI;YACf;YACA,KAAK;QACP;AACF;AAEA,IAAI,sBAAsB,CAAA;IACxB,IAAI,YAAY;IAChB,IAAI,YAAY;IAChB,MAAM,YAAY,WAAW;QAC3B,YAAY;IACd;IACA,MAAM,SAAS,CAAA;QACb,IAAI,WAAW;YACb,uCAAwC,QAAQ;YAChD;QACF;QACA,IAAI,WAAW;YACb,uCAAwC,QAAQ,CAAC;;;MAGjD,CAAC;YACD;QACF;QACA,YAAY;QACZ,SAAS;QACT,aAAa;IACf;IACA,OAAO,SAAS,GAAG,IAAM;IACzB,OAAO;AACT;AAEA,IAAI,kBAAkB;IACpB,MAAM,UAAU,EAAE;IAClB,MAAM,UAAU,CAAA;QACd,MAAM,QAAQ,QAAQ,SAAS,CAAC,CAAA,OAAQ,KAAK,OAAO,KAAK;QACzD,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,uCAAwC,UAAU,OAAO,iEAAwC,KAAK;QACxH,MAAM,CAAC,MAAM,GAAG,QAAQ,MAAM,CAAC,OAAO;QACtC,MAAM,QAAQ;IAChB;IACA,MAAM,MAAM,CAAA;QACV,MAAM,UAAU,WAAW,IAAM,QAAQ;QACzC,MAAM,QAAQ;YACZ;YACA,UAAU;QACZ;QACA,QAAQ,IAAI,CAAC;IACf;IACA,MAAM,QAAQ;QACZ,IAAI,CAAC,QAAQ,MAAM,EAAE;YACnB;QACF;QACA,MAAM,UAAU;eAAI;SAAQ;QAC5B,QAAQ,MAAM,GAAG;QACjB,QAAQ,OAAO,CAAC,CAAA;YACd,aAAa,MAAM,OAAO;YAC1B,MAAM,QAAQ;QAChB;IACF;IACA,OAAO;QACL;QACA;IACF;AACF;AAEA,MAAM,oBAAoB,CAAC,OAAO;IAChC,IAAI,SAAS,QAAQ,UAAU,MAAM;QACnC,OAAO;IACT;IACA,IAAI,SAAS,QAAQ,UAAU,MAAM;QACnC,OAAO;IACT;IACA,OAAO,MAAM,WAAW,KAAK,OAAO,WAAW,IAAI,MAAM,KAAK,KAAK,OAAO,KAAK;AACjF;AACA,MAAM,iBAAiB,CAAC,OAAO;IAC7B,IAAI,SAAS,QAAQ,UAAU,MAAM;QACnC,OAAO;IACT;IACA,IAAI,SAAS,QAAQ,UAAU,MAAM;QACnC,OAAO;IACT;IACA,OAAO,MAAM,WAAW,KAAK,OAAO,WAAW,IAAI,MAAM,WAAW,KAAK,OAAO,WAAW;AAC7F;AACA,MAAM,kBAAkB,CAAC,OAAO;IAC9B,IAAI,UAAU,QAAQ;QACpB,OAAO;IACT;IACA,MAAM,mBAAmB,MAAM,SAAS,CAAC,EAAE,KAAK,OAAO,SAAS,CAAC,EAAE,IAAI,MAAM,SAAS,CAAC,WAAW,KAAK,OAAO,SAAS,CAAC,WAAW,IAAI,MAAM,SAAS,CAAC,IAAI,KAAK,OAAO,SAAS,CAAC,IAAI,IAAI,MAAM,SAAS,CAAC,KAAK,KAAK,OAAO,SAAS,CAAC,KAAK;IACzO,MAAM,mBAAmB,MAAM,SAAS,CAAC,EAAE,KAAK,OAAO,SAAS,CAAC,EAAE,IAAI,MAAM,SAAS,CAAC,IAAI,KAAK,OAAO,SAAS,CAAC,IAAI;IACrH,OAAO,oBAAoB;AAC7B;AAEA,MAAM,cAAc,CAAC,KAAK;IACxB;IACA;IACA;AACF;AACA,MAAM,eAAe,CAAC,UAAU,OAAS,CAAC;QACxC,aAAa,SAAS,SAAS,CAAC,EAAE;QAClC,MAAM,SAAS,SAAS,CAAC,IAAI;QAC7B,QAAQ;YACN,aAAa,SAAS,SAAS,CAAC,EAAE;YAClC,OAAO,SAAS,SAAS,CAAC,KAAK;QACjC;QACA;IACF,CAAC;AACD,SAAS,QAAQ,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,iBAAiB;IAC3D,IAAI,CAAC,WAAW;QACd,SAAS,kBAAkB;QAC3B;IACF;IACA,MAAM,aAAa,oBAAoB;IACvC,MAAM,WAAW;QACf,UAAU;IACZ;IACA,UAAU,MAAM;IAChB,IAAI,CAAC,WAAW,SAAS,IAAI;QAC3B,SAAS,kBAAkB;IAC7B;AACF;AACA,IAAI,eAAe,CAAC,eAAe;IACjC,MAAM,eAAe;IACrB,IAAI,WAAW;IACf,MAAM,gBAAgB,CAAC,aAAa;QAClC,CAAC,CAAC,WAAW,uCAAwC,UAAU,OAAO,mHAA0F,KAAK;QACrK,YAAY,mBAAmB;YAC7B,MAAM,KAAK,gBAAgB,eAAe;YAC1C,IAAI,IAAI;gBACN,MAAM,SAAS;oBACb;oBACA;gBACF;gBACA,GAAG;YACL;QACF;IACF;IACA,MAAM,cAAc,CAAC,UAAU;QAC7B,CAAC,CAAC,WAAW,uCAAwC,UAAU,OAAO,qHAA4F,KAAK;QACvK,YAAY,qBAAqB;YAC/B,MAAM,KAAK,gBAAgB,iBAAiB;YAC5C,IAAI,IAAI;gBACN,GAAG,aAAa,UAAU;YAC5B;QACF;IACF;IACA,MAAM,QAAQ,CAAC,UAAU;QACvB,CAAC,CAAC,WAAW,uCAAwC,UAAU,OAAO,qHAA4F,KAAK;QACvK,MAAM,OAAO,aAAa,UAAU;QACpC,WAAW;YACT;YACA,cAAc;YACd,cAAc,KAAK,MAAM;YACzB,aAAa;QACf;QACA,aAAa,GAAG,CAAC;YACf,YAAY,eAAe,IAAM,QAAQ,gBAAgB,WAAW,EAAE,MAAM,UAAU,OAAO,WAAW;QAC1G;IACF;IACA,MAAM,SAAS,CAAC,UAAU;QACxB,MAAM,WAAW,kBAAkB;QACnC,MAAM,UAAU,cAAc;QAC9B,CAAC,WAAW,uCAAwC,UAAU,OAAO,wGAA+E,KAAK;QACzJ,MAAM,qBAAqB,CAAC,gBAAgB,UAAU,SAAS,YAAY;QAC3E,IAAI,oBAAoB;YACtB,SAAS,YAAY,GAAG;QAC1B;QACA,MAAM,qBAAqB,CAAC,kBAAkB,SAAS,YAAY,EAAE;QACrE,IAAI,oBAAoB;YACtB,SAAS,YAAY,GAAG;QAC1B;QACA,MAAM,qBAAqB,CAAC,eAAe,SAAS,WAAW,EAAE;QACjE,IAAI,oBAAoB;YACtB,SAAS,WAAW,GAAG;QACzB;QACA,IAAI,CAAC,sBAAsB,CAAC,sBAAsB,CAAC,oBAAoB;YACrE;QACF;QACA,MAAM,OAAO;YACX,GAAG,aAAa,UAAU,SAAS,IAAI,CAAC;YACxC;YACA,aAAa;QACf;QACA,aAAa,GAAG,CAAC;YACf,YAAY,gBAAgB,IAAM,QAAQ,gBAAgB,YAAY,EAAE,MAAM,UAAU,OAAO,YAAY;QAC7G;IACF;IACA,MAAM,QAAQ;QACZ,CAAC,WAAW,uCAAwC,UAAU,OAAO,qFAA4D,KAAK;QACtI,aAAa,KAAK;IACpB;IACA,MAAM,OAAO,CAAA;QACX,CAAC,WAAW,uCAAwC,UAAU,OAAO,wGAA+E,KAAK;QACzJ,WAAW;QACX,YAAY,aAAa,IAAM,QAAQ,gBAAgB,SAAS,EAAE,QAAQ,UAAU,OAAO,SAAS;IACtG;IACA,MAAM,QAAQ;QACZ,IAAI,CAAC,UAAU;YACb;QACF;QACA,MAAM,SAAS;YACb,GAAG,aAAa,SAAS,YAAY,EAAE,SAAS,IAAI,CAAC;YACrD,SAAS;YACT,aAAa;YACb,QAAQ;QACV;QACA,KAAK;IACP;IACA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAEA,IAAI,aAAa,CAAC,eAAe;IAC/B,MAAM,YAAY,aAAa,eAAe;IAC9C,OAAO,CAAA,QAAS,CAAA,OAAQ,CAAA;gBACtB,IAAI,MAAM,QAAQ,2BAA2B;oBAC3C,UAAU,aAAa,CAAC,OAAO,OAAO,CAAC,WAAW,EAAE,OAAO,OAAO,CAAC,YAAY;oBAC/E;gBACF;gBACA,IAAI,MAAM,QAAQ,oBAAoB;oBACpC,MAAM,WAAW,OAAO,OAAO,CAAC,QAAQ;oBACxC,UAAU,WAAW,CAAC,UAAU,OAAO,OAAO,CAAC,YAAY;oBAC3D,KAAK;oBACL,UAAU,KAAK,CAAC,UAAU,OAAO,OAAO,CAAC,YAAY;oBACrD;gBACF;gBACA,IAAI,MAAM,QAAQ,kBAAkB;oBAClC,MAAM,SAAS,OAAO,OAAO,CAAC,SAAS,CAAC,MAAM;oBAC9C,UAAU,KAAK;oBACf,KAAK;oBACL,UAAU,IAAI,CAAC;oBACf;gBACF;gBACA,KAAK;gBACL,IAAI,MAAM,QAAQ,UAAU;oBAC1B,UAAU,KAAK;oBACf;gBACF;gBACA,MAAM,QAAQ,MAAM,QAAQ;gBAC5B,IAAI,MAAM,KAAK,KAAK,YAAY;oBAC9B,UAAU,MAAM,CAAC,MAAM,QAAQ,EAAE,MAAM,MAAM;gBAC/C;YACF;AACF;AAEA,MAAM,gCAAgC,CAAA,QAAS,CAAA,OAAQ,CAAA;YACrD,IAAI,CAAC,MAAM,QAAQ,4BAA4B;gBAC7C,KAAK;gBACL;YACF;YACA,MAAM,QAAQ,MAAM,QAAQ;YAC5B,CAAC,CAAC,MAAM,KAAK,KAAK,gBAAgB,IAAI,uCAAwC,UAAU,OAAO,qGAA4E,KAAK;YAChL,MAAM,QAAQ,CAAC,aAAa;gBAC1B,WAAW,MAAM,SAAS;YAC5B;QACF;AAEA,MAAM,uCAAuC,CAAA;IAC3C,IAAI,SAAS;IACb,IAAI,UAAU;IACd,SAAS;QACP,IAAI,SAAS;YACX,qBAAqB;YACrB,UAAU;QACZ;QACA,IAAI,QAAQ;YACV;YACA,SAAS;QACX;IACF;IACA,OAAO,CAAA,OAAQ,CAAA;YACb,IAAI,MAAM,QAAQ,YAAY,MAAM,QAAQ,oBAAoB,MAAM,QAAQ,4BAA4B;gBACxG;YACF;YACA,KAAK;YACL,IAAI,CAAC,MAAM,QAAQ,iBAAiB;gBAClC;YACF;YACA,MAAM,UAAU;gBACd,WAAW;gBACX,SAAS;oBACP,SAAS;oBACT,SAAS;oBACT,MAAM;gBACR;gBACA,IAAI,SAAS;oBACX,MAAM,QAAQ,MAAM,QAAQ;oBAC5B,IAAI,MAAM,KAAK,KAAK,kBAAkB;wBACpC,MAAM,QAAQ,CAAC;oBACjB;gBACF;YACF;YACA,UAAU,sBAAsB;gBAC9B,UAAU;gBACV,SAAS,WAAW,QAAQ;oBAAC;iBAAQ;YACvC;QACF;AACF;AAEA,IAAI,0BAA0B,CAAA,UAAW,IAAM,CAAA,OAAQ,CAAA;gBACrD,IAAI,MAAM,QAAQ,oBAAoB,MAAM,QAAQ,YAAY,MAAM,QAAQ,iBAAiB;oBAC7F,QAAQ,cAAc;gBACxB;gBACA,KAAK;YACP;AAEA,IAAI,QAAQ,CAAA;IACV,IAAI,aAAa;IACjB,OAAO,IAAM,CAAA,OAAQ,CAAA;gBACnB,IAAI,MAAM,QAAQ,oBAAoB;oBACpC,aAAa;oBACb,QAAQ,cAAc,CAAC,OAAO,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;oBAC3D,KAAK;oBACL,QAAQ,uBAAuB;oBAC/B;gBACF;gBACA,KAAK;gBACL,IAAI,CAAC,YAAY;oBACf;gBACF;gBACA,IAAI,MAAM,QAAQ,UAAU;oBAC1B,aAAa;oBACb,QAAQ,uBAAuB;oBAC/B;gBACF;gBACA,IAAI,MAAM,QAAQ,kBAAkB;oBAClC,aAAa;oBACb,MAAM,SAAS,OAAO,OAAO,CAAC,SAAS,CAAC,MAAM;oBAC9C,IAAI,OAAO,OAAO,EAAE;wBAClB,QAAQ,cAAc,CAAC,OAAO,WAAW,EAAE,OAAO,OAAO,CAAC,WAAW;oBACvE;oBACA,QAAQ,uBAAuB;gBACjC;YACF;AACF;AAEA,MAAM,aAAa,CAAA,SAAU,MAAM,QAAQ,oBAAoB,MAAM,QAAQ,mBAAmB,MAAM,QAAQ;AAC9G,IAAI,aAAa,CAAA,eAAgB,CAAA,QAAS,CAAA,OAAQ,CAAA;gBAChD,IAAI,WAAW,SAAS;oBACtB,aAAa,IAAI;oBACjB,KAAK;oBACL;gBACF;gBACA,IAAI,MAAM,QAAQ,oBAAoB;oBACpC,KAAK;oBACL,MAAM,QAAQ,MAAM,QAAQ;oBAC5B,CAAC,CAAC,MAAM,KAAK,KAAK,UAAU,IAAI,uCAAwC,UAAU,OAAO,gGAAuE,KAAK;oBACrK,aAAa,KAAK,CAAC;oBACnB;gBACF;gBACA,KAAK;gBACL,aAAa,MAAM,CAAC,MAAM,QAAQ;YACpC;AAEA,MAAM,cAAc,CAAA,QAAS,CAAA,OAAQ,CAAA;YACnC,KAAK;YACL,IAAI,CAAC,MAAM,QAAQ,2BAA2B;gBAC5C;YACF;YACA,MAAM,kBAAkB,MAAM,QAAQ;YACtC,IAAI,gBAAgB,KAAK,KAAK,gBAAgB;gBAC5C;YACF;YACA,IAAI,gBAAgB,SAAS,EAAE;gBAC7B;YACF;YACA,MAAM,QAAQ,CAAC,KAAK;gBAClB,QAAQ,gBAAgB,MAAM;YAChC;QACF;AAEA,MAAM,mBAAmB,oDAAyB,gBAAgB,OAAO,WAAW,eAAe,OAAO,oCAAoC,GAAG,OAAO,oCAAoC,CAAC;IAC3L,MAAM;AACR,KAAK,uIAAA,CAAA,UAAO;AACZ,IAAI,cAAc,CAAC,EACjB,gBAAgB,EAChB,YAAY,EACZ,YAAY,EACZ,aAAa,EACb,QAAQ,EACR,YAAY,EACb,GAAK,CAAA,GAAA,uIAAA,CAAA,cAAa,AAAD,EAAE,SAAS,iBAAiB,CAAA,GAAA,uIAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,eAAe,wBAAwB,mBAAmB,KAAK,mBAAmB,gBAAgB,+BAA+B,sCAAsC,aAAa,WAAW,eAAe,gBAAgB,MAAM,eAAe,WAAW,eAAe;AAEjV,MAAM,UAAU,IAAM,CAAC;QACrB,WAAW,CAAC;QACZ,UAAU,CAAC;QACX,UAAU,CAAC;IACb,CAAC;AACD,SAAS,gBAAgB,EACvB,QAAQ,EACR,SAAS,EACV;IACC,IAAI,UAAU;IACd,IAAI,UAAU;IACd,MAAM,UAAU;QACd,IAAI,SAAS;YACX;QACF;QACA,UAAU,kBAAkB;QAC5B,UAAU,sBAAsB;YAC9B,UAAU;YACV;YACA,MAAM,EACJ,SAAS,EACT,QAAQ,EACR,QAAQ,EACT,GAAG;YACJ,MAAM,QAAQ,OAAO,IAAI,CAAC,WAAW,GAAG,CAAC,CAAA,KAAM,SAAS,SAAS,CAAC,OAAO,CAAC,IAAI,YAAY,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,UAAU,CAAC,KAAK,GAAG,EAAE,UAAU,CAAC,KAAK;YAC1J,MAAM,UAAU,OAAO,IAAI,CAAC,UAAU,GAAG,CAAC,CAAA;gBACxC,MAAM,QAAQ,SAAS,SAAS,CAAC,OAAO,CAAC;gBACzC,MAAM,SAAS,MAAM,SAAS,CAAC,sBAAsB;gBACrD,OAAO;oBACL,aAAa;oBACb;gBACF;YACF;YACA,MAAM,SAAS;gBACb,WAAW;gBACX,UAAU,OAAO,IAAI,CAAC;gBACtB,UAAU;YACZ;YACA,UAAU;YACV;YACA,UAAU,OAAO,CAAC;QACpB;IACF;IACA,MAAM,MAAM,CAAA;QACV,MAAM,KAAK,MAAM,UAAU,CAAC,EAAE;QAC9B,QAAQ,SAAS,CAAC,GAAG,GAAG;QACxB,QAAQ,QAAQ,CAAC,MAAM,UAAU,CAAC,WAAW,CAAC,GAAG;QACjD,IAAI,QAAQ,QAAQ,CAAC,GAAG,EAAE;YACxB,OAAO,QAAQ,QAAQ,CAAC,GAAG;QAC7B;QACA;IACF;IACA,MAAM,SAAS,CAAA;QACb,MAAM,aAAa,MAAM,UAAU;QACnC,QAAQ,QAAQ,CAAC,WAAW,EAAE,CAAC,GAAG;QAClC,QAAQ,QAAQ,CAAC,WAAW,WAAW,CAAC,GAAG;QAC3C,IAAI,QAAQ,SAAS,CAAC,WAAW,EAAE,CAAC,EAAE;YACpC,OAAO,QAAQ,SAAS,CAAC,WAAW,EAAE,CAAC;QACzC;QACA;IACF;IACA,MAAM,OAAO;QACX,IAAI,CAAC,SAAS;YACZ;QACF;QACA,qBAAqB;QACrB,UAAU;QACV,UAAU;IACZ;IACA,OAAO;QACL;QACA;QACA;IACF;AACF;AAEA,IAAI,eAAe,CAAC,EAClB,YAAY,EACZ,WAAW,EACX,MAAM,EACN,KAAK,EACN;IACC,MAAM,YAAY,SAAS;QACzB,GAAG;QACH,GAAG;IACL,GAAG;QACD,GAAG;QACH,GAAG;IACL;IACA,MAAM,oBAAoB;QACxB,GAAG,KAAK,GAAG,CAAC,GAAG,UAAU,CAAC;QAC1B,GAAG,KAAK,GAAG,CAAC,GAAG,UAAU,CAAC;IAC5B;IACA,OAAO;AACT;AAEA,IAAI,qBAAqB;IACvB,MAAM,MAAM,SAAS,eAAe;IACpC,CAAC,MAAM,uCAAwC,UAAU,OAAO,iFAAwD,KAAK;IAC7H,OAAO;AACT;AAEA,IAAI,qBAAqB;IACvB,MAAM,MAAM;IACZ,MAAM,YAAY,aAAa;QAC7B,cAAc,IAAI,YAAY;QAC9B,aAAa,IAAI,WAAW;QAC5B,OAAO,IAAI,WAAW;QACtB,QAAQ,IAAI,YAAY;IAC1B;IACA,OAAO;AACT;AAEA,IAAI,cAAc;IAChB,MAAM,SAAS;IACf,MAAM,YAAY;IAClB,MAAM,MAAM,OAAO,CAAC;IACpB,MAAM,OAAO,OAAO,CAAC;IACrB,MAAM,MAAM;IACZ,MAAM,QAAQ,IAAI,WAAW;IAC7B,MAAM,SAAS,IAAI,YAAY;IAC/B,MAAM,QAAQ,OAAO;IACrB,MAAM,SAAS,MAAM;IACrB,MAAM,QAAQ,CAAA,GAAA,yKAAA,CAAA,UAAO,AAAD,EAAE;QACpB;QACA;QACA;QACA;IACF;IACA,MAAM,WAAW;QACf;QACA,QAAQ;YACN,SAAS;YACT,SAAS;YACT,KAAK;YACL,MAAM;gBACJ,OAAO;gBACP,cAAc;YAChB;QACF;IACF;IACA,OAAO;AACT;AAEA,IAAI,oBAAoB,CAAC,EACvB,QAAQ,EACR,aAAa,EACb,QAAQ,EACT;IACC;IACA,MAAM,WAAW;IACjB,MAAM,eAAe,SAAS,MAAM,CAAC,OAAO;IAC5C,MAAM,OAAO,SAAS,SAAS;IAC/B,MAAM,aAAa,SAAS,SAAS,CAAC,YAAY,CAAC,KAAK,IAAI,EAAE,GAAG,CAAC,CAAA,QAAS,MAAM,SAAS,CAAC,0BAA0B,CAAC,cAAc;IACpI,MAAM,aAAa,SAAS,SAAS,CAAC,YAAY,CAAC,SAAS,SAAS,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA,QAAS,MAAM,YAAY,CAAC;IAC5G,MAAM,aAAa;QACjB,YAAY,eAAe;QAC3B,YAAY,eAAe;IAC7B;IACA;IACA,MAAM,SAAS;QACb;QACA;QACA;IACF;IACA,OAAO;AACT;AAEA,SAAS,oBAAoB,QAAQ,EAAE,QAAQ,EAAE,KAAK;IACpD,IAAI,MAAM,UAAU,CAAC,EAAE,KAAK,SAAS,EAAE,EAAE;QACvC,OAAO;IACT;IACA,IAAI,MAAM,UAAU,CAAC,IAAI,KAAK,SAAS,IAAI,EAAE;QAC3C,OAAO;IACT;IACA,MAAM,OAAO,SAAS,SAAS,CAAC,OAAO,CAAC,MAAM,UAAU,CAAC,WAAW;IACpE,IAAI,KAAK,UAAU,CAAC,IAAI,KAAK,WAAW;QACtC,uCAAwC,QAAQ,CAAC;2DACM,EAAE,MAAM,UAAU,CAAC,EAAE,CAAC;;;;IAI7E,CAAC;QACD,OAAO;IACT;IACA,OAAO;AACT;AACA,IAAI,yBAAyB,CAAC,UAAU;IACtC,IAAI,aAAa;IACjB,MAAM,YAAY,gBAAgB;QAChC,WAAW;YACT,SAAS,UAAU,oBAAoB;YACvC,oBAAoB,UAAU,kBAAkB;QAClD;QACA;IACF;IACA,MAAM,2BAA2B,CAAC,IAAI;QACpC,CAAC,SAAS,SAAS,CAAC,MAAM,CAAC,MAAM,uCAAwC,UAAU,OAAO,CAAC,2CAA2C,EAAE,GAAG,wBAAwB,CAAC,2CAAkB,KAAK;QAC3L,IAAI,CAAC,YAAY;YACf;QACF;QACA,UAAU,wBAAwB,CAAC;YACjC;YACA;QACF;IACF;IACA,MAAM,kCAAkC,CAAC,IAAI;QAC3C,IAAI,CAAC,YAAY;YACf;QACF;QACA,CAAC,SAAS,SAAS,CAAC,MAAM,CAAC,MAAM,uCAAwC,UAAU,OAAO,CAAC,iDAAiD,EAAE,GAAG,wBAAwB,CAAC,2CAAkB,KAAK;QACjM,UAAU,+BAA+B,CAAC;YACxC;YACA;QACF;IACF;IACA,MAAM,wBAAwB,CAAC,IAAI;QACjC,IAAI,CAAC,YAAY;YACf;QACF;QACA,CAAC,SAAS,SAAS,CAAC,MAAM,CAAC,MAAM,uCAAwC,UAAU,OAAO,CAAC,sCAAsC,EAAE,GAAG,wBAAwB,CAAC,2CAAkB,KAAK;QACtL,UAAU,qBAAqB,CAAC;YAC9B;YACA;QACF;IACF;IACA,MAAM,kBAAkB,CAAC,IAAI;QAC3B,IAAI,CAAC,YAAY;YACf;QACF;QACA,SAAS,SAAS,CAAC,OAAO,CAAC,IAAI,SAAS,CAAC,MAAM,CAAC;IAClD;IACA,MAAM,iBAAiB;QACrB,IAAI,CAAC,YAAY;YACf;QACF;QACA,UAAU,IAAI;QACd,MAAM,OAAO,WAAW,QAAQ,CAAC,SAAS;QAC1C,SAAS,SAAS,CAAC,YAAY,CAAC,KAAK,IAAI,EAAE,OAAO,CAAC,CAAA,QAAS,MAAM,SAAS,CAAC,WAAW;QACvF,WAAW,WAAW;QACtB,aAAa;IACf;IACA,MAAM,aAAa,CAAA;QACjB,CAAC,aAAa,uCAAwC,UAAU,OAAO,qGAA4E,KAAK;QACxJ,MAAM,WAAW,WAAW,QAAQ,CAAC,SAAS;QAC9C,IAAI,MAAM,IAAI,KAAK,YAAY;YAC7B,IAAI,oBAAoB,UAAU,UAAU,MAAM,KAAK,GAAG;gBACxD,UAAU,GAAG,CAAC,MAAM,KAAK;YAC3B;QACF;QACA,IAAI,MAAM,IAAI,KAAK,WAAW;YAC5B,IAAI,oBAAoB,UAAU,UAAU,MAAM,KAAK,GAAG;gBACxD,UAAU,MAAM,CAAC,MAAM,KAAK;YAC9B;QACF;IACF;IACA,MAAM,kBAAkB,CAAA;QACtB,CAAC,CAAC,aAAa,uCAAwC,UAAU,OAAO,wHAA+F,KAAK;QAC5K,MAAM,QAAQ,SAAS,SAAS,CAAC,OAAO,CAAC,QAAQ,WAAW;QAC5D,MAAM,OAAO,SAAS,SAAS,CAAC,OAAO,CAAC,MAAM,UAAU,CAAC,WAAW;QACpE,MAAM,WAAW;YACf,WAAW,MAAM,UAAU;YAC3B,WAAW,KAAK,UAAU;QAC5B;QACA,MAAM,cAAc,SAAS,SAAS,CAAC;QACvC,aAAa;YACX;YACA;QACF;QACA,OAAO,kBAAkB;YACvB;YACA;YACA,eAAe,QAAQ,aAAa;QACtC;IACF;IACA,MAAM,UAAU;QACd;QACA;QACA;QACA;QACA;QACA;IACF;IACA,OAAO;AACT;AAEA,IAAI,eAAe,CAAC,OAAO;IACzB,IAAI,MAAM,KAAK,KAAK,QAAQ;QAC1B,OAAO;IACT;IACA,IAAI,MAAM,KAAK,KAAK,kBAAkB;QACpC,OAAO;IACT;IACA,IAAI,MAAM,SAAS,CAAC,MAAM,CAAC,WAAW,KAAK,IAAI;QAC7C,OAAO;IACT;IACA,OAAO,MAAM,SAAS,CAAC,MAAM,CAAC,MAAM,KAAK;AAC3C;AAEA,IAAI,eAAe,CAAA;IACjB,OAAO,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC;AACpC;AAEA,MAAM,0BAA0B,WAAW,CAAA,aAAc,gBAAgB,YAAY,MAAM,CAAC,CAAA;QAC1F,IAAI,CAAC,UAAU,SAAS,EAAE;YACxB,OAAO;QACT;QACA,IAAI,CAAC,UAAU,KAAK,EAAE;YACpB,OAAO;QACT;QACA,OAAO;IACT;AACA,MAAM,6BAA6B,CAAC,QAAQ;IAC1C,MAAM,QAAQ,wBAAwB,YAAY,IAAI,CAAC,CAAA;QACrD,CAAC,UAAU,KAAK,GAAG,uCAAwC,UAAU,OAAO,2DAAkC,KAAK;QACnH,OAAO,kBAAkB,UAAU,KAAK,CAAC,aAAa,EAAE;IAC1D,MAAM;IACN,OAAO;AACT;AACA,IAAI,6BAA6B,CAAC,EAChC,MAAM,EACN,WAAW,EACX,UAAU,EACX;IACC,IAAI,aAAa;QACf,MAAM,YAAY,UAAU,CAAC,YAAY;QACzC,IAAI,CAAC,UAAU,KAAK,EAAE;YACpB,OAAO;QACT;QACA,OAAO;IACT;IACA,MAAM,YAAY,2BAA2B,QAAQ;IACrD,OAAO;AACT;AAEA,MAAM,6BAA6B;IACjC,qBAAqB;IACrB,uBAAuB;IACvB,gBAAgB;IAChB,MAAM,CAAA,aAAc,cAAc;IAClC,mBAAmB;QACjB,iBAAiB;QACjB,cAAc;IAChB;IACA,UAAU;AACZ;AAEA,IAAI,wBAAwB,CAAC,WAAW,MAAM,yBAAyB,IAAM,0BAA0B;IACrG,MAAM,sBAAsB;IAC5B,MAAM,qBAAqB,SAAS,CAAC,KAAK,IAAI,CAAC,GAAG,oBAAoB,mBAAmB;IACzF,MAAM,mBAAmB,SAAS,CAAC,KAAK,IAAI,CAAC,GAAG,oBAAoB,qBAAqB;IACzF,MAAM,aAAa;QACjB;QACA;IACF;IACA,OAAO;AACT;AAEA,IAAI,gBAAgB,CAAC,EACnB,YAAY,EACZ,UAAU,EACV,OAAO,EACR;IACC,MAAM,QAAQ,aAAa;IAC3B,IAAI,UAAU,GAAG;QACf,uCAAwC,QAAQ,CAAC;;;;IAIjD,CAAC;QACD,OAAO;IACT;IACA,MAAM,iBAAiB,UAAU;IACjC,MAAM,aAAa,iBAAiB;IACpC,OAAO;AACT;AAEA,IAAI,YAAY;AAEhB,IAAI,uBAAuB,CAAC,gBAAgB,YAAY,yBAAyB,IAAM,0BAA0B;IAC/G,MAAM,sBAAsB;IAC5B,IAAI,iBAAiB,WAAW,kBAAkB,EAAE;QAClD,OAAO;IACT;IACA,IAAI,kBAAkB,WAAW,gBAAgB,EAAE;QACjD,OAAO,oBAAoB,cAAc;IAC3C;IACA,IAAI,mBAAmB,WAAW,kBAAkB,EAAE;QACpD,OAAO;IACT;IACA,MAAM,iCAAiC,cAAc;QACnD,cAAc,WAAW,gBAAgB;QACzC,YAAY,WAAW,kBAAkB;QACzC,SAAS;IACX;IACA,MAAM,mCAAmC,IAAI;IAC7C,MAAM,SAAS,oBAAoB,cAAc,GAAG,oBAAoB,IAAI,CAAC;IAC7E,OAAO,KAAK,IAAI,CAAC;AACnB;AAEA,IAAI,oBAAoB,CAAC,gBAAgB,eAAe;IACtD,MAAM,sBAAsB;IAC5B,MAAM,eAAe,oBAAoB,iBAAiB,CAAC,YAAY;IACvE,MAAM,SAAS,oBAAoB,iBAAiB,CAAC,eAAe;IACpE,MAAM,eAAe;IACrB,MAAM,aAAa;IACnB,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,UAAU,MAAM;IACtB,IAAI,WAAW,QAAQ;QACrB,OAAO;IACT;IACA,IAAI,UAAU,cAAc;QAC1B,OAAO;IACT;IACA,MAAM,yCAAyC,cAAc;QAC3D,cAAc;QACd;QACA,SAAS;IACX;IACA,MAAM,SAAS,iBAAiB,oBAAoB,IAAI,CAAC;IACzD,OAAO,KAAK,IAAI,CAAC;AACnB;AAEA,IAAI,WAAW,CAAC,EACd,cAAc,EACd,UAAU,EACV,aAAa,EACb,sBAAsB,EACtB,sBAAsB,EACvB;IACC,MAAM,SAAS,qBAAqB,gBAAgB,YAAY;IAChE,IAAI,WAAW,GAAG;QAChB,OAAO;IACT;IACA,IAAI,CAAC,wBAAwB;QAC3B,OAAO;IACT;IACA,OAAO,KAAK,GAAG,CAAC,kBAAkB,QAAQ,eAAe,yBAAyB;AACpF;AAEA,IAAI,kBAAkB,CAAC,EACrB,SAAS,EACT,eAAe,EACf,aAAa,EACb,IAAI,EACJ,sBAAsB,EACtB,sBAAsB,EACvB;IACC,MAAM,aAAa,sBAAsB,WAAW,MAAM;IAC1D,MAAM,gBAAgB,eAAe,CAAC,KAAK,GAAG,CAAC,GAAG,eAAe,CAAC,KAAK,KAAK,CAAC;IAC7E,IAAI,eAAe;QACjB,OAAO,SAAS;YACd,gBAAgB,eAAe,CAAC,KAAK,GAAG,CAAC;YACzC;YACA;YACA;YACA;QACF;IACF;IACA,OAAO,CAAC,IAAI,SAAS;QACnB,gBAAgB,eAAe,CAAC,KAAK,KAAK,CAAC;QAC3C;QACA;QACA;QACA;IACF;AACF;AAEA,IAAI,sBAAsB,CAAC,EACzB,SAAS,EACT,OAAO,EACP,cAAc,EACf;IACC,MAAM,qBAAqB,QAAQ,MAAM,GAAG,UAAU,MAAM;IAC5D,MAAM,uBAAuB,QAAQ,KAAK,GAAG,UAAU,KAAK;IAC5D,IAAI,CAAC,wBAAwB,CAAC,oBAAoB;QAChD,OAAO;IACT;IACA,IAAI,wBAAwB,oBAAoB;QAC9C,OAAO;IACT;IACA,OAAO;QACL,GAAG,uBAAuB,IAAI,eAAe,CAAC;QAC9C,GAAG,qBAAqB,IAAI,eAAe,CAAC;IAC9C;AACF;AAEA,MAAM,QAAQ,MAAM,CAAA,QAAS,UAAU,IAAI,IAAI;AAC/C,IAAI,cAAc,CAAC,EACjB,aAAa,EACb,SAAS,EACT,OAAO,EACP,MAAM,EACN,sBAAsB,EACtB,sBAAsB,EACvB;IACC,MAAM,kBAAkB;QACtB,KAAK,OAAO,CAAC,GAAG,UAAU,GAAG;QAC7B,OAAO,UAAU,KAAK,GAAG,OAAO,CAAC;QACjC,QAAQ,UAAU,MAAM,GAAG,OAAO,CAAC;QACnC,MAAM,OAAO,CAAC,GAAG,UAAU,IAAI;IACjC;IACA,MAAM,IAAI,gBAAgB;QACxB;QACA;QACA;QACA,MAAM;QACN;QACA;IACF;IACA,MAAM,IAAI,gBAAgB;QACxB;QACA;QACA;QACA,MAAM;QACN;QACA;IACF;IACA,MAAM,WAAW,MAAM;QACrB;QACA;IACF;IACA,IAAI,UAAU,UAAU,SAAS;QAC/B,OAAO;IACT;IACA,MAAM,UAAU,oBAAoB;QAClC;QACA;QACA,gBAAgB;IAClB;IACA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IACA,OAAO,UAAU,SAAS,UAAU,OAAO;AAC7C;AAEA,MAAM,iBAAiB,MAAM,CAAA;IAC3B,IAAI,UAAU,GAAG;QACf,OAAO;IACT;IACA,OAAO,QAAQ,IAAI,IAAI,CAAC;AAC1B;AACA,MAAM,aAAa,CAAC;IAClB,MAAM,eAAe,CAAC,QAAQ;QAC5B,IAAI,SAAS,GAAG;YACd,OAAO;QACT;QACA,IAAI,SAAS,KAAK;YAChB,OAAO,SAAS;QAClB;QACA,OAAO;IACT;IACA,OAAO,CAAC,EACN,OAAO,EACP,GAAG,EACH,MAAM,EACP;QACC,MAAM,eAAe,IAAI,SAAS;QAClC,MAAM,UAAU;YACd,GAAG,aAAa,aAAa,CAAC,EAAE,IAAI,CAAC;YACrC,GAAG,aAAa,aAAa,CAAC,EAAE,IAAI,CAAC;QACvC;QACA,IAAI,UAAU,SAAS,SAAS;YAC9B,OAAO;QACT;QACA,OAAO;IACT;AACF,CAAC;AACD,MAAM,qBAAqB,CAAC,EAC1B,KAAK,MAAM,EACX,OAAO,EACP,MAAM,EACP;IACC,MAAM,MAAM;QACV,GAAG,KAAK,GAAG,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC;QAC/B,GAAG,KAAK,GAAG,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC;IACjC;IACA,MAAM,iBAAiB,eAAe;IACtC,MAAM,UAAU,WAAW;QACzB;QACA;QACA,QAAQ;IACV;IACA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IACA,IAAI,eAAe,CAAC,KAAK,KAAK,QAAQ,CAAC,KAAK,GAAG;QAC7C,OAAO;IACT;IACA,IAAI,eAAe,CAAC,KAAK,KAAK,QAAQ,CAAC,KAAK,GAAG;QAC7C,OAAO;IACT;IACA,OAAO;AACT;AACA,MAAM,kBAAkB,CAAC,UAAU,SAAW,mBAAmB;QAC/D,SAAS,SAAS,MAAM,CAAC,OAAO;QAChC,KAAK,SAAS,MAAM,CAAC,GAAG;QACxB;IACF;AACA,MAAM,mBAAmB,CAAC,UAAU;IAClC,IAAI,CAAC,gBAAgB,UAAU,SAAS;QACtC,OAAO;IACT;IACA,MAAM,MAAM,SAAS,MAAM,CAAC,GAAG;IAC/B,MAAM,UAAU,SAAS,MAAM,CAAC,OAAO;IACvC,OAAO,WAAW;QAChB;QACA;QACA;IACF;AACF;AACA,MAAM,qBAAqB,CAAC,WAAW;IACrC,MAAM,QAAQ,UAAU,KAAK;IAC7B,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IACA,OAAO,mBAAmB;QACxB,SAAS,MAAM,MAAM,CAAC,OAAO;QAC7B,KAAK,MAAM,MAAM,CAAC,GAAG;QACrB;IACF;AACF;AACA,MAAM,sBAAsB,CAAC,WAAW;IACtC,MAAM,QAAQ,UAAU,KAAK;IAC7B,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IACA,IAAI,CAAC,mBAAmB,WAAW,SAAS;QAC1C,OAAO;IACT;IACA,OAAO,WAAW;QAChB,SAAS,MAAM,MAAM,CAAC,OAAO;QAC7B,KAAK,MAAM,MAAM,CAAC,GAAG;QACrB;IACF;AACF;AAEA,IAAI,wBAAwB,CAAC,EAC3B,QAAQ,EACR,OAAO,EACP,MAAM,EACN,aAAa,EACb,sBAAsB,EACtB,sBAAsB,EACvB;IACC,MAAM,SAAS,YAAY;QACzB;QACA,WAAW,SAAS,KAAK;QACzB;QACA;QACA;QACA;IACF;IACA,OAAO,UAAU,gBAAgB,UAAU,UAAU,SAAS;AAChE;AAEA,IAAI,2BAA2B,CAAC,EAC9B,SAAS,EACT,OAAO,EACP,MAAM,EACN,aAAa,EACb,sBAAsB,EACtB,sBAAsB,EACvB;IACC,MAAM,QAAQ,UAAU,KAAK;IAC7B,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IACA,MAAM,SAAS,YAAY;QACzB;QACA,WAAW,MAAM,aAAa;QAC9B;QACA;QACA;QACA;IACF;IACA,OAAO,UAAU,mBAAmB,WAAW,UAAU,SAAS;AACpE;AAEA,IAAI,SAAS,CAAC,EACZ,KAAK,EACL,aAAa,EACb,sBAAsB,EACtB,YAAY,EACZ,eAAe,EACf,sBAAsB,EACvB;IACC,MAAM,SAAS,MAAM,OAAO,CAAC,IAAI,CAAC,eAAe;IACjD,MAAM,YAAY,MAAM,UAAU,CAAC,UAAU,CAAC,MAAM,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;IAC1E,MAAM,UAAU,UAAU,IAAI,CAAC,SAAS;IACxC,IAAI,MAAM,qBAAqB,EAAE;QAC/B,MAAM,WAAW,MAAM,QAAQ;QAC/B,MAAM,SAAS,sBAAsB;YACnC;YACA;YACA;YACA;YACA;YACA;QACF;QACA,IAAI,QAAQ;YACV,aAAa;YACb;QACF;IACF;IACA,MAAM,YAAY,2BAA2B;QAC3C;QACA,aAAa,kBAAkB,MAAM,MAAM;QAC3C,YAAY,MAAM,UAAU,CAAC,UAAU;IACzC;IACA,IAAI,CAAC,WAAW;QACd;IACF;IACA,MAAM,SAAS,yBAAyB;QACtC;QACA;QACA;QACA;QACA;QACA;IACF;IACA,IAAI,QAAQ;QACV,gBAAgB,UAAU,UAAU,CAAC,EAAE,EAAE;IAC3C;AACF;AAEA,IAAI,sBAAsB,CAAC,EACzB,YAAY,EACZ,eAAe,EACf,yBAAyB,IAAM,0BAA0B,EAC1D;IACC,MAAM,uBAAuB,CAAA,GAAA,yJAAA,CAAA,UAAO,AAAD,EAAE;IACrC,MAAM,0BAA0B,CAAA,GAAA,yJAAA,CAAA,UAAO,AAAD,EAAE;IACxC,IAAI,WAAW;IACf,MAAM,YAAY,CAAA;QAChB,CAAC,WAAW,uCAAwC,UAAU,OAAO,gFAAuD,KAAK;QACjI,MAAM,EACJ,sBAAsB,EACtB,aAAa,EACd,GAAG;QACJ,OAAO;YACL;YACA,cAAc;YACd,iBAAiB;YACjB;YACA;YACA;QACF;IACF;IACA,MAAM,UAAU,CAAA;QACd;QACA,CAAC,CAAC,WAAW,uCAAwC,UAAU,OAAO,6FAAoE,KAAK;QAC/I,MAAM,gBAAgB,KAAK,GAAG;QAC9B,IAAI,kBAAkB;QACtB,MAAM,qBAAqB;YACzB,kBAAkB;QACpB;QACA,OAAO;YACL;YACA,eAAe;YACf,wBAAwB;YACxB,cAAc;YACd,iBAAiB;YACjB;QACF;QACA,WAAW;YACT;YACA,wBAAwB;QAC1B;QACA;QACA,IAAI,iBAAiB;YACnB,UAAU;QACZ;IACF;IACA,MAAM,OAAO;QACX,IAAI,CAAC,UAAU;YACb;QACF;QACA,qBAAqB,MAAM;QAC3B,wBAAwB,MAAM;QAC9B,WAAW;IACb;IACA,OAAO;QACL,OAAO;QACP;QACA,QAAQ;IACV;AACF;AAEA,IAAI,qBAAqB,CAAC,EACxB,IAAI,EACJ,eAAe,EACf,YAAY,EACb;IACC,MAAM,eAAe,CAAC,OAAO;QAC3B,MAAM,SAAS,IAAI,MAAM,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE;QACnD,KAAK;YACH;QACF;IACF;IACA,MAAM,+BAA+B,CAAC,WAAW;QAC/C,IAAI,CAAC,mBAAmB,WAAW,SAAS;YAC1C,OAAO;QACT;QACA,MAAM,UAAU,oBAAoB,WAAW;QAC/C,IAAI,CAAC,SAAS;YACZ,gBAAgB,UAAU,UAAU,CAAC,EAAE,EAAE;YACzC,OAAO;QACT;QACA,MAAM,4BAA4B,SAAS,QAAQ;QACnD,gBAAgB,UAAU,UAAU,CAAC,EAAE,EAAE;QACzC,MAAM,YAAY,SAAS,QAAQ;QACnC,OAAO;IACT;IACA,MAAM,4BAA4B,CAAC,uBAAuB,UAAU;QAClE,IAAI,CAAC,uBAAuB;YAC1B,OAAO;QACT;QACA,IAAI,CAAC,gBAAgB,UAAU,SAAS;YACtC,OAAO;QACT;QACA,MAAM,UAAU,iBAAiB,UAAU;QAC3C,IAAI,CAAC,SAAS;YACZ,aAAa;YACb,OAAO;QACT;QACA,MAAM,yBAAyB,SAAS,QAAQ;QAChD,aAAa;QACb,MAAM,YAAY,SAAS,QAAQ;QACnC,OAAO;IACT;IACA,MAAM,eAAe,CAAA;QACnB,MAAM,UAAU,MAAM,iBAAiB;QACvC,IAAI,CAAC,SAAS;YACZ;QACF;QACA,MAAM,cAAc,kBAAkB,MAAM,MAAM;QAClD,CAAC,cAAc,uCAAwC,UAAU,OAAO,sGAA6E,KAAK;QAC1J,MAAM,qBAAqB,6BAA6B,MAAM,UAAU,CAAC,UAAU,CAAC,YAAY,EAAE;QAClG,IAAI,CAAC,oBAAoB;YACvB;QACF;QACA,MAAM,WAAW,MAAM,QAAQ;QAC/B,MAAM,kBAAkB,0BAA0B,MAAM,qBAAqB,EAAE,UAAU;QACzF,IAAI,CAAC,iBAAiB;YACpB;QACF;QACA,aAAa,OAAO;IACtB;IACA,OAAO;AACT;AAEA,IAAI,qBAAqB,CAAC,EACxB,eAAe,EACf,YAAY,EACZ,IAAI,EACJ,sBAAsB,EACvB;IACC,MAAM,gBAAgB,oBAAoB;QACxC;QACA;QACA;IACF;IACA,MAAM,aAAa,mBAAmB;QACpC;QACA;QACA;IACF;IACA,MAAM,SAAS,CAAA;QACb,MAAM,sBAAsB;QAC5B,IAAI,oBAAoB,QAAQ,IAAI,MAAM,KAAK,KAAK,YAAY;YAC9D;QACF;QACA,IAAI,MAAM,YAAY,KAAK,SAAS;YAClC,cAAc,MAAM,CAAC;YACrB;QACF;QACA,IAAI,CAAC,MAAM,iBAAiB,EAAE;YAC5B;QACF;QACA,WAAW;IACb;IACA,MAAM,WAAW;QACf;QACA,OAAO,cAAc,KAAK;QAC1B,MAAM,cAAc,IAAI;IAC1B;IACA,OAAO;AACT;AAEA,MAAM,SAAS;AACf,MAAM,aAAa,CAAC;IAClB,MAAM,OAAO,GAAG,OAAO,YAAY,CAAC;IACpC,OAAO;QACL;QACA,aAAa,GAAG,KAAK,aAAa,CAAC;QACnC,WAAW,GAAG,KAAK,WAAW,CAAC;IACjC;AACF,CAAC;AACD,MAAM,YAAY,CAAC;IACjB,MAAM,OAAO,GAAG,OAAO,UAAU,CAAC;IAClC,OAAO;QACL;QACA,WAAW,GAAG,KAAK,WAAW,CAAC;QAC/B,IAAI,GAAG,KAAK,GAAG,CAAC;IAClB;AACF,CAAC;AACD,MAAM,YAAY,CAAC;IACjB,MAAM,OAAO,GAAG,OAAO,UAAU,CAAC;IAClC,OAAO;QACL;QACA,WAAW,GAAG,KAAK,WAAW,CAAC;QAC/B,IAAI,GAAG,KAAK,GAAG,CAAC;IAClB;AACF,CAAC;AACD,MAAM,kBAAkB;IACtB,WAAW,GAAG,OAAO,4BAA4B,CAAC;AACpD;AAEA,MAAM,kBAAkB,CAAA,UAAW,CAAA,YAAa,CAAC,CAAC,EAAE,UAAU,EAAE,EAAE,QAAQ,EAAE,CAAC;AAC7E,MAAM,YAAY,CAAC,OAAO,WAAa,MAAM,GAAG,CAAC,CAAA;QAC/C,MAAM,QAAQ,KAAK,MAAM,CAAC,SAAS;QACnC,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QACA,OAAO,GAAG,KAAK,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,CAAC;IACxC,GAAG,IAAI,CAAC;AACR,MAAM,kBAAkB;AACxB,IAAI,cAAc,CAAA;IAChB,MAAM,cAAc,gBAAgB;IACpC,MAAM,eAAe,CAAC;QACpB,MAAM,aAAa,CAAC;;;IAGpB,CAAC;QACD,OAAO;YACL,UAAU,YAAY,WAAW,SAAS;YAC1C,QAAQ;gBACN,QAAQ,CAAC;;;;QAIT,CAAC;gBACD,SAAS;gBACT,UAAU;gBACV,eAAe;YACjB;QACF;IACF,CAAC;IACD,MAAM,cAAc,CAAC;QACnB,MAAM,aAAa,CAAC;kBACN,EAAE,YAAY,WAAW,CAAC;IACxC,CAAC;QACD,OAAO;YACL,UAAU,YAAY,UAAU,SAAS;YACzC,QAAQ;gBACN,UAAU;gBACV,eAAe;gBACf,YAAY;YACd;QACF;IACF,CAAC;IACD,MAAM,cAAc;QAClB,UAAU,YAAY,UAAU,SAAS;QACzC,QAAQ;YACN,QAAQ,CAAC,sBAAsB,CAAC;QAClC;IACF;IACA,MAAM,OAAO;QACX,UAAU;QACV,QAAQ;YACN,UAAU,CAAC;;;;;;;;MAQX,CAAC;QACH;IACF;IACA,MAAM,QAAQ;QAAC;QAAa;QAAc;QAAa;KAAK;IAC5D,OAAO;QACL,QAAQ,UAAU,OAAO;QACzB,SAAS,UAAU,OAAO;QAC1B,UAAU,UAAU,OAAO;QAC3B,eAAe,UAAU,OAAO;QAChC,YAAY,UAAU,OAAO;IAC/B;AACF;AAEA,MAAM,4BAA4B,OAAO,WAAW,eAAe,OAAO,OAAO,QAAQ,KAAK,eAAe,OAAO,OAAO,QAAQ,CAAC,aAAa,KAAK,cAAc,qMAAA,CAAA,kBAAe,GAAG,qMAAA,CAAA,YAAS;AAE/L,MAAM,UAAU;IACd,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,CAAC,OAAO,uCAAwC,UAAU,OAAO,sFAA6D,KAAK;IACnI,OAAO;AACT;AACA,MAAM,gBAAgB,CAAA;IACpB,MAAM,KAAK,SAAS,aAAa,CAAC;IAClC,IAAI,OAAO;QACT,GAAG,YAAY,CAAC,SAAS;IAC3B;IACA,GAAG,IAAI,GAAG;IACV,OAAO;AACT;AACA,SAAS,gBAAgB,SAAS,EAAE,KAAK;IACvC,MAAM,SAAS,QAAQ,IAAM,YAAY,YAAY;QAAC;KAAU;IAChE,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,kBAAkB,YAAY,WAAW,CAAA;QAC7C,MAAM,KAAK,WAAW,OAAO;QAC7B,CAAC,KAAK,uCAAwC,UAAU,OAAO,8FAAqE,KAAK;QACzI,GAAG,WAAW,GAAG;IACnB,IAAI,EAAE;IACN,MAAM,iBAAiB,YAAY,CAAA;QACjC,MAAM,KAAK,UAAU,OAAO;QAC5B,CAAC,KAAK,uCAAwC,UAAU,OAAO,8FAAqE,KAAK;QACzI,GAAG,WAAW,GAAG;IACnB,GAAG,EAAE;IACL,0BAA0B;QACxB,CAAC,CAAC,CAAC,UAAU,OAAO,IAAI,CAAC,WAAW,OAAO,IAAI,uCAAwC,UAAU,OAAO,2EAAkD,KAAK;QAC/J,MAAM,SAAS,cAAc;QAC7B,MAAM,UAAU,cAAc;QAC9B,UAAU,OAAO,GAAG;QACpB,WAAW,OAAO,GAAG;QACrB,OAAO,YAAY,CAAC,GAAG,OAAO,OAAO,CAAC,EAAE;QACxC,QAAQ,YAAY,CAAC,GAAG,OAAO,QAAQ,CAAC,EAAE;QAC1C,UAAU,WAAW,CAAC;QACtB,UAAU,WAAW,CAAC;QACtB,eAAe,OAAO,MAAM;QAC5B,gBAAgB,OAAO,OAAO;QAC9B,OAAO;YACL,MAAM,SAAS,CAAA;gBACb,MAAM,UAAU,IAAI,OAAO;gBAC3B,CAAC,UAAU,uCAAwC,UAAU,OAAO,gFAAuD,KAAK;gBAChI,UAAU,WAAW,CAAC;gBACtB,IAAI,OAAO,GAAG;YAChB;YACA,OAAO;YACP,OAAO;QACT;IACF,GAAG;QAAC;QAAO;QAAgB;QAAiB,OAAO,MAAM;QAAE,OAAO,OAAO;QAAE;KAAU;IACrF,MAAM,WAAW,YAAY,IAAM,gBAAgB,OAAO,QAAQ,GAAG;QAAC;QAAiB,OAAO,QAAQ;KAAC;IACvG,MAAM,WAAW,YAAY,CAAA;QAC3B,IAAI,WAAW,QAAQ;YACrB,gBAAgB,OAAO,aAAa;YACpC;QACF;QACA,gBAAgB,OAAO,UAAU;IACnC,GAAG;QAAC;QAAiB,OAAO,aAAa;QAAE,OAAO,UAAU;KAAC;IAC7D,MAAM,UAAU,YAAY;QAC1B,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB;QACF;QACA,gBAAgB,OAAO,OAAO;IAChC,GAAG;QAAC;QAAiB,OAAO,OAAO;KAAC;IACpC,MAAM,UAAU,QAAQ,IAAM,CAAC;YAC7B;YACA;YACA;QACF,CAAC,GAAG;QAAC;QAAU;QAAU;KAAQ;IACjC,OAAO;AACT;AAEA,SAAS,iBAAiB,UAAU,EAAE,QAAQ;IAC5C,OAAO,MAAM,IAAI,CAAC,WAAW,gBAAgB,CAAC;AAChD;AAEA,IAAI,kBAAkB,CAAA;IACpB,IAAI,MAAM,GAAG,aAAa,IAAI,GAAG,aAAa,CAAC,WAAW,EAAE;QAC1D,OAAO,GAAG,aAAa,CAAC,WAAW;IACrC;IACA,OAAO;AACT;AAEA,SAAS,cAAc,EAAE;IACvB,OAAO,cAAc,gBAAgB,IAAI,WAAW;AACtD;AAEA,SAAS,eAAe,SAAS,EAAE,WAAW;IAC5C,MAAM,WAAW,CAAC,CAAC,EAAE,WAAW,SAAS,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;IAC3D,MAAM,WAAW,iBAAiB,UAAU;IAC5C,IAAI,CAAC,SAAS,MAAM,EAAE;QACpB,uCAAwC,QAAQ,CAAC,gDAAgD,EAAE,UAAU,CAAC,CAAC;QAC/G,OAAO;IACT;IACA,MAAM,SAAS,SAAS,IAAI,CAAC,CAAA;QAC3B,OAAO,GAAG,YAAY,CAAC,WAAW,WAAW,MAAM;IACrD;IACA,IAAI,CAAC,QAAQ;QACX,uCAAwC,QAAQ,CAAC,oCAAoC,EAAE,YAAY,2CAA2C,CAAC;QAC/I,OAAO;IACT;IACA,IAAI,CAAC,cAAc,SAAS;QAC1B,uCAAwC,QAAQ;QAChD,OAAO;IACT;IACA,OAAO;AACT;AAEA,SAAS,gBAAgB,SAAS;IAChC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,CAAC;IAC3B,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACpC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,WAAW,YAAY,SAAS,SAAS,EAAE,EAAE,KAAK;QACtD,MAAM,QAAQ;YACZ;YACA;QACF;QACA,WAAW,OAAO,CAAC,GAAG,GAAG;QACzB,OAAO,SAAS;YACd,MAAM,UAAU,WAAW,OAAO;YAClC,MAAM,UAAU,OAAO,CAAC,GAAG;YAC3B,IAAI,YAAY,OAAO;gBACrB,OAAO,OAAO,CAAC,GAAG;YACpB;QACF;IACF,GAAG,EAAE;IACL,MAAM,eAAe,YAAY,SAAS,aAAa,cAAc;QACnE,MAAM,SAAS,eAAe,WAAW;QACzC,IAAI,UAAU,WAAW,SAAS,aAAa,EAAE;YAC/C,OAAO,KAAK;QACd;IACF,GAAG;QAAC;KAAU;IACd,MAAM,iBAAiB,YAAY,SAAS,eAAe,QAAQ,EAAE,UAAU;QAC7E,IAAI,UAAU,OAAO,KAAK,UAAU;YAClC,UAAU,OAAO,GAAG;QACtB;IACF,GAAG,EAAE;IACL,MAAM,0BAA0B,YAAY,SAAS;QACnD,IAAI,qBAAqB,OAAO,EAAE;YAChC;QACF;QACA,IAAI,CAAC,aAAa,OAAO,EAAE;YACzB;QACF;QACA,qBAAqB,OAAO,GAAG,sBAAsB;YACnD,qBAAqB,OAAO,GAAG;YAC/B,MAAM,SAAS,UAAU,OAAO;YAChC,IAAI,QAAQ;gBACV,aAAa;YACf;QACF;IACF,GAAG;QAAC;KAAa;IACjB,MAAM,iBAAiB,YAAY,SAAS,eAAe,EAAE;QAC3D,UAAU,OAAO,GAAG;QACpB,MAAM,UAAU,SAAS,aAAa;QACtC,IAAI,CAAC,SAAS;YACZ;QACF;QACA,IAAI,QAAQ,YAAY,CAAC,WAAW,WAAW,MAAM,IAAI;YACvD;QACF;QACA,UAAU,OAAO,GAAG;IACtB,GAAG,EAAE;IACL,0BAA0B;QACxB,aAAa,OAAO,GAAG;QACvB,OAAO,SAAS;YACd,aAAa,OAAO,GAAG;YACvB,MAAM,UAAU,qBAAqB,OAAO;YAC5C,IAAI,SAAS;gBACX,qBAAqB;YACvB;QACF;IACF,GAAG,EAAE;IACL,MAAM,UAAU,QAAQ,IAAM,CAAC;YAC7B;YACA;YACA;YACA;QACF,CAAC,GAAG;QAAC;QAAU;QAAgB;QAAyB;KAAe;IACvE,OAAO;AACT;AAEA,SAAS;IACP,MAAM,UAAU;QACd,YAAY,CAAC;QACb,YAAY,CAAC;IACf;IACA,MAAM,cAAc,EAAE;IACtB,SAAS,UAAU,EAAE;QACnB,YAAY,IAAI,CAAC;QACjB,OAAO,SAAS;YACd,MAAM,QAAQ,YAAY,OAAO,CAAC;YAClC,IAAI,UAAU,CAAC,GAAG;gBAChB;YACF;YACA,YAAY,MAAM,CAAC,OAAO;QAC5B;IACF;IACA,SAAS,OAAO,KAAK;QACnB,IAAI,YAAY,MAAM,EAAE;YACtB,YAAY,OAAO,CAAC,CAAA,KAAM,GAAG;QAC/B;IACF;IACA,SAAS,kBAAkB,EAAE;QAC3B,OAAO,QAAQ,UAAU,CAAC,GAAG,IAAI;IACnC;IACA,SAAS,iBAAiB,EAAE;QAC1B,MAAM,QAAQ,kBAAkB;QAChC,CAAC,QAAQ,uCAAwC,UAAU,OAAO,CAAC,qCAAqC,EAAE,GAAG,CAAC,CAAC,2CAAkB,KAAK;QACtI,OAAO;IACT;IACA,MAAM,eAAe;QACnB,UAAU,CAAA;YACR,QAAQ,UAAU,CAAC,MAAM,UAAU,CAAC,EAAE,CAAC,GAAG;YAC1C,OAAO;gBACL,MAAM;gBACN,OAAO;YACT;QACF;QACA,QAAQ,CAAC,OAAO;YACd,MAAM,UAAU,QAAQ,UAAU,CAAC,KAAK,UAAU,CAAC,EAAE,CAAC;YACtD,IAAI,CAAC,SAAS;gBACZ;YACF;YACA,IAAI,QAAQ,QAAQ,KAAK,MAAM,QAAQ,EAAE;gBACvC;YACF;YACA,OAAO,QAAQ,UAAU,CAAC,KAAK,UAAU,CAAC,EAAE,CAAC;YAC7C,QAAQ,UAAU,CAAC,MAAM,UAAU,CAAC,EAAE,CAAC,GAAG;QAC5C;QACA,YAAY,CAAA;YACV,MAAM,cAAc,MAAM,UAAU,CAAC,EAAE;YACvC,MAAM,UAAU,kBAAkB;YAClC,IAAI,CAAC,SAAS;gBACZ;YACF;YACA,IAAI,MAAM,QAAQ,KAAK,QAAQ,QAAQ,EAAE;gBACvC;YACF;YACA,OAAO,QAAQ,UAAU,CAAC,YAAY;YACtC,IAAI,QAAQ,UAAU,CAAC,MAAM,UAAU,CAAC,WAAW,CAAC,EAAE;gBACpD,OAAO;oBACL,MAAM;oBACN,OAAO;gBACT;YACF;QACF;QACA,SAAS;QACT,UAAU;QACV,QAAQ,CAAA,KAAM,QAAQ,kBAAkB;QACxC,cAAc,CAAA,OAAQ,OAAO,MAAM,CAAC,QAAQ,UAAU,EAAE,MAAM,CAAC,CAAA,QAAS,MAAM,UAAU,CAAC,IAAI,KAAK;IACpG;IACA,SAAS,kBAAkB,EAAE;QAC3B,OAAO,QAAQ,UAAU,CAAC,GAAG,IAAI;IACnC;IACA,SAAS,iBAAiB,EAAE;QAC1B,MAAM,QAAQ,kBAAkB;QAChC,CAAC,QAAQ,uCAAwC,UAAU,OAAO,CAAC,qCAAqC,EAAE,GAAG,CAAC,CAAC,2CAAkB,KAAK;QACtI,OAAO;IACT;IACA,MAAM,eAAe;QACnB,UAAU,CAAA;YACR,QAAQ,UAAU,CAAC,MAAM,UAAU,CAAC,EAAE,CAAC,GAAG;QAC5C;QACA,YAAY,CAAA;YACV,MAAM,UAAU,kBAAkB,MAAM,UAAU,CAAC,EAAE;YACrD,IAAI,CAAC,SAAS;gBACZ;YACF;YACA,IAAI,MAAM,QAAQ,KAAK,QAAQ,QAAQ,EAAE;gBACvC;YACF;YACA,OAAO,QAAQ,UAAU,CAAC,MAAM,UAAU,CAAC,EAAE,CAAC;QAChD;QACA,SAAS;QACT,UAAU;QACV,QAAQ,CAAA,KAAM,QAAQ,kBAAkB;QACxC,cAAc,CAAA,OAAQ,OAAO,MAAM,CAAC,QAAQ,UAAU,EAAE,MAAM,CAAC,CAAA,QAAS,MAAM,UAAU,CAAC,IAAI,KAAK;IACpG;IACA,SAAS;QACP,QAAQ,UAAU,GAAG,CAAC;QACtB,QAAQ,UAAU,GAAG,CAAC;QACtB,YAAY,MAAM,GAAG;IACvB;IACA,OAAO;QACL,WAAW;QACX,WAAW;QACX;QACA;IACF;AACF;AAEA,SAAS;IACP,MAAM,WAAW,QAAQ,gBAAgB,EAAE;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO,SAAS;YACd,SAAS,KAAK;QAChB;IACF,GAAG;QAAC;KAAS;IACb,OAAO;AACT;AAEA,IAAI,eAAe,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC;AAEvC,IAAI,iBAAiB;IACnB,MAAM,OAAO,SAAS,IAAI;IAC1B,CAAC,OAAO,uCAAwC,UAAU,OAAO,sEAA6C,KAAK;IACnH,OAAO;AACT;AAEA,MAAM,iBAAiB;IACrB,UAAU;IACV,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,SAAS;IACT,UAAU;IACV,MAAM;IACN,aAAa;AACf;AAEA,MAAM,QAAQ,CAAA,YAAa,CAAC,iBAAiB,EAAE,WAAW;AAC1D,SAAS,aAAa,SAAS;IAC7B,MAAM,KAAK,QAAQ,IAAM,MAAM,YAAY;QAAC;KAAU;IACtD,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QACjB,MAAM,KAAK,SAAS,aAAa,CAAC;QAClC,IAAI,OAAO,GAAG;QACd,GAAG,EAAE,GAAG;QACR,GAAG,YAAY,CAAC,aAAa;QAC7B,GAAG,YAAY,CAAC,eAAe;QAC/B,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE,GAAG,KAAK,EAAE;QACnB,iBAAiB,WAAW,CAAC;QAC7B,OAAO,SAAS;YACd,WAAW,SAAS;gBAClB,MAAM,OAAO;gBACb,IAAI,KAAK,QAAQ,CAAC,KAAK;oBACrB,KAAK,WAAW,CAAC;gBACnB;gBACA,IAAI,OAAO,IAAI,OAAO,EAAE;oBACtB,IAAI,OAAO,GAAG;gBAChB;YACF;QACF;IACF,GAAG;QAAC;KAAG;IACP,MAAM,WAAW,YAAY,CAAA;QAC3B,MAAM,KAAK,IAAI,OAAO;QACtB,IAAI,IAAI;YACN,GAAG,WAAW,GAAG;YACjB;QACF;QACA,uCAAwC,QAAQ,CAAC;;;;;;;;OAQ9C,EAAE,QAAQ;IACb,CAAC;IACH,GAAG,EAAE;IACL,OAAO;AACT;AAEA,MAAM,WAAW;IACf,WAAW;AACb;AACA,SAAS,YAAY,MAAM,EAAE,UAAU,QAAQ;IAC7C,MAAM,KAAK,qMAAA,CAAA,UAAK,CAAC,KAAK;IACtB,OAAO,QAAQ,IAAM,GAAG,SAAS,QAAQ,SAAS,GAAG,IAAI,EAAE;QAAC,QAAQ,SAAS;QAAE;QAAQ;KAAG;AAC5F;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACT;IACC,OAAO,CAAC,gBAAgB,EAAE,UAAU,CAAC,EAAE,UAAU;AACnD;AACA,SAAS,qBAAqB,EAC5B,SAAS,EACT,IAAI,EACL;IACC,MAAM,WAAW,YAAY,eAAe;QAC1C,WAAW;IACb;IACA,MAAM,KAAK,QAAQ,IAAM,aAAa;YACpC;YACA;QACF,IAAI;QAAC;QAAU;KAAU;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QACjB,MAAM,KAAK,SAAS,aAAa,CAAC;QAClC,GAAG,EAAE,GAAG;QACR,GAAG,WAAW,GAAG;QACjB,GAAG,KAAK,CAAC,OAAO,GAAG;QACnB,iBAAiB,WAAW,CAAC;QAC7B,OAAO,SAAS;YACd,MAAM,OAAO;YACb,IAAI,KAAK,QAAQ,CAAC,KAAK;gBACrB,KAAK,WAAW,CAAC;YACnB;QACF;IACF,GAAG;QAAC;QAAI;KAAK;IACb,OAAO;AACT;AAEA,IAAI,aAAa,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC;AAErC,IAAI,mBAAmB;IACtB,OAAO;AAAoB;AAE5B,MAAM,SAAS;AACf,MAAM,aAAa,CAAA;IACjB,MAAM,SAAS,OAAO,IAAI,CAAC;IAC3B,CAAC,CAAC,UAAU,IAAI,IAAI,uCAAwC,UAAU,OAAO,CAAC,8BAA8B,EAAE,OAAO,2CAAkB,KAAK;IAC5I,MAAM,QAAQ,OAAO,MAAM,CAAC,EAAE;IAC9B,MAAM,QAAQ,OAAO,MAAM,CAAC,EAAE;IAC9B,MAAM,QAAQ,OAAO,MAAM,CAAC,EAAE;IAC9B,OAAO;QACL;QACA;QACA;QACA,KAAK;IACP;AACF;AACA,MAAM,cAAc,CAAC,UAAU;IAC7B,IAAI,OAAO,KAAK,GAAG,SAAS,KAAK,EAAE;QACjC,OAAO;IACT;IACA,IAAI,OAAO,KAAK,GAAG,SAAS,KAAK,EAAE;QACjC,OAAO;IACT;IACA,IAAI,OAAO,KAAK,GAAG,SAAS,KAAK,EAAE;QACjC,OAAO;IACT;IACA,IAAI,OAAO,KAAK,GAAG,SAAS,KAAK,EAAE;QACjC,OAAO;IACT;IACA,OAAO,OAAO,KAAK,IAAI,SAAS,KAAK;AACvC;AACA,IAAI,oBAAoB,CAAC,cAAc;IACrC,MAAM,UAAU,WAAW;IAC3B,MAAM,SAAS,WAAW;IAC1B,IAAI,YAAY,SAAS,SAAS;QAChC;IACF;IACA,uCAAwC,QAAQ,CAAC;oBAC/B,EAAE,OAAO,GAAG,CAAC;wDACuB,EAAE,QAAQ,GAAG,CAAC;;;EAGpE,CAAC;AACH;AAEA,MAAM,SAAS,CAAC;;;;;AAKhB,CAAC;AACD,IAAI,eAAe,CAAA;IACjB,MAAM,UAAU,IAAI,OAAO;IAC3B,IAAI,CAAC,SAAS;QACZ,uCAAwC,QAAQ,CAAC;;;MAG/C,EAAE,OAAO;IACX,CAAC;QACD;IACF;IACA,IAAI,QAAQ,IAAI,CAAC,WAAW,OAAO,QAAQ;QACzC,uCAAwC,QAAQ,CAAC;oCACjB,EAAE,QAAQ,IAAI,CAAC;;MAE7C,EAAE,OAAO;IACX,CAAC;IACH;IACA,IAAI,QAAQ,QAAQ,KAAK,IAAI;QAC3B,uCAAwC,QAAQ,CAAC;6CACR,EAAE,QAAQ,QAAQ,CAAC;;;MAG1D,EAAE,OAAO;IACX,CAAC;IACH;AACF;AAEA,SAAS,OAAO,OAAO;IACrB,wCAA2C;QACzC;IACF;AACF;AAEA,SAAS,mBAAmB,EAAE,EAAE,MAAM;IACpC,OAAO;QACL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;YACR,IAAI;gBACF;YACF,EAAE,OAAO,GAAG;gBACV,MAAM,CAAC;;;YAGH,EAAE,EAAE,OAAO,CAAC;QAChB,CAAC;YACH;QACF,GAAG;IACL;AACF;AAEA,SAAS;IACP,mBAAmB;QACjB,kBAAkB,iBAAiB,KAAK,EAAE,qMAAA,CAAA,UAAK,CAAC,OAAO;QACvD,aAAa;IACf,GAAG,EAAE;AACP;AAEA,SAAS,YAAY,OAAO;IAC1B,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO,GAAG;IAChB;IACA,OAAO;AACT;AAEA,SAAS;IACP,IAAI,OAAO;IACX,SAAS;QACP,OAAO,QAAQ;IACjB;IACA,SAAS,SAAS,KAAK;QACrB,OAAO,UAAU;IACnB;IACA,SAAS,MAAM,OAAO;QACpB,CAAC,CAAC,OAAO,uCAAwC,UAAU,OAAO,uFAA8D,KAAK;QACrI,MAAM,UAAU;YACd;QACF;QACA,OAAO;QACP,OAAO;IACT;IACA,SAAS;QACP,CAAC,OAAO,uCAAwC,UAAU,OAAO,sFAA6D,KAAK;QACnI,OAAO;IACT;IACA,SAAS;QACP,IAAI,MAAM;YACR,KAAK,OAAO;YACZ;QACF;IACF;IACA,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;AAEA,SAAS,WAAW,KAAK;IACvB,IAAI,MAAM,KAAK,KAAK,UAAU,MAAM,KAAK,KAAK,kBAAkB;QAC9D,OAAO;IACT;IACA,OAAO,MAAM,UAAU;AACzB;AAEA,MAAM,MAAM;AACZ,MAAM,QAAQ;AACd,MAAM,SAAS;AACf,MAAM,QAAQ;AACd,MAAM,SAAS;AACf,MAAM,WAAW;AACjB,MAAM,MAAM;AACZ,MAAM,OAAO;AACb,MAAM,YAAY;AAClB,MAAM,UAAU;AAChB,MAAM,aAAa;AACnB,MAAM,YAAY;AAElB,MAAM,gBAAgB;IACpB,CAAC,MAAM,EAAE;IACT,CAAC,IAAI,EAAE;AACT;AACA,IAAI,2BAA2B,CAAA;IAC7B,IAAI,aAAa,CAAC,MAAM,OAAO,CAAC,EAAE;QAChC,MAAM,cAAc;IACtB;AACF;AAEA,MAAM,qBAAqB,CAAC;IAC1B,MAAM,OAAO;IACb,IAAI,OAAO,aAAa,aAAa;QACnC,OAAO;IACT;IACA,MAAM,aAAa;QAAC;QAAM,CAAC,EAAE,EAAE,MAAM;QAAE,CAAC,MAAM,EAAE,MAAM;QAAE,CAAC,GAAG,EAAE,MAAM;QAAE,CAAC,CAAC,EAAE,MAAM;KAAC;IACjF,MAAM,YAAY,WAAW,IAAI,CAAC,CAAA,YAAa,CAAC,EAAE,EAAE,WAAW,IAAI;IACnE,OAAO,aAAa;AACtB,CAAC;AAED,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,SAAS,+BAA+B,QAAQ,EAAE,OAAO;IACvD,OAAO,KAAK,GAAG,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC,KAAK,wBAAwB,KAAK,GAAG,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC,KAAK;AACzG;AACA,MAAM,SAAS;IACb,MAAM;AACR;AACA,SAAS,mBAAmB,EAC1B,MAAM,EACN,SAAS,EACT,QAAQ,EACR,QAAQ,EACT;IACC,OAAO;QAAC;YACN,WAAW;YACX,IAAI,CAAA;gBACF,MAAM,EACJ,MAAM,EACN,OAAO,EACP,OAAO,EACR,GAAG;gBACJ,IAAI,WAAW,eAAe;oBAC5B;gBACF;gBACA,MAAM,QAAQ;oBACZ,GAAG;oBACH,GAAG;gBACL;gBACA,MAAM,QAAQ;gBACd,IAAI,MAAM,IAAI,KAAK,YAAY;oBAC7B,MAAM,cAAc;oBACpB,MAAM,OAAO,CAAC,IAAI,CAAC;oBACnB;gBACF;gBACA,CAAC,CAAC,MAAM,IAAI,KAAK,SAAS,IAAI,uCAAwC,UAAU,OAAO,2DAAkC,KAAK;gBAC9H,MAAM,UAAU,MAAM,KAAK;gBAC3B,IAAI,CAAC,+BAA+B,SAAS,QAAQ;oBACnD;gBACF;gBACA,MAAM,cAAc;gBACpB,MAAM,UAAU,MAAM,OAAO,CAAC,SAAS,CAAC;gBACxC,SAAS;oBACP,MAAM;oBACN;gBACF;YACF;QACF;QAAG;YACD,WAAW;YACX,IAAI,CAAA;gBACF,MAAM,QAAQ;gBACd,IAAI,MAAM,IAAI,KAAK,YAAY;oBAC7B;oBACA;gBACF;gBACA,MAAM,cAAc;gBACpB,MAAM,OAAO,CAAC,IAAI,CAAC;oBACjB,sBAAsB;gBACxB;gBACA;YACF;QACF;QAAG;YACD,WAAW;YACX,IAAI,CAAA;gBACF,IAAI,WAAW,IAAI,KAAK,YAAY;oBAClC,MAAM,cAAc;gBACtB;gBACA;YACF;QACF;QAAG;YACD,WAAW;YACX,IAAI,CAAA;gBACF,MAAM,QAAQ;gBACd,IAAI,MAAM,IAAI,KAAK,WAAW;oBAC5B;oBACA;gBACF;gBACA,IAAI,MAAM,OAAO,KAAK,QAAQ;oBAC5B,MAAM,cAAc;oBACpB;oBACA;gBACF;gBACA,yBAAyB;YAC3B;QACF;QAAG;YACD,WAAW;YACX,IAAI;QACN;QAAG;YACD,WAAW;YACX,SAAS;gBACP,SAAS;gBACT,SAAS;YACX;YACA,IAAI;gBACF,IAAI,WAAW,IAAI,KAAK,WAAW;oBACjC;gBACF;YACF;QACF;QAAG;YACD,WAAW;YACX,IAAI,CAAA;gBACF,MAAM,QAAQ;gBACd,CAAC,CAAC,MAAM,IAAI,KAAK,MAAM,IAAI,uCAAwC,UAAU,OAAO,6DAAoC,KAAK;gBAC7H,IAAI,MAAM,OAAO,CAAC,uBAAuB,IAAI;oBAC3C;oBACA;gBACF;gBACA,MAAM,cAAc;YACtB;QACF;QAAG;YACD,WAAW;YACX,IAAI;QACN;KAAE;AACJ;AACA,SAAS,eAAe,GAAG;IACzB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,MAAM,sBAAsB,QAAQ,IAAM,CAAC;YACzC,WAAW;YACX,IAAI,SAAS,YAAY,KAAK;gBAC5B,IAAI,MAAM,gBAAgB,EAAE;oBAC1B;gBACF;gBACA,IAAI,MAAM,MAAM,KAAK,eAAe;oBAClC;gBACF;gBACA,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,IAAI,MAAM,QAAQ,IAAI,MAAM,MAAM,EAAE;oBACpE;gBACF;gBACA,MAAM,cAAc,IAAI,sBAAsB,CAAC;gBAC/C,IAAI,CAAC,aAAa;oBAChB;gBACF;gBACA,MAAM,UAAU,IAAI,UAAU,CAAC,aAAa,MAAM;oBAChD,aAAa;gBACf;gBACA,IAAI,CAAC,SAAS;oBACZ;gBACF;gBACA,MAAM,cAAc;gBACpB,MAAM,QAAQ;oBACZ,GAAG,MAAM,OAAO;oBAChB,GAAG,MAAM,OAAO;gBAClB;gBACA,gBAAgB,OAAO;gBACvB,iBAAiB,SAAS;YAC5B;QACF,CAAC,GAAG;QAAC;KAAI;IACT,MAAM,2BAA2B,QAAQ,IAAM,CAAC;YAC9C,WAAW;YACX,IAAI,CAAA;gBACF,IAAI,MAAM,gBAAgB,EAAE;oBAC1B;gBACF;gBACA,MAAM,KAAK,IAAI,sBAAsB,CAAC;gBACtC,IAAI,CAAC,IAAI;oBACP;gBACF;gBACA,MAAM,UAAU,IAAI,uBAAuB,CAAC;gBAC5C,IAAI,CAAC,SAAS;oBACZ;gBACF;gBACA,IAAI,QAAQ,uBAAuB,EAAE;oBACnC;gBACF;gBACA,IAAI,CAAC,IAAI,UAAU,CAAC,KAAK;oBACvB;gBACF;gBACA,MAAM,cAAc;YACtB;QACF,CAAC,GAAG;QAAC;KAAI;IACT,MAAM,mBAAmB,YAAY,SAAS;QAC5C,MAAM,UAAU;YACd,SAAS;YACT,SAAS;QACX;QACA,gBAAgB,OAAO,GAAG,WAAW,QAAQ;YAAC;YAA0B;SAAoB,EAAE;IAChG,GAAG;QAAC;QAA0B;KAAoB;IAClD,MAAM,OAAO,YAAY;QACvB,MAAM,UAAU,SAAS,OAAO;QAChC,IAAI,QAAQ,IAAI,KAAK,QAAQ;YAC3B;QACF;QACA,SAAS,OAAO,GAAG;QACnB,gBAAgB,OAAO;QACvB;IACF,GAAG;QAAC;KAAiB;IACrB,MAAM,SAAS,YAAY;QACzB,MAAM,QAAQ,SAAS,OAAO;QAC9B;QACA,IAAI,MAAM,IAAI,KAAK,YAAY;YAC7B,MAAM,OAAO,CAAC,MAAM,CAAC;gBACnB,sBAAsB;YACxB;QACF;QACA,IAAI,MAAM,IAAI,KAAK,WAAW;YAC5B,MAAM,OAAO,CAAC,KAAK;QACrB;IACF,GAAG;QAAC;KAAK;IACT,MAAM,sBAAsB,YAAY,SAAS;QAC/C,MAAM,UAAU;YACd,SAAS;YACT,SAAS;QACX;QACA,MAAM,WAAW,mBAAmB;YAClC;YACA,WAAW;YACX,UAAU,IAAM,SAAS,OAAO;YAChC,UAAU,CAAA;gBACR,SAAS,OAAO,GAAG;YACrB;QACF;QACA,gBAAgB,OAAO,GAAG,WAAW,QAAQ,UAAU;IACzD,GAAG;QAAC;QAAQ;KAAK;IACjB,MAAM,mBAAmB,YAAY,SAAS,iBAAiB,OAAO,EAAE,KAAK;QAC3E,CAAC,CAAC,SAAS,OAAO,CAAC,IAAI,KAAK,MAAM,IAAI,uCAAwC,UAAU,OAAO,uFAA8D,KAAK;QAClK,SAAS,OAAO,GAAG;YACjB,MAAM;YACN;YACA;QACF;QACA;IACF,GAAG;QAAC;KAAoB;IACxB,0BAA0B,SAAS;QACjC;QACA,OAAO,SAAS;YACd,gBAAgB,OAAO;QACzB;IACF,GAAG;QAAC;KAAiB;AACvB;AAEA,SAAS,UAAU;AACnB,MAAM,iBAAiB;IACrB,CAAC,SAAS,EAAE;IACZ,CAAC,OAAO,EAAE;IACV,CAAC,KAAK,EAAE;IACR,CAAC,IAAI,EAAE;AACT;AACA,SAAS,oBAAoB,OAAO,EAAE,IAAI;IACxC,SAAS;QACP;QACA,QAAQ,MAAM;IAChB;IACA,SAAS;QACP;QACA,QAAQ,IAAI;IACd;IACA,OAAO;QAAC;YACN,WAAW;YACX,IAAI,CAAA;gBACF,IAAI,MAAM,OAAO,KAAK,QAAQ;oBAC5B,MAAM,cAAc;oBACpB;oBACA;gBACF;gBACA,IAAI,MAAM,OAAO,KAAK,OAAO;oBAC3B,MAAM,cAAc;oBACpB;oBACA;gBACF;gBACA,IAAI,MAAM,OAAO,KAAK,WAAW;oBAC/B,MAAM,cAAc;oBACpB,QAAQ,QAAQ;oBAChB;gBACF;gBACA,IAAI,MAAM,OAAO,KAAK,SAAS;oBAC7B,MAAM,cAAc;oBACpB,QAAQ,MAAM;oBACd;gBACF;gBACA,IAAI,MAAM,OAAO,KAAK,YAAY;oBAChC,MAAM,cAAc;oBACpB,QAAQ,SAAS;oBACjB;gBACF;gBACA,IAAI,MAAM,OAAO,KAAK,WAAW;oBAC/B,MAAM,cAAc;oBACpB,QAAQ,QAAQ;oBAChB;gBACF;gBACA,IAAI,cAAc,CAAC,MAAM,OAAO,CAAC,EAAE;oBACjC,MAAM,cAAc;oBACpB;gBACF;gBACA,yBAAyB;YAC3B;QACF;QAAG;YACD,WAAW;YACX,IAAI;QACN;QAAG;YACD,WAAW;YACX,IAAI;QACN;QAAG;YACD,WAAW;YACX,IAAI;QACN;QAAG;YACD,WAAW;YACX,IAAI;QACN;QAAG;YACD,WAAW;YACX,IAAI;QACN;QAAG;YACD,WAAW;YACX,IAAI;YACJ,SAAS;gBACP,SAAS;YACX;QACF;QAAG;YACD,WAAW;YACX,IAAI;QACN;KAAE;AACJ;AACA,SAAS,kBAAkB,GAAG;IAC5B,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,MAAM,sBAAsB,QAAQ,IAAM,CAAC;YACzC,WAAW;YACX,IAAI,SAAS,UAAU,KAAK;gBAC1B,IAAI,MAAM,gBAAgB,EAAE;oBAC1B;gBACF;gBACA,IAAI,MAAM,OAAO,KAAK,OAAO;oBAC3B;gBACF;gBACA,MAAM,cAAc,IAAI,sBAAsB,CAAC;gBAC/C,IAAI,CAAC,aAAa;oBAChB;gBACF;gBACA,MAAM,UAAU,IAAI,UAAU,CAAC,aAAa,MAAM;oBAChD,aAAa;gBACf;gBACA,IAAI,CAAC,SAAS;oBACZ;gBACF;gBACA,MAAM,cAAc;gBACpB,IAAI,cAAc;gBAClB,MAAM,UAAU,QAAQ,QAAQ;gBAChC,gBAAgB,OAAO;gBACvB,SAAS;oBACP,CAAC,cAAc,uCAAwC,UAAU,OAAO,qGAA4E,KAAK;oBACzJ,cAAc;oBACd,gBAAgB,OAAO;oBACvB;gBACF;gBACA,gBAAgB,OAAO,GAAG,WAAW,QAAQ,oBAAoB,SAAS,OAAO;oBAC/E,SAAS;oBACT,SAAS;gBACX;YACF;QACF,CAAC,GAAG;QAAC;KAAI;IACT,MAAM,mBAAmB,YAAY,SAAS;QAC5C,MAAM,UAAU;YACd,SAAS;YACT,SAAS;QACX;QACA,gBAAgB,OAAO,GAAG,WAAW,QAAQ;YAAC;SAAoB,EAAE;IACtE,GAAG;QAAC;KAAoB;IACxB,0BAA0B,SAAS;QACjC;QACA,OAAO,SAAS;YACd,gBAAgB,OAAO;QACzB;IACF,GAAG;QAAC;KAAiB;AACvB;AAEA,MAAM,OAAO;IACX,MAAM;AACR;AACA,MAAM,mBAAmB;AACzB,MAAM,sBAAsB;AAC5B,SAAS,kBAAkB,EACzB,MAAM,EACN,QAAQ,EACT;IACC,OAAO;QAAC;YACN,WAAW;YACX,IAAI;QACN;QAAG;YACD,WAAW;YACX,IAAI;QACN;QAAG;YACD,WAAW;YACX,IAAI,CAAA;gBACF,MAAM,cAAc;YACtB;QACF;QAAG;YACD,WAAW;YACX,IAAI,CAAA;gBACF,IAAI,WAAW,IAAI,KAAK,YAAY;oBAClC;oBACA;gBACF;gBACA,IAAI,MAAM,OAAO,KAAK,QAAQ;oBAC5B,MAAM,cAAc;gBACtB;gBACA;YACF;QACF;QAAG;YACD,WAAW;YACX,IAAI;QACN;KAAE;AACJ;AACA,SAAS,kBAAkB,EACzB,MAAM,EACN,SAAS,EACT,QAAQ,EACT;IACC,OAAO;QAAC;YACN,WAAW;YACX,SAAS;gBACP,SAAS;YACX;YACA,IAAI,CAAA;gBACF,MAAM,QAAQ;gBACd,IAAI,MAAM,IAAI,KAAK,YAAY;oBAC7B;oBACA;gBACF;gBACA,MAAM,QAAQ,GAAG;gBACjB,MAAM,EACJ,OAAO,EACP,OAAO,EACR,GAAG,MAAM,OAAO,CAAC,EAAE;gBACpB,MAAM,QAAQ;oBACZ,GAAG;oBACH,GAAG;gBACL;gBACA,MAAM,cAAc;gBACpB,MAAM,OAAO,CAAC,IAAI,CAAC;YACrB;QACF;QAAG;YACD,WAAW;YACX,IAAI,CAAA;gBACF,MAAM,QAAQ;gBACd,IAAI,MAAM,IAAI,KAAK,YAAY;oBAC7B;oBACA;gBACF;gBACA,MAAM,cAAc;gBACpB,MAAM,OAAO,CAAC,IAAI,CAAC;oBACjB,sBAAsB;gBACxB;gBACA;YACF;QACF;QAAG;YACD,WAAW;YACX,IAAI,CAAA;gBACF,IAAI,WAAW,IAAI,KAAK,YAAY;oBAClC;oBACA;gBACF;gBACA,MAAM,cAAc;gBACpB;YACF;QACF;QAAG;YACD,WAAW;YACX,IAAI,CAAA;gBACF,MAAM,QAAQ;gBACd,CAAC,CAAC,MAAM,IAAI,KAAK,MAAM,IAAI,uCAAwC,qDAA4B,KAAK;gBACpG,MAAM,QAAQ,MAAM,OAAO,CAAC,EAAE;gBAC9B,IAAI,CAAC,OAAO;oBACV;gBACF;gBACA,MAAM,eAAe,MAAM,KAAK,IAAI;gBACpC,IAAI,CAAC,cAAc;oBACjB;gBACF;gBACA,MAAM,gBAAgB,MAAM,OAAO,CAAC,uBAAuB;gBAC3D,IAAI,MAAM,IAAI,KAAK,WAAW;oBAC5B,IAAI,eAAe;wBACjB;oBACF;oBACA;gBACF;gBACA,IAAI,eAAe;oBACjB,IAAI,MAAM,QAAQ,EAAE;wBAClB,MAAM,cAAc;wBACpB;oBACF;oBACA;oBACA;gBACF;gBACA,MAAM,cAAc;YACtB;QACF;QAAG;YACD,WAAW;YACX,IAAI;QACN;KAAE;AACJ;AACA,SAAS,eAAe,GAAG;IACzB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,MAAM,WAAW,YAAY,SAAS;QACpC,OAAO,SAAS,OAAO;IACzB,GAAG,EAAE;IACL,MAAM,WAAW,YAAY,SAAS,SAAS,KAAK;QAClD,SAAS,OAAO,GAAG;IACrB,GAAG,EAAE;IACL,MAAM,sBAAsB,QAAQ,IAAM,CAAC;YACzC,WAAW;YACX,IAAI,SAAS,aAAa,KAAK;gBAC7B,IAAI,MAAM,gBAAgB,EAAE;oBAC1B;gBACF;gBACA,MAAM,cAAc,IAAI,sBAAsB,CAAC;gBAC/C,IAAI,CAAC,aAAa;oBAChB;gBACF;gBACA,MAAM,UAAU,IAAI,UAAU,CAAC,aAAa,MAAM;oBAChD,aAAa;gBACf;gBACA,IAAI,CAAC,SAAS;oBACZ;gBACF;gBACA,MAAM,QAAQ,MAAM,OAAO,CAAC,EAAE;gBAC9B,MAAM,EACJ,OAAO,EACP,OAAO,EACR,GAAG;gBACJ,MAAM,QAAQ;oBACZ,GAAG;oBACH,GAAG;gBACL;gBACA,gBAAgB,OAAO;gBACvB,iBAAiB,SAAS;YAC5B;QACF,CAAC,GAAG;QAAC;KAAI;IACT,MAAM,mBAAmB,YAAY,SAAS;QAC5C,MAAM,UAAU;YACd,SAAS;YACT,SAAS;QACX;QACA,gBAAgB,OAAO,GAAG,WAAW,QAAQ;YAAC;SAAoB,EAAE;IACtE,GAAG;QAAC;KAAoB;IACxB,MAAM,OAAO,YAAY;QACvB,MAAM,UAAU,SAAS,OAAO;QAChC,IAAI,QAAQ,IAAI,KAAK,QAAQ;YAC3B;QACF;QACA,IAAI,QAAQ,IAAI,KAAK,WAAW;YAC9B,aAAa,QAAQ,gBAAgB;QACvC;QACA,SAAS;QACT,gBAAgB,OAAO;QACvB;IACF,GAAG;QAAC;QAAkB;KAAS;IAC/B,MAAM,SAAS,YAAY;QACzB,MAAM,QAAQ,SAAS,OAAO;QAC9B;QACA,IAAI,MAAM,IAAI,KAAK,YAAY;YAC7B,MAAM,OAAO,CAAC,MAAM,CAAC;gBACnB,sBAAsB;YACxB;QACF;QACA,IAAI,MAAM,IAAI,KAAK,WAAW;YAC5B,MAAM,OAAO,CAAC,KAAK;QACrB;IACF,GAAG;QAAC;KAAK;IACT,MAAM,sBAAsB,YAAY,SAAS;QAC/C,MAAM,UAAU;YACd,SAAS;YACT,SAAS;QACX;QACA,MAAM,OAAO;YACX;YACA,WAAW;YACX;QACF;QACA,MAAM,eAAe,WAAW,QAAQ,kBAAkB,OAAO;QACjE,MAAM,eAAe,WAAW,QAAQ,kBAAkB,OAAO;QACjE,gBAAgB,OAAO,GAAG,SAAS;YACjC;YACA;QACF;IACF,GAAG;QAAC;QAAQ;QAAU;KAAK;IAC3B,MAAM,gBAAgB,YAAY,SAAS;QACzC,MAAM,QAAQ;QACd,CAAC,CAAC,MAAM,IAAI,KAAK,SAAS,IAAI,uCAAwC,UAAU,OAAO,CAAC,iCAAiC,EAAE,MAAM,IAAI,EAAE,2CAAkB,KAAK;QAC9J,MAAM,UAAU,MAAM,OAAO,CAAC,SAAS,CAAC,MAAM,KAAK;QACnD,SAAS;YACP,MAAM;YACN;YACA,UAAU;QACZ;IACF,GAAG;QAAC;QAAU;KAAS;IACvB,MAAM,mBAAmB,YAAY,SAAS,iBAAiB,OAAO,EAAE,KAAK;QAC3E,CAAC,CAAC,WAAW,IAAI,KAAK,MAAM,IAAI,uCAAwC,UAAU,OAAO,uFAA8D,KAAK;QAC5J,MAAM,mBAAmB,WAAW,eAAe;QACnD,SAAS;YACP,MAAM;YACN;YACA;YACA;QACF;QACA;IACF,GAAG;QAAC;QAAqB;QAAU;QAAU;KAAc;IAC3D,0BAA0B,SAAS;QACjC;QACA,OAAO,SAAS;YACd,gBAAgB,OAAO;YACvB,MAAM,QAAQ;YACd,IAAI,MAAM,IAAI,KAAK,WAAW;gBAC5B,aAAa,MAAM,gBAAgB;gBACnC,SAAS;YACX;QACF;IACF,GAAG;QAAC;QAAU;QAAkB;KAAS;IACzC,0BAA0B,SAAS;QACjC,MAAM,SAAS,WAAW,QAAQ;YAAC;gBACjC,WAAW;gBACX,IAAI,KAAO;gBACX,SAAS;oBACP,SAAS;oBACT,SAAS;gBACX;YACF;SAAE;QACF,OAAO;IACT,GAAG,EAAE;AACP;AAEA,SAAS,uBAAuB,WAAW;IACzC,OAAO;QACL,MAAM,cAAc,YAAY;QAChC,mBAAmB;YACjB,CAAC,CAAC,YAAY,OAAO,CAAC,MAAM,KAAK,YAAY,MAAM,IAAI,uCAAwC,UAAU,OAAO,oGAAgF,KAAK;QACvM;IACF;AACF;AAEA,MAAM,sBAAsB;IAAC;IAAS;IAAU;IAAY;IAAU;IAAU;IAAY;IAAS;CAAQ;AAC7G,SAAS,uBAAuB,MAAM,EAAE,OAAO;IAC7C,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IACA,MAAM,sBAAsB,oBAAoB,QAAQ,CAAC,QAAQ,OAAO,CAAC,WAAW;IACpF,IAAI,qBAAqB;QACvB,OAAO;IACT;IACA,MAAM,YAAY,QAAQ,YAAY,CAAC;IACvC,IAAI,cAAc,UAAU,cAAc,IAAI;QAC5C,OAAO;IACT;IACA,IAAI,YAAY,QAAQ;QACtB,OAAO;IACT;IACA,OAAO,uBAAuB,QAAQ,QAAQ,aAAa;AAC7D;AACA,SAAS,4BAA4B,SAAS,EAAE,KAAK;IACnD,MAAM,SAAS,MAAM,MAAM;IAC3B,IAAI,CAAC,cAAc,SAAS;QAC1B,OAAO;IACT;IACA,OAAO,uBAAuB,WAAW;AAC3C;AAEA,IAAI,6BAA6B,CAAA,KAAM,CAAA,GAAA,yKAAA,CAAA,UAAO,AAAD,EAAE,GAAG,qBAAqB,IAAI,MAAM;AAEjF,SAAS,UAAU,EAAE;IACnB,OAAO,cAAc,gBAAgB,IAAI,OAAO;AAClD;AAEA,MAAM,uBAAuB,CAAC;IAC5B,MAAM,OAAO;IACb,IAAI,OAAO,aAAa,aAAa;QACnC,OAAO;IACT;IACA,MAAM,aAAa;QAAC;QAAM;QAAqB;KAAwB;IACvE,MAAM,QAAQ,WAAW,IAAI,CAAC,CAAA,OAAQ,QAAQ,QAAQ,SAAS;IAC/D,OAAO,SAAS;AAClB,CAAC;AACD,SAAS,gBAAgB,EAAE,EAAE,QAAQ;IACnC,IAAI,MAAM,MAAM;QACd,OAAO;IACT;IACA,IAAI,EAAE,CAAC,qBAAqB,CAAC,WAAW;QACtC,OAAO;IACT;IACA,OAAO,gBAAgB,GAAG,aAAa,EAAE;AAC3C;AACA,SAAS,QAAQ,EAAE,EAAE,QAAQ;IAC3B,IAAI,GAAG,OAAO,EAAE;QACd,OAAO,GAAG,OAAO,CAAC;IACpB;IACA,OAAO,gBAAgB,IAAI;AAC7B;AAEA,SAAS,YAAY,SAAS;IAC5B,OAAO,CAAC,CAAC,EAAE,WAAW,SAAS,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;AACnD;AACA,SAAS,+BAA+B,SAAS,EAAE,KAAK;IACtD,MAAM,SAAS,MAAM,MAAM;IAC3B,IAAI,CAAC,UAAU,SAAS;QACtB,uCAAwC,QAAQ;QAChD,OAAO;IACT;IACA,MAAM,WAAW,YAAY;IAC7B,MAAM,SAAS,QAAQ,QAAQ;IAC/B,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IACA,IAAI,CAAC,cAAc,SAAS;QAC1B,uCAAwC,QAAQ;QAChD,OAAO;IACT;IACA,OAAO;AACT;AACA,SAAS,kCAAkC,SAAS,EAAE,KAAK;IACzD,MAAM,SAAS,+BAA+B,WAAW;IACzD,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IACA,OAAO,OAAO,YAAY,CAAC,WAAW,WAAW;AACnD;AAEA,SAAS,cAAc,SAAS,EAAE,WAAW;IAC3C,MAAM,WAAW,CAAC,CAAC,EAAE,UAAU,SAAS,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;IAC1D,MAAM,WAAW,iBAAiB,UAAU;IAC5C,MAAM,cAAc,SAAS,IAAI,CAAC,CAAA;QAChC,OAAO,GAAG,YAAY,CAAC,UAAU,EAAE,MAAM;IAC3C;IACA,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IACA,IAAI,CAAC,cAAc,cAAc;QAC/B,uCAAwC,QAAQ;QAChD,OAAO;IACT;IACA,OAAO;AACT;AAEA,SAAS,eAAe,KAAK;IAC3B,MAAM,cAAc;AACtB;AACA,SAAS,SAAS,EAChB,QAAQ,EACR,KAAK,EACL,YAAY,EACZ,UAAU,EACX;IACC,IAAI,CAAC,gBAAgB;QACnB,IAAI,YAAY;YACd,uCAAwC,QAAQ,CAAC;;;;;;;;MAQjD,CAAC;QACH;QACA,OAAO;IACT;IACA,IAAI,aAAa,OAAO;QACtB,IAAI,YAAY;YACd,uCAAwC,QAAQ,CAAC;;;;uBAIhC,EAAE,SAAS;kDACgB,EAAE,MAAM;;;;;MAKpD,CAAC;QACH;QACA,OAAO;IACT;IACA,OAAO;AACT;AACA,SAAS,SAAS,EAChB,OAAO,EACP,KAAK,EACL,QAAQ,EACR,WAAW,EACZ;IACC,IAAI,QAAQ,SAAS,IAAI;QACvB,OAAO;IACT;IACA,MAAM,QAAQ,SAAS,SAAS,CAAC,QAAQ,CAAC;IAC1C,IAAI,CAAC,OAAO;QACV,uCAAwC,QAAQ,CAAC,kCAAkC,EAAE,aAAa;QAClG,OAAO;IACT;IACA,IAAI,CAAC,MAAM,OAAO,CAAC,SAAS,EAAE;QAC5B,OAAO;IACT;IACA,IAAI,CAAC,aAAa,MAAM,QAAQ,IAAI,cAAc;QAChD,OAAO;IACT;IACA,OAAO;AACT;AACA,SAAS,SAAS,EAChB,OAAO,EACP,SAAS,EACT,KAAK,EACL,QAAQ,EACR,WAAW,EACX,eAAe,EACf,WAAW,EACZ;IACC,MAAM,cAAc,SAAS;QAC3B;QACA;QACA;QACA;IACF;IACA,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IACA,MAAM,QAAQ,SAAS,SAAS,CAAC,OAAO,CAAC;IACzC,MAAM,KAAK,cAAc,WAAW,MAAM,UAAU,CAAC,EAAE;IACvD,IAAI,CAAC,IAAI;QACP,uCAAwC,QAAQ,CAAC,0CAA0C,EAAE,aAAa;QAC1G,OAAO;IACT;IACA,IAAI,eAAe,CAAC,MAAM,OAAO,CAAC,0BAA0B,IAAI,4BAA4B,IAAI,cAAc;QAC5G,OAAO;IACT;IACA,MAAM,OAAO,QAAQ,KAAK,CAAC,mBAAmB;IAC9C,IAAI,QAAQ;IACZ,SAAS;QACP,OAAO,MAAM,OAAO,CAAC,uBAAuB;IAC9C;IACA,SAAS;QACP,OAAO,QAAQ,QAAQ,CAAC;IAC1B;IACA,SAAS,YAAY,QAAQ,EAAE,SAAS;QACtC,IAAI,SAAS;YACX;YACA;YACA;YACA,YAAY;QACd,IAAI;YACF,MAAM,QAAQ,CAAC;QACjB;IACF;IACA,MAAM,0BAA0B,YAAY,IAAI,CAAC,MAAM;IACvD,SAAS,KAAK,IAAI;QAChB,SAAS;YACP,QAAQ,OAAO;YACf,QAAQ;QACV;QACA,IAAI,UAAU,YAAY;YACxB;YACA,uCAAwC,UAAU,OAAO,CAAC,qBAAqB,EAAE,OAAO;QAC1F;QACA,MAAM,QAAQ,CAAC,OAAO,KAAK,cAAc;QACzC,QAAQ;QACR,SAAS,OAAO,MAAM,EAAE,UAAU;YAChC,sBAAsB;QACxB,CAAC;YACC,KAAK,OAAO;YACZ,IAAI,QAAQ,oBAAoB,EAAE;gBAChC,MAAM,SAAS,WAAW,QAAQ;oBAAC;wBACjC,WAAW;wBACX,IAAI;wBACJ,SAAS;4BACP,MAAM;4BACN,SAAS;4BACT,SAAS;wBACX;oBACF;iBAAE;gBACF,WAAW;YACb;YACA;YACA,MAAM,QAAQ,CAAC,KAAK;gBAClB;YACF;QACF;QACA,OAAO;YACL,UAAU,IAAM,SAAS;oBACvB,UAAU;oBACV;oBACA;oBACA,YAAY;gBACd;YACA,yBAAyB;YACzB,MAAM,CAAA,UAAW,OAAO,QAAQ;YAChC,QAAQ,CAAA,UAAW,OAAO,UAAU;YACpC,GAAG,KAAK,OAAO;QACjB;IACF;IACA,SAAS,UAAU,eAAe;QAChC,MAAM,SAAS,CAAA,GAAA,yJAAA,CAAA,UAAO,AAAD,EAAE,CAAA;YACrB,wBAAwB,IAAM,KAAK;oBACjC;gBACF;QACF;QACA,MAAM,MAAM,KAAK;YACf,gBAAgB;gBACd,IAAI;gBACJ;gBACA,cAAc;YAChB;YACA,SAAS,IAAM,OAAO,MAAM;YAC5B,SAAS;gBACP,MAAM;YACR;QACF;QACA,OAAO;YACL,GAAG,GAAG;YACN,MAAM;QACR;IACF;IACA,SAAS;QACP,MAAM,UAAU;YACd,QAAQ,IAAM,wBAAwB;YACtC,WAAW,IAAM,wBAAwB;YACzC,UAAU,IAAM,wBAAwB;YACxC,UAAU,IAAM,wBAAwB;QAC1C;QACA,OAAO,KAAK;YACV,gBAAgB;gBACd,IAAI;gBACJ,iBAAiB,2BAA2B;gBAC5C,cAAc;YAChB;YACA,SAAS;YACT;QACF;IACF;IACA,SAAS;QACP,MAAM,gBAAgB,SAAS;YAC7B,UAAU;YACV;YACA;YACA,YAAY;QACd;QACA,IAAI,eAAe;YACjB,QAAQ,OAAO;QACjB;IACF;IACA,MAAM,UAAU;QACd,UAAU,IAAM,SAAS;gBACvB,UAAU;gBACV;gBACA;gBACA,YAAY;YACd;QACA,yBAAyB;QACzB;QACA;QACA,OAAO;IACT;IACA,OAAO;AACT;AACA,MAAM,iBAAiB;IAAC;IAAgB;IAAmB;CAAe;AAC1E,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,aAAa,EACb,oBAAoB,EACrB;IACC,MAAM,aAAa;WAAK,uBAAuB,iBAAiB,EAAE;WAAO,iBAAiB,EAAE;KAAE;IAC9F,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAM,SAAS,CAAC,EAAE;IAC3C,MAAM,iBAAiB,YAAY,SAAS,eAAe,QAAQ,EAAE,OAAO;QAC1E,IAAI,WAAW,aAAa,CAAC,WAAW,UAAU;YAChD,QAAQ,UAAU;QACpB;IACF,GAAG;QAAC;KAAQ;IACZ,0BAA0B,SAAS;QACjC,IAAI,WAAW,MAAM,QAAQ;QAC7B,MAAM,cAAc,MAAM,SAAS,CAAC;YAClC,MAAM,UAAU,MAAM,QAAQ;YAC9B,eAAe,UAAU;YACzB,WAAW;QACb;QACA,OAAO;IACT,GAAG;QAAC;QAAS;QAAO;KAAe;IACnC,0BAA0B;QACxB,OAAO,QAAQ,UAAU;IAC3B,GAAG;QAAC,QAAQ,UAAU;KAAC;IACvB,MAAM,aAAa,YAAY,CAAA;QAC7B,OAAO,SAAS;YACd;YACA;YACA;YACA;QACF;IACF,GAAG;QAAC;QAAS;QAAU;KAAM;IAC7B,MAAM,aAAa,YAAY,CAAC,aAAa,WAAW,UAAY,SAAS;YAC3E;YACA;YACA;YACA;YACA;YACA,iBAAiB,aAAa;YAC9B,aAAa,WAAW,QAAQ,WAAW,GAAG,QAAQ,WAAW,GAAG;QACtE,IAAI;QAAC;QAAW;QAAS;QAAU;KAAM;IACzC,MAAM,yBAAyB,YAAY,CAAA,QAAS,kCAAkC,WAAW,QAAQ;QAAC;KAAU;IACpH,MAAM,0BAA0B,YAAY,CAAA;QAC1C,MAAM,QAAQ,SAAS,SAAS,CAAC,QAAQ,CAAC;QAC1C,OAAO,QAAQ,MAAM,OAAO,GAAG;IACjC,GAAG;QAAC,SAAS,SAAS;KAAC;IACvB,MAAM,iBAAiB,YAAY,SAAS;QAC1C,IAAI,CAAC,QAAQ,SAAS,IAAI;YACxB;QACF;QACA,QAAQ,UAAU;QAClB,IAAI,MAAM,QAAQ,GAAG,KAAK,KAAK,QAAQ;YACrC,MAAM,QAAQ,CAAC;QACjB;IACF,GAAG;QAAC;QAAS;KAAM;IACnB,MAAM,gBAAgB,YAAY,IAAM,QAAQ,SAAS,IAAI;QAAC;KAAQ;IACtE,MAAM,MAAM,QAAQ,IAAM,CAAC;YACzB;YACA;YACA;YACA;YACA;YACA;QACF,CAAC,GAAG;QAAC;QAAY;QAAY;QAAwB;QAAyB;QAAgB;KAAc;IAC5G,uBAAuB;IACvB,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;QAC1C,UAAU,CAAC,EAAE,CAAC;IAChB;AACF;AAEA,MAAM,mBAAmB,CAAA,QAAS,CAAC;QACjC,iBAAiB,CAAA;YACf,MAAM,yBAAyB;gBAC7B,IAAI,MAAM,eAAe,EAAE;oBACzB,MAAM,eAAe,CAAC;gBACxB;YACF;YACA,CAAA,GAAA,4MAAA,CAAA,YAAS,AAAD,EAAE;QACZ;QACA,mBAAmB,MAAM,iBAAiB;QAC1C,aAAa,MAAM,WAAW;QAC9B,WAAW,MAAM,SAAS;QAC1B,cAAc,MAAM,YAAY;IAClC,CAAC;AACD,MAAM,4BAA4B,CAAA,QAAS,CAAC;QAC1C,GAAG,0BAA0B;QAC7B,GAAG,MAAM,mBAAmB;QAC5B,mBAAmB;YACjB,GAAG,2BAA2B,iBAAiB;YAC/C,GAAG,MAAM,mBAAmB;QAC9B;IACF,CAAC;AACD,SAAS,SAAS,OAAO;IACvB,CAAC,QAAQ,OAAO,GAAG,uCAAwC,UAAU,OAAO,+EAAsD,KAAK;IACvI,OAAO,QAAQ,OAAO;AACxB;AACA,SAAS,IAAI,KAAK;IAChB,MAAM,EACJ,SAAS,EACT,YAAY,EACZ,OAAO,EACP,KAAK,EACL,2BAA2B,EAC5B,GAAG;IACJ,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC5B;IACA,MAAM,eAAe,YAAY;IACjC,MAAM,gBAAgB,YAAY;QAChC,OAAO,iBAAiB,aAAa,OAAO;IAC9C,GAAG;QAAC;KAAa;IACjB,MAAM,yBAAyB,YAAY;QACzC,OAAO,0BAA0B,aAAa,OAAO;IACvD,GAAG;QAAC;KAAa;IACjB,MAAM,WAAW,aAAa;IAC9B,MAAM,gCAAgC,qBAAqB;QACzD;QACA,MAAM;IACR;IACA,MAAM,eAAe,gBAAgB,WAAW;IAChD,MAAM,eAAe,YAAY,CAAA;QAC/B,SAAS,cAAc,QAAQ,CAAC;IAClC,GAAG,EAAE;IACL,MAAM,mBAAmB,QAAQ,IAAM,CAAA,GAAA,uIAAA,CAAA,qBAAkB,AAAD,EAAE;YACxD;YACA;YACA;YACA;YACA;QACF,GAAG,eAAe;QAAC;KAAa;IAChC,MAAM,WAAW;IACjB,MAAM,mBAAmB,QAAQ;QAC/B,OAAO,uBAAuB,UAAU;IAC1C,GAAG;QAAC;QAAU;KAAiB;IAC/B,MAAM,eAAe,QAAQ,IAAM,mBAAmB;YACpD;YACA,iBAAiB,iBAAiB,eAAe;YACjD;YACA,GAAG,CAAA,GAAA,uIAAA,CAAA,qBAAkB,AAAD,EAAE;gBACpB;YACF,GAAG,aAAa;QAClB,IAAI;QAAC,iBAAiB,eAAe;QAAE;QAAc;KAAuB;IAC5E,MAAM,eAAe,gBAAgB;IACrC,MAAM,QAAQ,QAAQ,IAAM,YAAY;YACtC;YACA;YACA;YACA;YACA;YACA;QACF,IAAI;QAAC;QAAU;QAAc;QAAkB;QAAc;QAAe;KAAa;IACzF,wCAA2C;QACzC,IAAI,aAAa,OAAO,IAAI,aAAa,OAAO,KAAK,OAAO;YAC1D,uCAAwC,QAAQ;QAClD;IACF;IACA,aAAa,OAAO,GAAG;IACvB,MAAM,gBAAgB,YAAY;QAChC,MAAM,UAAU,SAAS;QACzB,MAAM,QAAQ,QAAQ,QAAQ;QAC9B,IAAI,MAAM,KAAK,KAAK,QAAQ;YAC1B,QAAQ,QAAQ,CAAC;QACnB;IACF,GAAG,EAAE;IACL,MAAM,aAAa,YAAY;QAC7B,MAAM,QAAQ,SAAS,cAAc,QAAQ;QAC7C,IAAI,MAAM,KAAK,KAAK,kBAAkB;YACpC,OAAO;QACT;QACA,IAAI,MAAM,KAAK,KAAK,QAAQ;YAC1B,OAAO;QACT;QACA,OAAO,MAAM,UAAU;IACzB,GAAG,EAAE;IACL,MAAM,eAAe,QAAQ,IAAM,CAAC;YAClC;YACA,UAAU;QACZ,CAAC,GAAG;QAAC;QAAY;KAAc;IAC/B,aAAa;IACb,MAAM,aAAa,YAAY,CAAA,KAAM,aAAa,SAAS,cAAc,QAAQ,IAAI,KAAK,EAAE;IAC5F,MAAM,uBAAuB,YAAY,IAAM,kBAAkB,SAAS,cAAc,QAAQ,KAAK,EAAE;IACvG,MAAM,aAAa,QAAQ,IAAM,CAAC;YAChC,SAAS;YACT,OAAO;YACP;YACA,SAAS;YACT,mBAAmB;YACnB;YACA;QACF,CAAC,GAAG;QAAC;QAAW;QAAkB;QAA+B;QAAc;QAAY;QAAsB;KAAS;IAC1H,iBAAiB;QACf;QACA;QACA;QACA,eAAe,WAAW;QAC1B,sBAAsB,MAAM,oBAAoB,KAAK;IACvD;IACA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;IACT,GAAG;QAAC;KAAc;IAClB,OAAO,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,WAAW,QAAQ,EAAE;QAC9C,OAAO;IACT,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,yJAAA,CAAA,WAAQ,EAAE;QAC/B,SAAS;QACT,OAAO;IACT,GAAG,MAAM,QAAQ;AACnB;AAEA,SAAS;IACP,OAAO,qMAAA,CAAA,UAAK,CAAC,KAAK;AACpB;AAEA,SAAS,gBAAgB,KAAK;IAC5B,MAAM,YAAY;IAClB,MAAM,8BAA8B,MAAM,2BAA2B,IAAI,OAAO,2BAA2B;IAC3G,OAAO,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,eAAe,MAAM,CAAA,eAAgB,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,KAAK;YACvF,OAAO,MAAM,KAAK;YAClB,WAAW;YACX,cAAc;YACd,6BAA6B;YAC7B,sBAAsB,MAAM,oBAAoB;YAChD,SAAS,MAAM,OAAO;YACtB,iBAAiB,MAAM,eAAe;YACtC,mBAAmB,MAAM,iBAAiB;YAC1C,aAAa,MAAM,WAAW;YAC9B,cAAc,MAAM,YAAY;YAChC,WAAW,MAAM,SAAS;YAC1B,qBAAqB,MAAM,mBAAmB;QAChD,GAAG,MAAM,QAAQ;AACnB;AAEA,MAAM,gBAAgB;IACpB,UAAU;IACV,eAAe;AACjB;AACA,MAAM,wBAAwB,CAAC,2BAA2B;IACxD,IAAI,UAAU;QACZ,OAAO,YAAY,IAAI,CAAC,SAAS,QAAQ;IAC3C;IACA,IAAI,2BAA2B;QAC7B,OAAO,YAAY,IAAI;IACzB;IACA,OAAO,YAAY,KAAK;AAC1B;AACA,MAAM,qBAAqB,CAAC,aAAa;IACvC,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IACA,OAAO,kBAAkB,QAAQ,OAAO,CAAC,IAAI,GAAG,QAAQ,OAAO,CAAC,SAAS;AAC3E;AACA,MAAM,2BAA2B,CAAA;IAC/B,IAAI,SAAS,kBAAkB,IAAI,MAAM;QACvC,OAAO,SAAS,kBAAkB;IACpC;IACA,OAAO,SAAS,IAAI,KAAK;AAC3B;AACA,SAAS,iBAAiB,QAAQ;IAChC,MAAM,YAAY,SAAS,SAAS;IACpC,MAAM,MAAM,UAAU,MAAM;IAC5B,MAAM,EACJ,MAAM,EACN,WAAW,EACX,QAAQ,EACT,GAAG;IACJ,MAAM,cAAc,QAAQ;IAC5B,MAAM,gBAAgB,yBAAyB;IAC/C,MAAM,kBAAkB,QAAQ;IAChC,MAAM,YAAY,kBAAkB,WAAW,IAAI,CAAC,QAAQ,eAAe,WAAW,MAAM,CAAC;IAC7F,MAAM,QAAQ;QACZ,UAAU;QACV,KAAK,IAAI,SAAS,CAAC,GAAG;QACtB,MAAM,IAAI,SAAS,CAAC,IAAI;QACxB,WAAW;QACX,OAAO,IAAI,SAAS,CAAC,KAAK;QAC1B,QAAQ,IAAI,SAAS,CAAC,MAAM;QAC5B,YAAY,sBAAsB,eAAe;QACjD;QACA,SAAS,mBAAmB,aAAa;QACzC,QAAQ,kBAAkB,cAAc,aAAa,GAAG,cAAc,QAAQ;QAC9E,eAAe;IACjB;IACA,OAAO;AACT;AACA,SAAS,kBAAkB,SAAS;IAClC,OAAO;QACL,WAAW,WAAW,MAAM,CAAC,UAAU,MAAM;QAC7C,YAAY,UAAU,yBAAyB,GAAG,YAAY;IAChE;AACF;AACA,SAAS,WAAW,MAAM;IACxB,OAAO,OAAO,IAAI,KAAK,aAAa,iBAAiB,UAAU,kBAAkB;AACnF;AAEA,SAAS,eAAe,UAAU,EAAE,EAAE,EAAE,eAAe,MAAM;IAC3D,MAAM,iBAAiB,OAAO,gBAAgB,CAAC;IAC/C,MAAM,YAAY,GAAG,qBAAqB;IAC1C,MAAM,SAAS,CAAA,GAAA,yKAAA,CAAA,eAAY,AAAD,EAAE,WAAW;IACvC,MAAM,OAAO,CAAA,GAAA,yKAAA,CAAA,aAAU,AAAD,EAAE,QAAQ;IAChC,MAAM,cAAc;QAClB;QACA,SAAS,GAAG,OAAO,CAAC,WAAW;QAC/B,SAAS,eAAe,OAAO;IACjC;IACA,MAAM,aAAa;QACjB,GAAG,OAAO,SAAS,CAAC,KAAK;QACzB,GAAG,OAAO,SAAS,CAAC,MAAM;IAC5B;IACA,MAAM,YAAY;QAChB;QACA;QACA;QACA;QACA;IACF;IACA,OAAO;AACT;AAEA,SAAS,sBAAsB,IAAI;IACjC,MAAM,WAAW,YAAY;IAC7B,MAAM,EACJ,UAAU,EACV,QAAQ,EACR,eAAe,EACf,0BAA0B,EAC1B,uBAAuB,EACvB,SAAS,EACV,GAAG;IACJ,MAAM,UAAU,QAAQ,IAAM,CAAC;YAC7B;YACA;YACA;QACF,CAAC,GAAG;QAAC;QAA4B;QAAW;KAAwB;IACpE,MAAM,eAAe,YAAY,CAAA;QAC/B,MAAM,KAAK;QACX,CAAC,KAAK,uCAAwC,UAAU,OAAO,oFAA2D,KAAK;QAC/H,OAAO,eAAe,YAAY,IAAI;IACxC,GAAG;QAAC;QAAY;KAAgB;IAChC,MAAM,QAAQ,QAAQ,IAAM,CAAC;YAC3B;YACA;YACA;YACA;QACF,CAAC,GAAG;QAAC;QAAY;QAAc;QAAS;KAAS;IACjD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACjC,0BAA0B;QACxB,SAAS,SAAS,CAAC,QAAQ,CAAC,aAAa,OAAO;QAChD,OAAO,IAAM,SAAS,SAAS,CAAC,UAAU,CAAC,aAAa,OAAO;IACjE,GAAG;QAAC,SAAS,SAAS;KAAC;IACvB,0BAA0B;QACxB,IAAI,kBAAkB,OAAO,EAAE;YAC7B,kBAAkB,OAAO,GAAG;YAC5B;QACF;QACA,MAAM,OAAO,aAAa,OAAO;QACjC,aAAa,OAAO,GAAG;QACvB,SAAS,SAAS,CAAC,MAAM,CAAC,OAAO;IACnC,GAAG;QAAC;QAAO,SAAS,SAAS;KAAC;AAChC;AAEA,IAAI,mBAAmB,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC;AAE3C,SAAS,qBAAqB,EAAE;IAC9B,CAAC,CAAC,MAAM,cAAc,GAAG,IAAI,uCAAwC,UAAU,OAAO,CAAC;;;;;EAKvF,CAAC,2CAAkB,KAAK;AAC1B;AAEA,SAAS,gBAAgB,KAAK,EAAE,SAAS,EAAE,MAAM;IAC/C,mBAAmB;QACjB,SAAS,OAAO,EAAE;YAChB,OAAO,CAAC,cAAc,EAAE,GAAG,GAAG,CAAC;QACjC;QACA,MAAM,KAAK,MAAM,WAAW;QAC5B,CAAC,KAAK,uCAAwC,UAAU,OAAO,6EAAyD,KAAK;QAC7H,CAAC,CAAC,OAAO,OAAO,QAAQ,IAAI,uCAAwC,UAAU,OAAO,CAAC;uBACnE,EAAE,OAAO,GAAG,UAAU,EAAE,GAAG,CAAC,CAAC,2CAAuB,KAAK;QAC5E,CAAC,OAAO,SAAS,CAAC,MAAM,KAAK,IAAI,uCAAwC,UAAU,OAAO,GAAG,OAAO,IAAI,+BAA+B,CAAC,2CAAuB,KAAK;QACpK,IAAI,MAAM,MAAM,CAAC,IAAI,KAAK,YAAY;YACpC;QACF;QACA,qBAAqB;QACrB,IAAI,MAAM,SAAS,EAAE;YACnB,CAAC,eAAe,WAAW,MAAM,uCAAwC,UAAU,OAAO,GAAG,OAAO,IAAI,2BAA2B,CAAC,2CAAuB,KAAK;QAClK;IACF;AACF;AACA,SAAS,uBAAuB,OAAO;IACrC,OAAO;QACL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;QAC1B,mBAAmB;YACjB,CAAC,CAAC,YAAY,WAAW,OAAO,IAAI,uCAAwC,UAAU,OAAO,uGAAmF,KAAK;QACvL,GAAG;YAAC;SAAQ;IACd;AACF;AAEA,SAAS,mBAAmB,OAAO;IACjC,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC1B,CAAC,SAAS,uCAAwC,UAAU,OAAO,4EAAmD,KAAK;IAC3H,OAAO;AACT;AAEA,SAAS,gBAAgB,KAAK;IAC5B,MAAM,cAAc;AACtB;AACA,MAAM,YAAY,CAAA;IAChB,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,SAAS,YAAY,CAAC,KAAK,IAAI;QACnC,IAAI,OAAO,GAAG;IAChB,GAAG,EAAE;IACL,MAAM,SAAS,YAAY,IAAM,IAAI,OAAO,EAAE,EAAE;IAChD,MAAM,EACJ,SAAS,EACT,6BAA6B,EAC7B,QAAQ,EACT,GAAG,mBAAmB;IACvB,MAAM,EACJ,IAAI,EACJ,WAAW,EACZ,GAAG,mBAAmB;IACvB,MAAM,aAAa,QAAQ,IAAM,CAAC;YAChC,IAAI,MAAM,WAAW;YACrB,OAAO,MAAM,KAAK;YAClB;YACA;QACF,CAAC,GAAG;QAAC,MAAM,WAAW;QAAE,MAAM,KAAK;QAAE;QAAM;KAAY;IACvD,MAAM,EACJ,QAAQ,EACR,WAAW,EACX,SAAS,EACT,uBAAuB,EACvB,0BAA0B,EAC1B,OAAO,EACP,MAAM,EACN,uBAAuB,2BAA2B,EACnD,GAAG;IACJ,gBAAgB,OAAO,WAAW;IAClC,uBAAuB;IACvB,IAAI,CAAC,SAAS;QACZ,MAAM,eAAe,QAAQ,IAAM,CAAC;gBAClC;gBACA;gBACA,iBAAiB;gBACjB;gBACA;gBACA;YACF,CAAC,GAAG;YAAC;YAAY;YAAU;YAAQ;YAA4B;YAAyB;SAAU;QAClG,sBAAsB;IACxB;IACA,MAAM,kBAAkB,QAAQ,IAAM,YAAY;YAChD,UAAU;YACV,MAAM;YACN,oBAAoB;YACpB,qCAAqC;YACrC,mCAAmC;YACnC,WAAW;YACX,aAAa;QACf,IAAI,MAAM;QAAC;QAAW;QAA+B;QAAa;KAAU;IAC5E,MAAM,YAAY,YAAY,CAAA;QAC5B,IAAI,OAAO,IAAI,KAAK,YAAY;YAC9B;QACF;QACA,IAAI,CAAC,OAAO,QAAQ,EAAE;YACpB;QACF;QACA,IAAI,MAAM,YAAY,KAAK,aAAa;YACtC;QACF;QACA,CAAA,GAAA,4MAAA,CAAA,YAAS,AAAD,EAAE;IACZ,GAAG;QAAC;QAA6B;KAAO;IACxC,MAAM,WAAW,QAAQ;QACvB,MAAM,QAAQ,WAAW;QACzB,MAAM,kBAAkB,OAAO,IAAI,KAAK,cAAc,OAAO,QAAQ,GAAG,YAAY;QACpF,MAAM,SAAS;YACb,UAAU;YACV,gBAAgB;gBACd,iCAAiC;gBACjC,yBAAyB;gBACzB;gBACA;YACF;YACA;QACF;QACA,OAAO;IACT,GAAG;QAAC;QAAW;QAAiB;QAAa;QAAQ;QAAW;KAAO;IACvE,MAAM,SAAS,QAAQ,IAAM,CAAC;YAC5B,aAAa,WAAW,EAAE;YAC1B,MAAM,WAAW,IAAI;YACrB,QAAQ;gBACN,OAAO,WAAW,KAAK;gBACvB,aAAa,WAAW,WAAW;YACrC;QACF,CAAC,GAAG;QAAC,WAAW,WAAW;QAAE,WAAW,EAAE;QAAE,WAAW,KAAK;QAAE,WAAW,IAAI;KAAC;IAC9E,OAAO,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ,EAAE,MAAM,SAAS,UAAU,OAAO,QAAQ,EAAE;AACvF;AAEA,IAAI,gBAAgB,CAAC,GAAG,IAAM,MAAM;AAEpC,IAAI,8BAA8B,CAAA;IAChC,MAAM,EACJ,OAAO,EACP,WAAW,EACZ,GAAG;IACJ,IAAI,aAAa;QACf,OAAO,YAAY,WAAW;IAChC;IACA,IAAI,SAAS;QACX,OAAO,QAAQ,WAAW;IAC5B;IACA,OAAO;AACT;AAEA,MAAM,2BAA2B,CAAA;IAC/B,OAAO,OAAO,OAAO,GAAG,OAAO,OAAO,CAAC,WAAW,GAAG;AACvD;AACA,MAAM,2BAA2B,CAAA;IAC/B,OAAO,OAAO,EAAE,IAAI,OAAO,EAAE,CAAC,IAAI,KAAK,YAAY,OAAO,EAAE,CAAC,OAAO,CAAC,WAAW,GAAG;AACrF;AACA,SAAS;IACP,MAAM,iBAAiB,WAAW,CAAC,GAAG,IAAM,CAAC;YAC3C;YACA;QACF,CAAC;IACD,MAAM,sBAAsB,WAAW,CAAC,MAAM,SAAS,eAAe,IAAI,EAAE,cAAc,IAAI,EAAE,WAAW,IAAI,GAAK,CAAC;YACnH,YAAY;YACZ;YACA,iBAAiB,QAAQ;YACzB,eAAe;YACf;YACA;YACA;YACA,kBAAkB;QACpB,CAAC;IACD,MAAM,mBAAmB,WAAW,CAAC,QAAQ,MAAM,WAAW,SAAS,eAAe,IAAI,EAAE,cAAc,IAAI,EAAE,qBAAqB,IAAI,GAAK,CAAC;YAC7I,QAAQ;gBACN,MAAM;gBACN,UAAU;gBACV;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA,UAAU,oBAAoB,MAAM,SAAS,cAAc,aAAa;YAC1E;QACF,CAAC;IACD,MAAM,WAAW,CAAC,OAAO;QACvB,IAAI,WAAW,QAAQ;YACrB,IAAI,MAAM,QAAQ,CAAC,SAAS,CAAC,EAAE,KAAK,SAAS,WAAW,EAAE;gBACxD,OAAO;YACT;YACA,MAAM,SAAS,MAAM,OAAO,CAAC,MAAM,CAAC,MAAM;YAC1C,MAAM,YAAY,MAAM,UAAU,CAAC,UAAU,CAAC,SAAS,WAAW,CAAC;YACnE,MAAM,eAAe,kBAAkB,MAAM,MAAM;YACnD,MAAM,cAAc,yBAAyB,MAAM,MAAM;YACzD,MAAM,qBAAqB,MAAM,kBAAkB;YACnD,OAAO,iBAAiB,eAAe,OAAO,CAAC,EAAE,OAAO,CAAC,GAAG,MAAM,YAAY,EAAE,WAAW,SAAS,OAAO,EAAE,cAAc,aAAa;QAC1I;QACA,IAAI,MAAM,KAAK,KAAK,kBAAkB;YACpC,MAAM,YAAY,MAAM,SAAS;YACjC,IAAI,UAAU,MAAM,CAAC,WAAW,KAAK,SAAS,WAAW,EAAE;gBACzD,OAAO;YACT;YACA,MAAM,UAAU,SAAS,OAAO;YAChC,MAAM,YAAY,MAAM,UAAU,CAAC,UAAU,CAAC,SAAS,WAAW,CAAC;YACnE,MAAM,SAAS,UAAU,MAAM;YAC/B,MAAM,OAAO,OAAO,IAAI;YACxB,MAAM,eAAe,4BAA4B;YACjD,MAAM,cAAc,yBAAyB;YAC7C,MAAM,WAAW,MAAM,YAAY;YACnC,MAAM,WAAW;gBACf;gBACA,OAAO,OAAO,IAAI;gBAClB,QAAQ,MAAM,mBAAmB;gBACjC,SAAS,cAAc,QAAQ,OAAO,CAAC,IAAI,GAAG;gBAC9C,OAAO,cAAc,QAAQ,KAAK,CAAC,IAAI,GAAG;YAC5C;YACA,OAAO;gBACL,QAAQ;oBACN,MAAM;oBACN,QAAQ,MAAM,mBAAmB;oBACjC;oBACA;oBACA;oBACA;oBACA;oBACA,oBAAoB;oBACpB,UAAU,oBAAoB,MAAM,SAAS,cAAc,aAAa;gBAC1E;YACF;QACF;QACA,OAAO;IACT;IACA,OAAO;AACT;AACA,SAAS,qBAAqB,mBAAmB,IAAI;IACnD,OAAO;QACL,YAAY;QACZ,iBAAiB;QACjB,SAAS;QACT,eAAe;QACf,MAAM;QACN,cAAc;QACd;QACA,aAAa;IACf;AACF;AACA,MAAM,SAAS;IACb,QAAQ;QACN,MAAM;QACN,QAAQ;QACR,kBAAkB;QAClB,2BAA2B;QAC3B,UAAU,qBAAqB;IACjC;AACF;AACA,SAAS;IACP,MAAM,iBAAiB,WAAW,CAAC,GAAG,IAAM,CAAC;YAC3C;YACA;QACF,CAAC;IACD,MAAM,sBAAsB,WAAW;IACvC,MAAM,mBAAmB,WAAW,CAAC,QAAQ,mBAAmB,IAAI,EAAE,4BAA8B,CAAC;YACnG,QAAQ;gBACN,MAAM;gBACN;gBACA;gBACA;gBACA,UAAU,oBAAoB;YAChC;QACF,CAAC;IACD,MAAM,cAAc,CAAA;QAClB,OAAO,mBAAmB,iBAAiB,QAAQ,kBAAkB,QAAQ;IAC/E;IACA,MAAM,WAAW,CAAC,OAAO,YAAY,QAAQ;QAC3C,MAAM,qBAAqB,OAAO,SAAS,CAAC,OAAO,CAAC,MAAM;QAC1D,MAAM,+BAA+B,QAAQ,cAAc,aAAa,IAAI,cAAc,QAAQ,CAAC,MAAM;QACzG,MAAM,UAAU,cAAc;QAC9B,MAAM,mBAAmB,WAAW,QAAQ,WAAW,KAAK,QAAQ,aAAa;QACjF,IAAI,CAAC,oBAAoB;YACvB,IAAI,CAAC,8BAA8B;gBACjC,OAAO,YAAY;YACrB;YACA,IAAI,OAAO,SAAS,CAAC,SAAS,CAAC,MAAM,EAAE;gBACrC,OAAO;YACT;YACA,MAAM,SAAS,OAAO,cAAc,WAAW,CAAC,KAAK;YACrD,MAAM,SAAS,eAAe,OAAO,CAAC,EAAE,OAAO,CAAC;YAChD,OAAO,iBAAiB,QAAQ,kBAAkB;QACpD;QACA,IAAI,8BAA8B;YAChC,OAAO,YAAY;QACrB;QACA,MAAM,aAAa,OAAO,WAAW,CAAC,KAAK;QAC3C,MAAM,SAAS,eAAe,WAAW,CAAC,EAAE,WAAW,CAAC;QACxD,OAAO,iBAAiB,QAAQ,kBAAkB,mBAAmB,aAAa;IACpF;IACA,MAAM,WAAW,CAAC,OAAO;QACvB,IAAI,WAAW,QAAQ;YACrB,IAAI,MAAM,QAAQ,CAAC,SAAS,CAAC,EAAE,KAAK,SAAS,WAAW,EAAE;gBACxD,OAAO;YACT;YACA,OAAO,SAAS,SAAS,WAAW,EAAE,MAAM,QAAQ,CAAC,SAAS,CAAC,EAAE,EAAE,MAAM,MAAM,EAAE,MAAM,aAAa;QACtG;QACA,IAAI,MAAM,KAAK,KAAK,kBAAkB;YACpC,MAAM,YAAY,MAAM,SAAS;YACjC,IAAI,UAAU,MAAM,CAAC,WAAW,KAAK,SAAS,WAAW,EAAE;gBACzD,OAAO;YACT;YACA,OAAO,SAAS,SAAS,WAAW,EAAE,UAAU,MAAM,CAAC,WAAW,EAAE,UAAU,MAAM,EAAE,UAAU,aAAa;QAC/G;QACA,OAAO;IACT;IACA,OAAO;AACT;AACA,MAAM,wBAAwB;IAC5B,MAAM,mBAAmB;IACzB,MAAM,oBAAoB;IAC1B,MAAM,WAAW,CAAC,OAAO,WAAa,iBAAiB,OAAO,aAAa,kBAAkB,OAAO,aAAa;IACjH,OAAO;AACT;AACA,MAAM,uBAAuB;IAC3B,uBAAuB;AACzB;AACA,MAAM,qBAAqB,CAAA,GAAA,yJAAA,CAAA,UAAO,AAAD,EAAE,uBAAuB,sBAAsB,MAAM;IACpF,SAAS;IACT,oBAAoB;AACtB,GAAG;AAEH,SAAS,iBAAiB,KAAK;IAC7B,MAAM,mBAAmB,mBAAmB;IAC5C,MAAM,kBAAkB,iBAAiB,eAAe;IACxD,IAAI,oBAAoB,MAAM,WAAW,IAAI,CAAC,MAAM,OAAO,EAAE;QAC3D,OAAO;IACT;IACA,OAAO,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,oBAAoB;AACjD;AACA,SAAS,gBAAgB,KAAK;IAC5B,MAAM,YAAY,OAAO,MAAM,cAAc,KAAK,YAAY,CAAC,MAAM,cAAc,GAAG;IACtF,MAAM,6BAA6B,QAAQ,MAAM,iCAAiC;IAClF,MAAM,0BAA0B,QAAQ,MAAM,uBAAuB;IACrE,OAAO,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,kBAAkB,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;QAC/D,SAAS;QACT,WAAW;QACX,4BAA4B;QAC5B,yBAAyB;IAC3B;AACF;AAEA,MAAM,UAAU,CAAA,OAAQ,CAAA,QAAS,SAAS;AAC1C,MAAM,WAAW,QAAQ;AACzB,MAAM,SAAS,QAAQ;AACvB,MAAM,YAAY,QAAQ;AAC1B,MAAM,WAAW,CAAC,UAAU,KAAO,GAAG,SAAS,SAAS,KAAK,GAAG,SAAS,SAAS;AAClF,MAAM,SAAS,CAAC,UAAU,KAAO,GAAG,SAAS,SAAS,KAAK,GAAG,SAAS,SAAS;AAChF,MAAM,sBAAsB,CAAA;IAC1B,MAAM,QAAQ,OAAO,gBAAgB,CAAC;IACtC,MAAM,WAAW;QACf,WAAW,MAAM,SAAS;QAC1B,WAAW,MAAM,SAAS;IAC5B;IACA,OAAO,SAAS,UAAU,aAAa,SAAS,UAAU;AAC5D;AACA,MAAM,mBAAmB;IACvB,uCAA2C;;IAE3C;IACA,MAAM,OAAO;IACb,MAAM,OAAO,SAAS,eAAe;IACrC,CAAC,OAAO,uCAAwC,qDAA4B,KAAK;IACjF,IAAI,CAAC,oBAAoB,OAAO;QAC9B,OAAO;IACT;IACA,MAAM,YAAY,OAAO,gBAAgB,CAAC;IAC1C,MAAM,eAAe;QACnB,WAAW,UAAU,SAAS;QAC9B,WAAW,UAAU,SAAS;IAChC;IACA,IAAI,OAAO,cAAc,YAAY;QACnC,OAAO;IACT;IACA,uCAAwC,QAAQ,CAAC;;;;;;;;;EASjD,CAAC;IACD,OAAO;AACT;AACA,MAAM,uBAAuB,CAAA;IAC3B,IAAI,MAAM,MAAM;QACd,OAAO;IACT;IACA,IAAI,OAAO,SAAS,IAAI,EAAE;QACxB,OAAO,4DAA0B;IACnC;IACA,IAAI,OAAO,SAAS,eAAe,EAAE;QACnC,OAAO;IACT;IACA,IAAI,CAAC,oBAAoB,KAAK;QAC5B,OAAO,qBAAqB,GAAG,aAAa;IAC9C;IACA,OAAO;AACT;AAEA,IAAI,iCAAiC,CAAA;IACnC,IAAI,CAAC,YAAY;QACf;IACF;IACA,MAAM,sBAAsB,qBAAqB,WAAW,aAAa;IACzE,IAAI,CAAC,qBAAqB;QACxB;IACF;IACA,uCAAwC,QAAQ,CAAC;;;;;;EAMjD,CAAC;AACH;AAEA,IAAI,YAAY,CAAA,KAAM,CAAC;QACrB,GAAG,GAAG,UAAU;QAChB,GAAG,GAAG,SAAS;IACjB,CAAC;AAED,MAAM,aAAa,CAAA;IACjB,IAAI,CAAC,IAAI;QACP,OAAO;IACT;IACA,MAAM,QAAQ,OAAO,gBAAgB,CAAC;IACtC,IAAI,MAAM,QAAQ,KAAK,SAAS;QAC9B,OAAO;IACT;IACA,OAAO,WAAW,GAAG,aAAa;AACpC;AACA,IAAI,SAAS,CAAA;IACX,MAAM,oBAAoB,qBAAqB;IAC/C,MAAM,gBAAgB,WAAW;IACjC,OAAO;QACL;QACA;IACF;AACF;AAEA,IAAI,wBAAwB,CAAC,EAC3B,UAAU,EACV,SAAS,EACT,gBAAgB,EAChB,aAAa,EACb,SAAS,EACT,MAAM,EACN,IAAI,EACJ,OAAO,EACR;IACC,MAAM,QAAQ,CAAC;QACb,IAAI,CAAC,SAAS;YACZ,OAAO;QACT;QACA,MAAM,EACJ,UAAU,EACV,QAAQ,WAAW,EACpB,GAAG;QACJ,MAAM,YAAY,aAAa;YAC7B,cAAc,WAAW,YAAY;YACrC,aAAa,WAAW,WAAW;YACnC,QAAQ,YAAY,UAAU,CAAC,MAAM;YACrC,OAAO,YAAY,UAAU,CAAC,KAAK;QACrC;QACA,OAAO;YACL,eAAe,QAAQ,IAAI,CAAC,SAAS;YACrC;YACA;YACA,mBAAmB,QAAQ,iBAAiB;YAC5C,QAAQ;gBACN,SAAS,QAAQ,MAAM;gBACvB,SAAS,QAAQ,MAAM;gBACvB,KAAK;gBACL,MAAM;oBACJ,OAAO;oBACP,cAAc;gBAChB;YACF;QACF;IACF,CAAC;IACD,MAAM,OAAO,cAAc,aAAa,WAAW;IACnD,MAAM,UAAU,WAAW;QACzB;QACA,iBAAiB;QACjB;QACA;IACF;IACA,MAAM,YAAY;QAChB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IACA,OAAO;AACT;AAEA,MAAM,YAAY,CAAC,WAAW;IAC5B,MAAM,OAAO,CAAA,GAAA,yKAAA,CAAA,SAAM,AAAD,EAAE;IACpB,IAAI,CAAC,mBAAmB;QACtB,OAAO;IACT;IACA,IAAI,cAAc,mBAAmB;QACnC,OAAO;IACT;IACA,MAAM,MAAM,KAAK,UAAU,CAAC,GAAG,GAAG,kBAAkB,SAAS;IAC7D,MAAM,OAAO,KAAK,UAAU,CAAC,IAAI,GAAG,kBAAkB,UAAU;IAChE,MAAM,SAAS,MAAM,kBAAkB,YAAY;IACnD,MAAM,QAAQ,OAAO,kBAAkB,WAAW;IAClD,MAAM,aAAa;QACjB;QACA;QACA;QACA;IACF;IACA,MAAM,YAAY,CAAA,GAAA,yKAAA,CAAA,SAAM,AAAD,EAAE,YAAY,KAAK,MAAM;IAChD,MAAM,SAAS,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE;QACvB;QACA,QAAQ,KAAK,MAAM;QACnB,QAAQ,KAAK,MAAM;QACnB,SAAS,KAAK,OAAO;IACvB;IACA,OAAO;AACT;AACA,IAAI,eAAe,CAAC,EAClB,GAAG,EACH,UAAU,EACV,GAAG,EACH,YAAY,EACZ,SAAS,EACT,cAAc,EACd,gBAAgB,EAChB,iBAAiB,EAClB;IACC,MAAM,oBAAoB,IAAI,iBAAiB;IAC/C,MAAM,SAAS,UAAU,KAAK;IAC9B,MAAM,OAAO,CAAA,GAAA,yKAAA,CAAA,aAAU,AAAD,EAAE,QAAQ;IAChC,MAAM,UAAU,CAAC;QACf,IAAI,CAAC,mBAAmB;YACtB,OAAO;QACT;QACA,MAAM,cAAc,CAAA,GAAA,yKAAA,CAAA,SAAM,AAAD,EAAE;QAC3B,MAAM,aAAa;YACjB,cAAc,kBAAkB,YAAY;YAC5C,aAAa,kBAAkB,WAAW;QAC5C;QACA,OAAO;YACL,QAAQ;YACR,MAAM,CAAA,GAAA,yKAAA,CAAA,aAAU,AAAD,EAAE,aAAa;YAC9B,QAAQ,UAAU;YAClB;YACA;QACF;IACF,CAAC;IACD,MAAM,YAAY,sBAAsB;QACtC;QACA,WAAW,CAAC;QACZ;QACA,eAAe,IAAI,aAAa;QAChC;QACA;QACA;QACA;IACF;IACA,OAAO;AACT;AAEA,MAAM,YAAY;IAChB,SAAS;AACX;AACA,MAAM,UAAU;IACd,SAAS;AACX;AACA,IAAI,qBAAqB,CAAA,UAAW,QAAQ,wBAAwB,GAAG,YAAY;AAEnF,MAAM,+BAA+B,CAAA,WAAY,YAAY,SAAS,GAAG,CAAC,iBAAiB,IAAI;AAC/F,SAAS,sBAAsB,IAAI;IACjC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAChC,MAAM,aAAa,mBAAmB;IACtC,MAAM,WAAW,YAAY;IAC7B,MAAM,EACJ,QAAQ,EACR,OAAO,EACR,GAAG;IACJ,MAAM,cAAc,YAAY;IAChC,MAAM,aAAa,QAAQ,IAAM,CAAC;YAChC,IAAI,KAAK,WAAW;YACpB,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;QACjB,CAAC,GAAG;QAAC,KAAK,WAAW;QAAE,KAAK,IAAI;QAAE,KAAK,IAAI;KAAC;IAC5C,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACtC,MAAM,uBAAuB,QAAQ,IAAM,WAAW,CAAC,GAAG;YACxD,CAAC,iBAAiB,OAAO,GAAG,uCAAwC,UAAU,OAAO,iFAAwD,KAAK;YAClJ,MAAM,SAAS;gBACb;gBACA;YACF;YACA,QAAQ,qBAAqB,CAAC,WAAW,EAAE,EAAE;QAC/C,IAAI;QAAC,WAAW,EAAE;QAAE;KAAQ;IAC5B,MAAM,mBAAmB,YAAY;QACnC,MAAM,WAAW,iBAAiB,OAAO;QACzC,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,CAAC,iBAAiB,EAAE;YAChD,OAAO;QACT;QACA,OAAO,UAAU,SAAS,GAAG,CAAC,iBAAiB;IACjD,GAAG,EAAE;IACL,MAAM,eAAe,YAAY;QAC/B,MAAM,SAAS;QACf,qBAAqB,OAAO,CAAC,EAAE,OAAO,CAAC;IACzC,GAAG;QAAC;QAAkB;KAAqB;IAC3C,MAAM,uBAAuB,QAAQ,IAAM,CAAA,GAAA,yJAAA,CAAA,UAAO,AAAD,EAAE,eAAe;QAAC;KAAa;IAChF,MAAM,kBAAkB,YAAY;QAClC,MAAM,WAAW,iBAAiB,OAAO;QACzC,MAAM,UAAU,6BAA6B;QAC7C,CAAC,CAAC,YAAY,OAAO,IAAI,uCAAwC,UAAU,OAAO,0FAAiE,KAAK;QACxJ,MAAM,UAAU,SAAS,aAAa;QACtC,IAAI,QAAQ,wBAAwB,EAAE;YACpC;YACA;QACF;QACA;IACF,GAAG;QAAC;QAAsB;KAAa;IACvC,MAAM,6BAA6B,YAAY,CAAC,cAAc;QAC5D,CAAC,CAAC,iBAAiB,OAAO,GAAG,uCAAwC,UAAU,OAAO,iGAAwE,KAAK;QACnK,MAAM,WAAW,YAAY,OAAO;QACpC,MAAM,MAAM,SAAS,eAAe;QACpC,CAAC,MAAM,uCAAwC,UAAU,OAAO,mFAA0D,KAAK;QAC/H,MAAM,MAAM,OAAO;QACnB,MAAM,WAAW;YACf;YACA;YACA;YACA,eAAe;QACjB;QACA,iBAAiB,OAAO,GAAG;QAC3B,MAAM,YAAY,aAAa;YAC7B;YACA;YACA;YACA;YACA,WAAW,SAAS,SAAS;YAC7B,gBAAgB,SAAS,cAAc;YACvC,kBAAkB,SAAS,gBAAgB;YAC3C,mBAAmB,CAAC,SAAS,uBAAuB;QACtD;QACA,MAAM,aAAa,IAAI,iBAAiB;QACxC,IAAI,YAAY;YACd,WAAW,YAAY,CAAC,gBAAgB,SAAS,EAAE,WAAW,SAAS;YACvE,WAAW,gBAAgB,CAAC,UAAU,iBAAiB,mBAAmB,SAAS,aAAa;YAChG,wCAA2C;gBACzC,+BAA+B;YACjC;QACF;QACA,OAAO;IACT,GAAG;QAAC,WAAW,SAAS;QAAE;QAAY;QAAiB;KAAY;IACnE,MAAM,yBAAyB,YAAY;QACzC,MAAM,WAAW,iBAAiB,OAAO;QACzC,MAAM,UAAU,6BAA6B;QAC7C,CAAC,CAAC,YAAY,OAAO,IAAI,uCAAwC,UAAU,OAAO,4HAAmG,KAAK;QAC1L,OAAO,UAAU;IACnB,GAAG,EAAE;IACL,MAAM,cAAc,YAAY;QAC9B,MAAM,WAAW,iBAAiB,OAAO;QACzC,CAAC,WAAW,uCAAwC,UAAU,OAAO,iFAAwD,KAAK;QAClI,MAAM,UAAU,6BAA6B;QAC7C,iBAAiB,OAAO,GAAG;QAC3B,IAAI,CAAC,SAAS;YACZ;QACF;QACA,qBAAqB,MAAM;QAC3B,QAAQ,eAAe,CAAC,gBAAgB,SAAS;QACjD,QAAQ,mBAAmB,CAAC,UAAU,iBAAiB,mBAAmB,SAAS,aAAa;IAClG,GAAG;QAAC;QAAiB;KAAqB;IAC1C,MAAM,SAAS,YAAY,CAAA;QACzB,MAAM,WAAW,iBAAiB,OAAO;QACzC,CAAC,WAAW,uCAAwC,UAAU,OAAO,gFAAuD,KAAK;QACjI,MAAM,UAAU,6BAA6B;QAC7C,CAAC,UAAU,uCAAwC,UAAU,OAAO,iGAAwE,KAAK;QACjJ,QAAQ,SAAS,IAAI,OAAO,CAAC;QAC7B,QAAQ,UAAU,IAAI,OAAO,CAAC;IAChC,GAAG,EAAE;IACL,MAAM,YAAY,QAAQ;QACxB,OAAO;YACL;YACA;YACA;YACA;QACF;IACF,GAAG;QAAC;QAAa;QAA4B;QAAwB;KAAO;IAC5E,MAAM,QAAQ,QAAQ,IAAM,CAAC;YAC3B;YACA;YACA;QACF,CAAC,GAAG;QAAC;QAAW;QAAY;KAAS;IACrC,0BAA0B;QACxB,uBAAuB,OAAO,GAAG,MAAM,UAAU;QACjD,SAAS,SAAS,CAAC,QAAQ,CAAC;QAC5B,OAAO;YACL,IAAI,iBAAiB,OAAO,EAAE;gBAC5B,uCAAwC,QAAQ;gBAChD;YACF;YACA,SAAS,SAAS,CAAC,UAAU,CAAC;QAChC;IACF,GAAG;QAAC;QAAW;QAAY;QAAa;QAAO;QAAS,SAAS,SAAS;KAAC;IAC3E,0BAA0B;QACxB,IAAI,CAAC,iBAAiB,OAAO,EAAE;YAC7B;QACF;QACA,QAAQ,wBAAwB,CAAC,uBAAuB,OAAO,CAAC,EAAE,EAAE,CAAC,KAAK,cAAc;IAC1F,GAAG;QAAC,KAAK,cAAc;QAAE;KAAQ;IACjC,0BAA0B;QACxB,IAAI,CAAC,iBAAiB,OAAO,EAAE;YAC7B;QACF;QACA,QAAQ,+BAA+B,CAAC,uBAAuB,OAAO,CAAC,EAAE,EAAE,KAAK,gBAAgB;IAClG,GAAG;QAAC,KAAK,gBAAgB;QAAE;KAAQ;AACrC;AAEA,SAAS,QAAQ;AACjB,MAAM,QAAQ;IACZ,OAAO;IACP,QAAQ;IACR,QAAQ;AACV;AACA,MAAM,UAAU,CAAC,EACf,sBAAsB,EACtB,WAAW,EACX,OAAO,EACR;IACC,IAAI,wBAAwB;QAC1B,OAAO;IACT;IACA,IAAI,YAAY,SAAS;QACvB,OAAO;IACT;IACA,OAAO;QACL,QAAQ,YAAY,MAAM,CAAC,SAAS,CAAC,MAAM;QAC3C,OAAO,YAAY,MAAM,CAAC,SAAS,CAAC,KAAK;QACzC,QAAQ,YAAY,MAAM,CAAC,MAAM;IACnC;AACF;AACA,MAAM,WAAW,CAAC,EAChB,sBAAsB,EACtB,WAAW,EACX,OAAO,EACR;IACC,MAAM,OAAO,QAAQ;QACnB;QACA;QACA;IACF;IACA,OAAO;QACL,SAAS,YAAY,OAAO;QAC5B,WAAW;QACX,OAAO,KAAK,KAAK;QACjB,QAAQ,KAAK,MAAM;QACnB,WAAW,KAAK,MAAM,CAAC,GAAG;QAC1B,aAAa,KAAK,MAAM,CAAC,KAAK;QAC9B,cAAc,KAAK,MAAM,CAAC,MAAM;QAChC,YAAY,KAAK,MAAM,CAAC,IAAI;QAC5B,YAAY;QACZ,UAAU;QACV,eAAe;QACf,YAAY,YAAY,SAAS,YAAY,WAAW,GAAG;IAC7D;AACF;AACA,MAAM,cAAc,CAAA;IAClB,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACnC,MAAM,2BAA2B,YAAY;QAC3C,IAAI,CAAC,oBAAoB,OAAO,EAAE;YAChC;QACF;QACA,aAAa,oBAAoB,OAAO;QACxC,oBAAoB,OAAO,GAAG;IAChC,GAAG,EAAE;IACL,MAAM,EACJ,OAAO,EACP,eAAe,EACf,OAAO,EACP,SAAS,EACV,GAAG;IACJ,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,OAAO,KAAK;IACvF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,wBAAwB;YAC3B,OAAO;QACT;QACA,IAAI,YAAY,QAAQ;YACtB;YACA,0BAA0B;YAC1B,OAAO;QACT;QACA,IAAI,oBAAoB,OAAO,EAAE;YAC/B,OAAO;QACT;QACA,oBAAoB,OAAO,GAAG,WAAW;YACvC,oBAAoB,OAAO,GAAG;YAC9B,0BAA0B;QAC5B;QACA,OAAO;IACT,GAAG;QAAC;QAAS;QAAwB;KAAyB;IAC9D,MAAM,kBAAkB,YAAY,CAAA;QAClC,IAAI,MAAM,YAAY,KAAK,UAAU;YACnC;QACF;QACA;QACA,IAAI,YAAY,SAAS;YACvB;QACF;IACF,GAAG;QAAC;QAAS;QAAS;KAAgB;IACtC,MAAM,QAAQ,SAAS;QACrB;QACA,SAAS,MAAM,OAAO;QACtB,aAAa,MAAM,WAAW;IAChC;IACA,OAAO,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,MAAM,WAAW,CAAC,OAAO,EAAE;QACpD;QACA,mCAAmC;QACnC,iBAAiB;QACjB,KAAK,MAAM,QAAQ;IACrB;AACF;AACA,IAAI,gBAAgB,qMAAA,CAAA,UAAK,CAAC,IAAI,CAAC;AAE/B,SAAS,UAAU,KAAK;IACtB,OAAO,OAAO,UAAU;AAC1B;AACA,SAAS,UAAU,IAAI,EAAE,MAAM;IAC7B,OAAO,OAAO,CAAC,CAAA,QAAS,MAAM;AAChC;AACA,MAAM,SAAS;IAAC,SAAS,SAAS,EAChC,KAAK,EACN;QACC,CAAC,MAAM,WAAW,GAAG,uCAAwC,UAAU,OAAO,oFAA2D,KAAK;QAC9I,CAAC,CAAC,OAAO,MAAM,WAAW,KAAK,QAAQ,IAAI,uCAAwC,UAAU,OAAO,CAAC,wDAAwD,EAAE,OAAO,MAAM,WAAW,CAAC,CAAC,CAAC,2CAAkB,KAAK;IACnN;IAAG,SAAS,QAAQ,EAClB,KAAK,EACN;QACC,CAAC,UAAU,MAAM,cAAc,IAAI,uCAAwC,UAAU,OAAO,6EAAoD,KAAK;QACrJ,CAAC,UAAU,MAAM,gBAAgB,IAAI,uCAAwC,UAAU,OAAO,+EAAsD,KAAK;QACzJ,CAAC,UAAU,MAAM,uBAAuB,IAAI,uCAAwC,UAAU,OAAO,sFAA6D,KAAK;IACzK;IAAG,SAAS,IAAI,EACd,eAAe,EAChB;QACC,qBAAqB;IACvB;CAAE;AACF,MAAM,WAAW;IAAC,SAAS,YAAY,EACrC,KAAK,EACL,iBAAiB,EAClB;QACC,IAAI,CAAC,MAAM,WAAW,EAAE;YACtB;QACF;QACA,MAAM,MAAM;QACZ,IAAI,KAAK;YACP;QACF;QACA,uCAAwC,QAAQ,CAAC;2CACR,EAAE,MAAM,WAAW,CAAC;;;;;IAK3D,CAAC;IACL;CAAE;AACF,MAAM,UAAU;IAAC,SAAS,SAAS,EACjC,KAAK,EACN;QACC,CAAC,MAAM,WAAW,GAAG,uCAAwC,UAAU,OAAO,iHAAwF,KAAK;IAC7K;IAAG,SAAS,iBAAiB,EAC3B,iBAAiB,EAClB;QACC,CAAC,CAAC,sBAAsB,uCAAwC,UAAU,OAAO,4FAAmE,KAAK;IAC3J;CAAE;AACF,SAAS,cAAc,IAAI;IACzB,mBAAmB;QACjB,UAAU,MAAM;QAChB,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,YAAY;YAClC,UAAU,MAAM;QAClB;QACA,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,WAAW;YACjC,UAAU,MAAM;QAClB;IACF;AACF;AAEA,MAAM,qBAAqB,qMAAA,CAAA,UAAK,CAAC,aAAa;IAC5C,YAAY,GAAG,IAAI,CAAE;QACnB,KAAK,IAAI;QACT,IAAI,CAAC,KAAK,GAAG;YACX,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,EAAE;YAChC,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;YACnB,SAAS,IAAI,CAAC,KAAK,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,SAAS;QAChE;QACA,IAAI,CAAC,OAAO,GAAG;YACb,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,KAAK,SAAS;gBAClC;YACF;YACA,IAAI,CAAC,QAAQ,CAAC;gBACZ,WAAW;YACb;QACF;IACF;IACA,OAAO,yBAAyB,KAAK,EAAE,KAAK,EAAE;QAC5C,IAAI,CAAC,MAAM,aAAa,EAAE;YACxB,OAAO;gBACL,WAAW,QAAQ,MAAM,EAAE;gBAC3B,MAAM,MAAM,EAAE;gBACd,SAAS;YACX;QACF;QACA,IAAI,MAAM,EAAE,EAAE;YACZ,OAAO;gBACL,WAAW;gBACX,MAAM,MAAM,EAAE;gBACd,SAAS;YACX;QACF;QACA,IAAI,MAAM,SAAS,EAAE;YACnB,OAAO;gBACL,WAAW;gBACX,MAAM,MAAM,IAAI;gBAChB,SAAS;YACX;QACF;QACA,OAAO;YACL,WAAW;YACX,SAAS;YACT,MAAM;QACR;IACF;IACA,SAAS;QACP,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;YACzB,OAAO;QACT;QACA,MAAM,WAAW;YACf,SAAS,IAAI,CAAC,OAAO;YACrB,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO;QAC7B;QACA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IAC7B;AACF;AAEA,MAAM,YAAY,CAAA;IAChB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC9B,CAAC,aAAa,uCAAwC,UAAU,OAAO,uEAA8C,KAAK;IAC1H,MAAM,EACJ,SAAS,EACT,iBAAiB,EAClB,GAAG;IACJ,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC9B,MAAM,EACJ,QAAQ,EACR,WAAW,EACX,IAAI,EACJ,IAAI,EACJ,SAAS,EACT,uBAAuB,EACvB,cAAc,EACd,gBAAgB,EAChB,QAAQ,EACR,QAAQ,EACR,uBAAuB,EACvB,oBAAoB,EACrB,GAAG;IACJ,MAAM,kBAAkB,YAAY,IAAM,aAAa,OAAO,EAAE,EAAE;IAClE,MAAM,kBAAkB,YAAY,CAAC,QAAQ,IAAI;QAC/C,aAAa,OAAO,GAAG;IACzB,GAAG,EAAE;IACL,MAAM,oBAAoB,YAAY,IAAM,eAAe,OAAO,EAAE,EAAE;IACtE,MAAM,oBAAoB,YAAY,CAAC,QAAQ,IAAI;QACjD,eAAe,OAAO,GAAG;IAC3B,GAAG,EAAE;IACL,cAAc;QACZ;QACA;QACA;IACF;IACA,MAAM,6BAA6B,YAAY;QAC7C,IAAI,qBAAqB;YACvB,wBAAwB;gBACtB,WAAW;YACb;QACF;IACF,GAAG;QAAC;QAAmB;KAAwB;IAC/C,sBAAsB;QACpB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IACA,MAAM,cAAc,QAAQ,IAAM,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,cAAc;YAClE,IAAI,MAAM,WAAW;YACrB,eAAe,MAAM,wBAAwB;QAC/C,GAAG,CAAC,EACF,OAAO,EACP,IAAI,EACJ,OAAO,EACR,GAAK,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,eAAe;gBACvC,aAAa;gBACb,SAAS;gBACT,UAAU;gBACV,SAAS;gBACT,WAAW;gBACX,iBAAiB;YACnB,KAAK;QAAC;QAAW;QAA4B,MAAM,WAAW;QAAE,MAAM,wBAAwB;QAAE;KAAkB;IAClH,MAAM,WAAW,QAAQ,IAAM,CAAC;YAC9B,UAAU;YACV;YACA,gBAAgB;gBACd,yBAAyB;gBACzB,iCAAiC;YACnC;QACF,CAAC,GAAG;QAAC;QAAW;QAAa;QAAa;KAAgB;IAC1D,MAAM,kBAAkB,WAAW,SAAS,QAAQ,CAAC,WAAW,GAAG;IACnE,MAAM,mBAAmB,QAAQ,IAAM,CAAC;YACtC;YACA;YACA;QACF,CAAC,GAAG;QAAC;QAAa;QAAiB;KAAK;IACxC,SAAS;QACP,IAAI,CAAC,UAAU;YACb,OAAO;QACT;QACA,MAAM,EACJ,QAAQ,EACR,MAAM,EACP,GAAG;QACJ,MAAM,OAAO,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,kBAAkB;YACjD,aAAa,SAAS,WAAW;YACjC,OAAO,SAAS,MAAM,CAAC,KAAK;YAC5B,SAAS;YACT,WAAW;YACX,yBAAyB;YACzB,4BAA4B;QAC9B,GAAG,CAAC,mBAAmB,oBAAsB,OAAO,mBAAmB,mBAAmB;QAC1F,OAAO,4MAAA,CAAA,UAAQ,CAAC,YAAY,CAAC,MAAM;IACrC;IACA,OAAO,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,iBAAiB,QAAQ,EAAE;QACpD,OAAO;IACT,GAAG,SAAS,UAAU,WAAW;AACnC;AAEA,SAAS;IACP,CAAC,SAAS,IAAI,GAAG,uCAAwC,UAAU,OAAO,uEAA8C,KAAK;IAC7H,OAAO,SAAS,IAAI;AACtB;AACA,MAAM,eAAe;IACnB,MAAM;IACN,MAAM;IACN,WAAW;IACX,gBAAgB;IAChB,kBAAkB;IAClB,yBAAyB;IACzB,aAAa;IACb,sBAAsB;AACxB;AACA,MAAM,+BAA+B,CAAA;IACnC,IAAI,cAAc;QAChB,GAAG,QAAQ;IACb;IACA,IAAI;IACJ,IAAK,kBAAkB,aAAc;QACnC,IAAI,QAAQ,CAAC,eAAe,KAAK,WAAW;YAC1C,cAAc;gBACZ,GAAG,WAAW;gBACd,CAAC,eAAe,EAAE,YAAY,CAAC,eAAe;YAChD;QACF;IACF;IACA,OAAO;AACT;AACA,MAAM,iBAAiB,CAAC,MAAM,WAAa,SAAS,SAAS,SAAS,CAAC,IAAI;AAC3E,MAAM,eAAe,CAAC,UAAU,aAAe,WAAW,UAAU,CAAC,SAAS,SAAS,CAAC,EAAE,CAAC;AAC3F,MAAM,sBAAsB;IAC1B,MAAM,oBAAoB;QACxB,aAAa;QACb,0BAA0B;QAC1B,UAAU;YACR,gBAAgB;YAChB,kBAAkB;YAClB,sBAAsB;YACtB,oBAAoB;QACtB;QACA,UAAU;IACZ;IACA,MAAM,uBAAuB;QAC3B,GAAG,iBAAiB;QACpB,0BAA0B;IAC5B;IACA,MAAM,qBAAqB,WAAW,CAAA,aAAc,CAAC;YACnD,aAAa,WAAW,EAAE;YAC1B,MAAM,WAAW,IAAI;YACrB,QAAQ;gBACN,OAAO,WAAW,KAAK;gBACvB,aAAa,WAAW,WAAW;YACrC;QACF,CAAC;IACD,MAAM,cAAc,WAAW,CAAC,IAAI,WAAW,2BAA2B,yBAAyB,UAAU;QAC3G,MAAM,cAAc,SAAS,UAAU,CAAC,EAAE;QAC1C,MAAM,SAAS,SAAS,UAAU,CAAC,WAAW,KAAK;QACnD,IAAI,QAAQ;YACV,MAAM,WAAW,cAAc;gBAC7B,QAAQ;gBACR,UAAU,mBAAmB,SAAS,UAAU;YAClD,IAAI;YACJ,MAAM,WAAW;gBACf,gBAAgB;gBAChB,kBAAkB,4BAA4B,cAAc;gBAC5D,sBAAsB;gBACtB,oBAAoB;YACtB;YACA,OAAO;gBACL,aAAa,SAAS,WAAW;gBACjC,0BAA0B;gBAC1B;gBACA;YACF;QACF;QACA,IAAI,CAAC,WAAW;YACd,OAAO;QACT;QACA,IAAI,CAAC,yBAAyB;YAC5B,OAAO;QACT;QACA,MAAM,WAAW;YACf,gBAAgB;YAChB,kBAAkB;YAClB,sBAAsB;YACtB,oBAAoB;QACtB;QACA,OAAO;YACL,aAAa,SAAS,WAAW;YACjC,0BAA0B;YAC1B;YACA,UAAU;QACZ;IACF;IACA,MAAM,WAAW,CAAC,OAAO;QACvB,MAAM,2BAA2B,6BAA6B;QAC9D,MAAM,KAAK,yBAAyB,WAAW;QAC/C,MAAM,OAAO,yBAAyB,IAAI;QAC1C,MAAM,YAAY,CAAC,yBAAyB,cAAc;QAC1D,MAAM,cAAc,yBAAyB,WAAW;QACxD,IAAI,WAAW,QAAQ;YACrB,MAAM,WAAW,MAAM,QAAQ;YAC/B,IAAI,CAAC,eAAe,MAAM,WAAW;gBACnC,OAAO;YACT;YACA,MAAM,WAAW,aAAa,UAAU,MAAM,UAAU;YACxD,MAAM,iBAAiB,kBAAkB,MAAM,MAAM,MAAM;YAC3D,OAAO,YAAY,IAAI,WAAW,gBAAgB,gBAAgB,UAAU;QAC9E;QACA,IAAI,MAAM,KAAK,KAAK,kBAAkB;YACpC,MAAM,YAAY,MAAM,SAAS;YACjC,IAAI,CAAC,eAAe,MAAM,UAAU,QAAQ,GAAG;gBAC7C,OAAO;YACT;YACA,MAAM,WAAW,aAAa,UAAU,QAAQ,EAAE,MAAM,UAAU;YAClE,OAAO,YAAY,IAAI,WAAW,4BAA4B,UAAU,MAAM,MAAM,IAAI,kBAAkB,UAAU,MAAM,MAAM,IAAI,UAAU;QAChJ;QACA,IAAI,MAAM,KAAK,KAAK,UAAU,MAAM,SAAS,IAAI,CAAC,MAAM,WAAW,EAAE;YACnE,MAAM,YAAY,MAAM,SAAS;YACjC,IAAI,CAAC,eAAe,MAAM,UAAU,QAAQ,GAAG;gBAC7C,OAAO;YACT;YACA,MAAM,UAAU,kBAAkB,UAAU,MAAM,MAAM;YACxD,MAAM,eAAe,QAAQ,UAAU,MAAM,CAAC,EAAE,IAAI,UAAU,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK;YACjF,MAAM,SAAS,UAAU,QAAQ,CAAC,SAAS,CAAC,EAAE,KAAK;YACnD,IAAI,SAAS;gBACX,OAAO,eAAe,oBAAoB;YAC5C;YACA,IAAI,QAAQ;gBACV,OAAO;YACT;YACA,OAAO;QACT;QACA,OAAO;IACT;IACA,OAAO;AACT;AACA,MAAM,qBAAqB;IACzB,yBAAyB;AAC3B;AACA,MAAM,qBAAqB,CAAA,GAAA,yJAAA,CAAA,UAAO,AAAD,EAAE,qBAAqB,oBAAoB,CAAC,YAAY,eAAe;IACtG,OAAO;QACL,GAAG,6BAA6B,SAAS;QACzC,GAAG,UAAU;QACb,GAAG,aAAa;IAClB;AACF,GAAG;IACD,SAAS;IACT,oBAAoB;AACtB,GAAG", "ignoreList": [0], "debugId": null}}]}