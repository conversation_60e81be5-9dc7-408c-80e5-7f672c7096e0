{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/augment%E9%A1%B9%E7%9B%AE/todo/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Plus,\n  Moon,\n  Sun,\n  Check,\n  Trash2,\n  Edit2,\n  Calendar,\n  Tag,\n  Filter,\n  Search,\n  Star,\n  Clock,\n  AlertCircle,\n  CheckCircle2,\n  Circle,\n  MoreHorizontal\n} from 'lucide-react';\nimport { useThemeStore } from '@/store/themeStore';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Input } from '@/components/ui/input';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Badge } from '@/components/ui/badge';\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';\nimport { Separator } from '@/components/ui/separator';\nimport { Progress } from '@/components/ui/progress';\n\ninterface Todo {\n  id: string;\n  title: string;\n  description?: string;\n  completed: boolean;\n  priority: 'low' | 'medium' | 'high';\n  category: string;\n  tags: string[];\n  dueDate?: Date;\n  createdAt: Date;\n  important: boolean;\n}\n\ninterface Category {\n  id: string;\n  name: string;\n  color: string;\n}\n\nconst defaultCategories: Category[] = [\n  { id: '1', name: 'Personal', color: '#3B82F6' },\n  { id: '2', name: 'Work', color: '#EF4444' },\n  { id: '3', name: 'Shopping', color: '#10B981' },\n  { id: '4', name: 'Health', color: '#F59E0B' },\n];\n\nexport default function Home() {\n  const { theme, toggleTheme } = useThemeStore();\n  const [todos, setTodos] = useState<Todo[]>([]);\n  const [categories] = useState<Category[]>(defaultCategories);\n  const [newTodo, setNewTodo] = useState('');\n  const [newDescription, setNewDescription] = useState('');\n  const [newPriority, setNewPriority] = useState<'low' | 'medium' | 'high'>('medium');\n  const [newCategory, setNewCategory] = useState('');\n  const [newDueDate, setNewDueDate] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterCategory, setFilterCategory] = useState('');\n  const [filterPriority, setFilterPriority] = useState('');\n  const [showCompleted, setShowCompleted] = useState(true);\n  const [isAddingTodo, setIsAddingTodo] = useState(false);\n  const [editingTodo, setEditingTodo] = useState<string | null>(null);\n\n  // Load todos from localStorage\n  useEffect(() => {\n    const savedTodos = localStorage.getItem('todos');\n    if (savedTodos) {\n      const parsedTodos = JSON.parse(savedTodos).map((todo: any) => ({\n        ...todo,\n        createdAt: new Date(todo.createdAt),\n        dueDate: todo.dueDate ? new Date(todo.dueDate) : undefined,\n      }));\n      setTodos(parsedTodos);\n    }\n  }, []);\n\n  // Save todos to localStorage\n  useEffect(() => {\n    localStorage.setItem('todos', JSON.stringify(todos));\n  }, [todos]);\n\n  const addTodo = () => {\n    if (newTodo.trim()) {\n      const todo: Todo = {\n        id: Date.now().toString(),\n        title: newTodo.trim(),\n        description: newDescription.trim() || undefined,\n        completed: false,\n        priority: newPriority,\n        category: newCategory,\n        tags: [],\n        dueDate: newDueDate ? new Date(newDueDate) : undefined,\n        createdAt: new Date(),\n        important: false,\n      };\n      setTodos([todo, ...todos]);\n      setNewTodo('');\n      setNewDescription('');\n      setNewPriority('medium');\n      setNewCategory('');\n      setNewDueDate('');\n      setIsAddingTodo(false);\n    }\n  };\n\n  const toggleTodo = (id: string) => {\n    setTodos(todos.map(todo =>\n      todo.id === id ? { ...todo, completed: !todo.completed } : todo\n    ));\n  };\n\n  const toggleImportant = (id: string) => {\n    setTodos(todos.map(todo =>\n      todo.id === id ? { ...todo, important: !todo.important } : todo\n    ));\n  };\n\n  const deleteTodo = (id: string) => {\n    setTodos(todos.filter(todo => todo.id !== id));\n  };\n\n  const updateTodo = (id: string, updates: Partial<Todo>) => {\n    setTodos(todos.map(todo =>\n      todo.id === id ? { ...todo, ...updates } : todo\n    ));\n  };\n\n  // Filter and search todos\n  const filteredTodos = todos.filter(todo => {\n    const matchesSearch = todo.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         todo.description?.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = !filterCategory || todo.category === filterCategory;\n    const matchesPriority = !filterPriority || todo.priority === filterPriority;\n    const matchesCompleted = showCompleted || !todo.completed;\n\n    return matchesSearch && matchesCategory && matchesPriority && matchesCompleted;\n  });\n\n  const completedCount = todos.filter(todo => todo.completed).length;\n  const importantCount = todos.filter(todo => todo.important && !todo.completed).length;\n  const overdueCount = todos.filter(todo =>\n    !todo.completed && todo.dueDate && todo.dueDate < new Date()\n  ).length;\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'high': return 'text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800';\n      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:text-yellow-400 dark:bg-yellow-900/20 dark:border-yellow-800';\n      case 'low': return 'text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800';\n      default: return 'text-gray-600 bg-gray-50 border-gray-200 dark:text-gray-400 dark:bg-gray-800/20 dark:border-gray-700';\n    }\n  };\n\n  const formatDate = (date: Date) => {\n    const today = new Date();\n    const tomorrow = new Date(today);\n    tomorrow.setDate(tomorrow.getDate() + 1);\n\n    if (date.toDateString() === today.toDateString()) return 'Today';\n    if (date.toDateString() === tomorrow.toDateString()) return 'Tomorrow';\n    return date.toLocaleDateString();\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-blue-900/20 dark:to-indigo-900/20 transition-all duration-500\">\n      {/* Header */}\n      <header className=\"backdrop-blur-xl bg-white/70 dark:bg-gray-900/70 border-b border-white/20 dark:border-gray-700/50 sticky top-0 z-50 shadow-lg shadow-black/5\">\n        <div className=\"max-w-6xl mx-auto px-6 py-6\">\n          <div className=\"flex items-center justify-between\">\n            <motion.div\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              className=\"flex items-center gap-4\"\n            >\n              <div className=\"relative\">\n                <div className=\"w-12 h-12 bg-gradient-to-br from-violet-500 via-purple-500 to-blue-500 rounded-2xl flex items-center justify-center shadow-xl shadow-purple-500/25\">\n                  <span className=\"text-white font-bold text-xl\">✓</span>\n                </div>\n                <div className=\"absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full animate-pulse\"></div>\n              </div>\n              <div>\n                <h1 className=\"text-3xl font-bold bg-gradient-to-r from-gray-900 via-purple-900 to-violet-900 dark:from-white dark:via-purple-100 dark:to-violet-100 bg-clip-text text-transparent\">\n                  TodoMaster\n                </h1>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400 font-medium\">\n                  ✨ 让生活更有序，让梦想更清晰\n                </p>\n              </div>\n            </motion.div>\n\n            <div className=\"flex items-center gap-3\">\n              <motion.div className=\"hidden md:flex items-center gap-2 px-4 py-2 bg-white/50 dark:bg-gray-800/50 rounded-full backdrop-blur-sm border border-white/20 dark:border-gray-700/50\">\n                <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  {todos.length} 个任务\n                </span>\n              </motion.div>\n\n              <motion.button\n                onClick={toggleTheme}\n                className=\"p-3 rounded-2xl bg-white/80 dark:bg-gray-800/80 hover:bg-white dark:hover:bg-gray-700 transition-all duration-300 shadow-lg shadow-black/5 border border-white/20 dark:border-gray-700/50 backdrop-blur-sm\"\n                whileHover={{ scale: 1.05, rotate: 180 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                <AnimatePresence mode=\"wait\">\n                  {theme === 'dark' ? (\n                    <motion.div\n                      key=\"sun\"\n                      initial={{ opacity: 0, rotate: -180 }}\n                      animate={{ opacity: 1, rotate: 0 }}\n                      exit={{ opacity: 0, rotate: 180 }}\n                      transition={{ duration: 0.3 }}\n                    >\n                      <Sun className=\"h-5 w-5 text-amber-500\" />\n                    </motion.div>\n                  ) : (\n                    <motion.div\n                      key=\"moon\"\n                      initial={{ opacity: 0, rotate: -180 }}\n                      animate={{ opacity: 1, rotate: 0 }}\n                      exit={{ opacity: 0, rotate: 180 }}\n                      transition={{ duration: 0.3 }}\n                    >\n                      <Moon className=\"h-5 w-5 text-slate-700\" />\n                    </motion.div>\n                  )}\n                </AnimatePresence>\n              </motion.button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-6xl mx-auto px-6 py-8\">\n        <div className=\"space-y-8\">\n          {/* Stats Dashboard */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            className=\"grid grid-cols-1 md:grid-cols-4 gap-6\"\n          >\n            {/* Total Tasks */}\n            <motion.div\n              whileHover={{ scale: 1.02, y: -2 }}\n              className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-3xl p-6 shadow-xl shadow-black/5 border border-white/20 dark:border-gray-700/50\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">总任务</p>\n                  <p className=\"text-3xl font-bold text-gray-900 dark:text-white\">{todos.length}</p>\n                </div>\n                <div className=\"w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center shadow-lg shadow-blue-500/25\">\n                  <Clock className=\"h-6 w-6 text-white\" />\n                </div>\n              </div>\n            </motion.div>\n\n            {/* Completed Tasks */}\n            <motion.div\n              whileHover={{ scale: 1.02, y: -2 }}\n              className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-3xl p-6 shadow-xl shadow-black/5 border border-white/20 dark:border-gray-700/50\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">已完成</p>\n                  <p className=\"text-3xl font-bold text-emerald-600\">{completedCount}</p>\n                </div>\n                <div className=\"w-12 h-12 bg-gradient-to-br from-emerald-500 to-green-500 rounded-2xl flex items-center justify-center shadow-lg shadow-emerald-500/25\">\n                  <Check className=\"h-6 w-6 text-white\" />\n                </div>\n              </div>\n            </motion.div>\n\n            {/* Important Tasks */}\n            <motion.div\n              whileHover={{ scale: 1.02, y: -2 }}\n              className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-3xl p-6 shadow-xl shadow-black/5 border border-white/20 dark:border-gray-700/50\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">重要任务</p>\n                  <p className=\"text-3xl font-bold text-amber-600\">{importantCount}</p>\n                </div>\n                <div className=\"w-12 h-12 bg-gradient-to-br from-amber-500 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg shadow-amber-500/25\">\n                  <Star className=\"h-6 w-6 text-white\" />\n                </div>\n              </div>\n            </motion.div>\n\n            {/* Overdue Tasks */}\n            <motion.div\n              whileHover={{ scale: 1.02, y: -2 }}\n              className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-3xl p-6 shadow-xl shadow-black/5 border border-white/20 dark:border-gray-700/50\"\n            >\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">已逾期</p>\n                  <p className=\"text-3xl font-bold text-red-600\">{overdueCount}</p>\n                </div>\n                <div className=\"w-12 h-12 bg-gradient-to-br from-red-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg shadow-red-500/25\">\n                  <AlertCircle className=\"h-6 w-6 text-white\" />\n                </div>\n              </div>\n            </motion.div>\n          </motion.div>\n\n          {/* Progress Bar */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.1 }}\n            className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-3xl p-6 shadow-xl shadow-black/5 border border-white/20 dark:border-gray-700/50\"\n          >\n            <div className=\"flex items-center justify-between mb-4\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">今日进度</h3>\n              <span className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {todos.length > 0 ? `${Math.round((completedCount / todos.length) * 100)}%` : '0%'}\n              </span>\n            </div>\n            <div className=\"relative w-full h-4 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden\">\n              <motion.div\n                className=\"absolute top-0 left-0 h-full bg-gradient-to-r from-violet-500 via-purple-500 to-blue-500 rounded-full shadow-lg\"\n                initial={{ width: 0 }}\n                animate={{ width: todos.length > 0 ? `${(completedCount / todos.length) * 100}%` : '0%' }}\n                transition={{ duration: 1, ease: \"easeOut\" }}\n              />\n              <div className=\"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent rounded-full\"></div>\n            </div>\n          </motion.div>\n\n          {/* Search and Filters */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.2 }}\n            className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-3xl p-6 shadow-xl shadow-black/5 border border-white/20 dark:border-gray-700/50\"\n          >\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-4\">\n              {/* Search */}\n              <div className=\"relative\">\n                <Search className=\"absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400\" />\n                <input\n                  type=\"text\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  placeholder=\"搜索任务...\"\n                  className=\"w-full pl-12 pr-4 py-3 bg-white/50 dark:bg-gray-700/50 border border-gray-200/50 dark:border-gray-600/50 rounded-2xl focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 backdrop-blur-sm transition-all\"\n                />\n              </div>\n\n              {/* Category Filter */}\n              <select\n                value={filterCategory}\n                onChange={(e) => setFilterCategory(e.target.value)}\n                className=\"px-4 py-3 bg-white/50 dark:bg-gray-700/50 border border-gray-200/50 dark:border-gray-600/50 rounded-2xl focus:outline-none focus:ring-2 focus:ring-violet-500 text-gray-900 dark:text-white backdrop-blur-sm transition-all\"\n              >\n                <option value=\"\">所有分类</option>\n                {categories.map((category) => (\n                  <option key={category.id} value={category.id}>\n                    {category.name}\n                  </option>\n                ))}\n              </select>\n\n              {/* Priority Filter */}\n              <select\n                value={filterPriority}\n                onChange={(e) => setFilterPriority(e.target.value)}\n                className=\"px-4 py-3 bg-white/50 dark:bg-gray-700/50 border border-gray-200/50 dark:border-gray-600/50 rounded-2xl focus:outline-none focus:ring-2 focus:ring-violet-500 text-gray-900 dark:text-white backdrop-blur-sm transition-all\"\n              >\n                <option value=\"\">所有优先级</option>\n                <option value=\"high\">高优先级</option>\n                <option value=\"medium\">中优先级</option>\n                <option value=\"low\">低优先级</option>\n              </select>\n            </div>\n\n            <div className=\"flex items-center justify-between mt-4\">\n              <motion.button\n                onClick={() => setShowCompleted(!showCompleted)}\n                className={`px-4 py-2 rounded-2xl text-sm font-medium transition-all ${\n                  showCompleted\n                    ? 'bg-violet-500 text-white shadow-lg shadow-violet-500/25'\n                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300'\n                }`}\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n              >\n                {showCompleted ? '隐藏已完成' : '显示已完成'}\n              </motion.button>\n\n              <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                找到 {filteredTodos.length} 个任务\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Add Todo */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3 }}\n            className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-black/5 border border-white/20 dark:border-gray-700/50 overflow-hidden\"\n          >\n            <div className=\"p-6\">\n              <div className=\"flex items-center gap-3 mb-4\">\n                <div className=\"w-8 h-8 bg-gradient-to-br from-violet-500 to-purple-500 rounded-xl flex items-center justify-center\">\n                  <Plus className=\"h-4 w-4 text-white\" />\n                </div>\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">添加新任务</h3>\n              </div>\n\n              <div className=\"space-y-4\">\n                <input\n                  type=\"text\"\n                  value={newTodo}\n                  onChange={(e) => setNewTodo(e.target.value)}\n                  onKeyPress={(e) => e.key === 'Enter' && !e.shiftKey && addTodo()}\n                  placeholder=\"输入任务标题...\"\n                  className=\"w-full px-4 py-3 bg-white/50 dark:bg-gray-700/50 border border-gray-200/50 dark:border-gray-600/50 rounded-2xl focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 backdrop-blur-sm transition-all\"\n                />\n\n                <AnimatePresence>\n                  {isAddingTodo && (\n                    <motion.div\n                      initial={{ height: 0, opacity: 0 }}\n                      animate={{ height: 'auto', opacity: 1 }}\n                      exit={{ height: 0, opacity: 0 }}\n                      transition={{ duration: 0.3 }}\n                      className=\"space-y-4\"\n                    >\n                      <textarea\n                        value={newDescription}\n                        onChange={(e) => setNewDescription(e.target.value)}\n                        placeholder=\"任务描述（可选）...\"\n                        rows={3}\n                        className=\"w-full px-4 py-3 bg-white/50 dark:bg-gray-700/50 border border-gray-200/50 dark:border-gray-600/50 rounded-2xl focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 backdrop-blur-sm transition-all resize-none\"\n                      />\n\n                      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                        <select\n                          value={newPriority}\n                          onChange={(e) => setNewPriority(e.target.value as 'low' | 'medium' | 'high')}\n                          className=\"px-4 py-3 bg-white/50 dark:bg-gray-700/50 border border-gray-200/50 dark:border-gray-600/50 rounded-2xl focus:outline-none focus:ring-2 focus:ring-violet-500 text-gray-900 dark:text-white backdrop-blur-sm transition-all\"\n                        >\n                          <option value=\"low\">低优先级</option>\n                          <option value=\"medium\">中优先级</option>\n                          <option value=\"high\">高优先级</option>\n                        </select>\n\n                        <select\n                          value={newCategory}\n                          onChange={(e) => setNewCategory(e.target.value)}\n                          className=\"px-4 py-3 bg-white/50 dark:bg-gray-700/50 border border-gray-200/50 dark:border-gray-600/50 rounded-2xl focus:outline-none focus:ring-2 focus:ring-violet-500 text-gray-900 dark:text-white backdrop-blur-sm transition-all\"\n                        >\n                          <option value=\"\">选择分类</option>\n                          {categories.map((category) => (\n                            <option key={category.id} value={category.id}>\n                              {category.name}\n                            </option>\n                          ))}\n                        </select>\n\n                        <input\n                          type=\"datetime-local\"\n                          value={newDueDate}\n                          onChange={(e) => setNewDueDate(e.target.value)}\n                          className=\"px-4 py-3 bg-white/50 dark:bg-gray-700/50 border border-gray-200/50 dark:border-gray-600/50 rounded-2xl focus:outline-none focus:ring-2 focus:ring-violet-500 text-gray-900 dark:text-white backdrop-blur-sm transition-all\"\n                        />\n                      </div>\n                    </motion.div>\n                  )}\n                </AnimatePresence>\n\n                <div className=\"flex items-center gap-3\">\n                  <motion.button\n                    onClick={() => setIsAddingTodo(!isAddingTodo)}\n                    className=\"px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-2xl hover:bg-gray-200 dark:hover:bg-gray-600 transition-all text-sm font-medium\"\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                  >\n                    {isAddingTodo ? '简单模式' : '详细模式'}\n                  </motion.button>\n\n                  <motion.button\n                    onClick={addTodo}\n                    disabled={!newTodo.trim()}\n                    className=\"flex-1 px-6 py-3 bg-gradient-to-r from-violet-500 via-purple-500 to-blue-500 text-white rounded-2xl hover:from-violet-600 hover:via-purple-600 hover:to-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all shadow-lg shadow-violet-500/25 font-medium\"\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                  >\n                    <div className=\"flex items-center justify-center gap-2\">\n                      <Plus className=\"h-5 w-5\" />\n                      添加任务\n                    </div>\n                  </motion.button>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Todo List */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.4 }}\n            className=\"space-y-4\"\n          >\n            {filteredTodos.length === 0 ? (\n              <motion.div\n                initial={{ opacity: 0, scale: 0.9 }}\n                animate={{ opacity: 1, scale: 1 }}\n                className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-3xl p-12 text-center shadow-xl shadow-black/5 border border-white/20 dark:border-gray-700/50\"\n              >\n                <div className=\"w-20 h-20 bg-gradient-to-br from-violet-500/20 to-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-6\">\n                  <Check className=\"h-10 w-10 text-violet-500\" />\n                </div>\n                <h3 className=\"text-xl font-bold text-gray-900 dark:text-white mb-3\">\n                  {todos.length === 0 ? '还没有任务' : '没有匹配的任务'}\n                </h3>\n                <p className=\"text-gray-600 dark:text-gray-400 max-w-md mx-auto\">\n                  {todos.length === 0\n                    ? '开始添加您的第一个任务，让生活更有条理！'\n                    : '尝试调整搜索条件或筛选器来找到您要的任务。'\n                  }\n                </p>\n              </motion.div>\n            ) : (\n              <AnimatePresence>\n                {filteredTodos.map((todo, index) => {\n                  const category = categories.find(c => c.id === todo.category);\n                  const isOverdue = todo.dueDate && !todo.completed && todo.dueDate < new Date();\n\n                  return (\n                    <motion.div\n                      key={todo.id}\n                      layout\n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      exit={{ opacity: 0, y: -20 }}\n                      transition={{ delay: index * 0.05 }}\n                      whileHover={{ scale: 1.01, y: -2 }}\n                      className={`group bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-3xl p-6 shadow-xl shadow-black/5 border border-white/20 dark:border-gray-700/50 hover:shadow-2xl transition-all duration-300 ${\n                        todo.completed ? 'opacity-75' : ''\n                      } ${isOverdue ? 'ring-2 ring-red-500/20' : ''}`}\n                    >\n                      <div className=\"flex items-start gap-4\">\n                        {/* Checkbox */}\n                        <motion.button\n                          onClick={() => toggleTodo(todo.id)}\n                          className={`relative w-7 h-7 rounded-2xl border-2 flex items-center justify-center transition-all duration-300 ${\n                            todo.completed\n                              ? 'bg-gradient-to-br from-emerald-500 to-green-500 border-emerald-500 shadow-lg shadow-emerald-500/25'\n                              : 'border-gray-300 dark:border-gray-600 hover:border-violet-500 hover:bg-violet-50 dark:hover:bg-violet-900/20'\n                          }`}\n                          whileHover={{ scale: 1.1 }}\n                          whileTap={{ scale: 0.9 }}\n                        >\n                          <AnimatePresence>\n                            {todo.completed && (\n                              <motion.div\n                                initial={{ scale: 0, rotate: -180 }}\n                                animate={{ scale: 1, rotate: 0 }}\n                                exit={{ scale: 0, rotate: 180 }}\n                                transition={{ duration: 0.3 }}\n                              >\n                                <Check className=\"h-4 w-4 text-white\" />\n                              </motion.div>\n                            )}\n                          </AnimatePresence>\n                        </motion.button>\n\n                        {/* Content */}\n                        <div className=\"flex-1 min-w-0\">\n                          <div className=\"flex items-start justify-between gap-3\">\n                            <div className=\"flex-1\">\n                              <h4 className={`text-lg font-semibold transition-all ${\n                                todo.completed\n                                  ? 'line-through text-gray-500 dark:text-gray-400'\n                                  : 'text-gray-900 dark:text-white'\n                              }`}>\n                                {todo.title}\n                              </h4>\n\n                              {todo.description && (\n                                <p className={`text-sm mt-1 transition-all ${\n                                  todo.completed\n                                    ? 'line-through text-gray-400 dark:text-gray-500'\n                                    : 'text-gray-600 dark:text-gray-400'\n                                }`}>\n                                  {todo.description}\n                                </p>\n                              )}\n                            </div>\n\n                            {/* Important Star */}\n                            <motion.button\n                              onClick={() => toggleImportant(todo.id)}\n                              className={`p-2 rounded-xl transition-all ${\n                                todo.important\n                                  ? 'text-amber-500 bg-amber-50 dark:bg-amber-900/20'\n                                  : 'text-gray-400 hover:text-amber-500 hover:bg-amber-50 dark:hover:bg-amber-900/20'\n                              }`}\n                              whileHover={{ scale: 1.1 }}\n                              whileTap={{ scale: 0.9 }}\n                            >\n                              <Star className={`h-4 w-4 ${todo.important ? 'fill-current' : ''}`} />\n                            </motion.button>\n                          </div>\n\n                          {/* Metadata */}\n                          <div className=\"flex flex-wrap items-center gap-2 mt-3\">\n                            {/* Priority */}\n                            <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${getPriorityColor(todo.priority)}`}>\n                              {todo.priority === 'high' ? '高' : todo.priority === 'medium' ? '中' : '低'}优先级\n                            </span>\n\n                            {/* Category */}\n                            {category && (\n                              <span\n                                className=\"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium text-white shadow-sm\"\n                                style={{ backgroundColor: category.color }}\n                              >\n                                {category.name}\n                              </span>\n                            )}\n\n                            {/* Due Date */}\n                            {todo.dueDate && (\n                              <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium ${\n                                isOverdue\n                                  ? 'text-red-600 bg-red-50 border border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800'\n                                  : 'text-blue-600 bg-blue-50 border border-blue-200 dark:text-blue-400 dark:bg-blue-900/20 dark:border-blue-800'\n                              }`}>\n                                <Calendar className=\"h-3 w-3\" />\n                                {formatDate(todo.dueDate)}\n                              </span>\n                            )}\n\n                            {/* Created Date */}\n                            <span className=\"inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700\">\n                              <Clock className=\"h-3 w-3\" />\n                              {formatDate(todo.createdAt)}\n                            </span>\n                          </div>\n                        </div>\n\n                        {/* Actions */}\n                        <div className=\"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity\">\n                          <motion.button\n                            onClick={() => setEditingTodo(editingTodo === todo.id ? null : todo.id)}\n                            className=\"p-2 rounded-xl text-gray-400 hover:text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all\"\n                            whileHover={{ scale: 1.1 }}\n                            whileTap={{ scale: 0.9 }}\n                          >\n                            <Edit2 className=\"h-4 w-4\" />\n                          </motion.button>\n\n                          <motion.button\n                            onClick={() => deleteTodo(todo.id)}\n                            className=\"p-2 rounded-xl text-gray-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 transition-all\"\n                            whileHover={{ scale: 1.1 }}\n                            whileTap={{ scale: 0.9 }}\n                          >\n                            <Trash2 className=\"h-4 w-4\" />\n                          </motion.button>\n                        </div>\n                      </div>\n                    </motion.div>\n                  );\n                })}\n              </AnimatePresence>\n            )}\n          </motion.div>\n        </div>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"mt-20 border-t border-white/20 dark:border-gray-700/50 bg-white/50 dark:bg-gray-900/50 backdrop-blur-xl\">\n        <div className=\"max-w-6xl mx-auto px-6 py-8\">\n          <div className=\"text-center\">\n            <div className=\"flex items-center justify-center gap-2 mb-4\">\n              <div className=\"w-8 h-8 bg-gradient-to-br from-violet-500 via-purple-500 to-blue-500 rounded-xl flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">✓</span>\n              </div>\n              <span className=\"text-lg font-bold bg-gradient-to-r from-gray-900 via-purple-900 to-violet-900 dark:from-white dark:via-purple-100 dark:to-violet-100 bg-clip-text text-transparent\">\n                TodoMaster\n              </span>\n            </div>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\n              让每一天都充满成就感 ✨\n            </p>\n            <div className=\"flex items-center justify-center gap-6 text-xs text-gray-500 dark:text-gray-500\">\n              <span>Next.js 15</span>\n              <span>•</span>\n              <span>TypeScript</span>\n              <span>•</span>\n              <span>Tailwind CSS</span>\n              <span>•</span>\n              <span>Framer Motion</span>\n            </div>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA;AAtBA;;;;;;AAqDA,MAAM,oBAAgC;IACpC;QAAE,IAAI;QAAK,MAAM;QAAY,OAAO;IAAU;IAC9C;QAAE,IAAI;QAAK,MAAM;QAAQ,OAAO;IAAU;IAC1C;QAAE,IAAI;QAAK,MAAM;QAAY,OAAO;IAAU;IAC9C;QAAE,IAAI;QAAK,MAAM;QAAU,OAAO;IAAU;CAC7C;AAEc,SAAS;IACtB,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,gBAAa,AAAD;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;IAC1C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B;IAC1E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,+BAA+B;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,IAAI,YAAY;YACd,MAAM,cAAc,KAAK,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC,OAAc,CAAC;oBAC7D,GAAG,IAAI;oBACP,WAAW,IAAI,KAAK,KAAK,SAAS;oBAClC,SAAS,KAAK,OAAO,GAAG,IAAI,KAAK,KAAK,OAAO,IAAI;gBACnD,CAAC;YACD,SAAS;QACX;IACF,GAAG,EAAE;IAEL,6BAA6B;IAC7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa,OAAO,CAAC,SAAS,KAAK,SAAS,CAAC;IAC/C,GAAG;QAAC;KAAM;IAEV,MAAM,UAAU;QACd,IAAI,QAAQ,IAAI,IAAI;YAClB,MAAM,OAAa;gBACjB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,OAAO,QAAQ,IAAI;gBACnB,aAAa,eAAe,IAAI,MAAM;gBACtC,WAAW;gBACX,UAAU;gBACV,UAAU;gBACV,MAAM,EAAE;gBACR,SAAS,aAAa,IAAI,KAAK,cAAc;gBAC7C,WAAW,IAAI;gBACf,WAAW;YACb;YACA,SAAS;gBAAC;mBAAS;aAAM;YACzB,WAAW;YACX,kBAAkB;YAClB,eAAe;YACf,eAAe;YACf,cAAc;YACd,gBAAgB;QAClB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,SAAS,MAAM,GAAG,CAAC,CAAA,OACjB,KAAK,EAAE,KAAK,KAAK;gBAAE,GAAG,IAAI;gBAAE,WAAW,CAAC,KAAK,SAAS;YAAC,IAAI;IAE/D;IAEA,MAAM,kBAAkB,CAAC;QACvB,SAAS,MAAM,GAAG,CAAC,CAAA,OACjB,KAAK,EAAE,KAAK,KAAK;gBAAE,GAAG,IAAI;gBAAE,WAAW,CAAC,KAAK,SAAS;YAAC,IAAI;IAE/D;IAEA,MAAM,aAAa,CAAC;QAClB,SAAS,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAC5C;IAEA,MAAM,aAAa,CAAC,IAAY;QAC9B,SAAS,MAAM,GAAG,CAAC,CAAA,OACjB,KAAK,EAAE,KAAK,KAAK;gBAAE,GAAG,IAAI;gBAAE,GAAG,OAAO;YAAC,IAAI;IAE/C;IAEA,0BAA0B;IAC1B,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA;QACjC,MAAM,gBAAgB,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,KAAK,WAAW,EAAE,cAAc,SAAS,WAAW,WAAW;QACpF,MAAM,kBAAkB,CAAC,kBAAkB,KAAK,QAAQ,KAAK;QAC7D,MAAM,kBAAkB,CAAC,kBAAkB,KAAK,QAAQ,KAAK;QAC7D,MAAM,mBAAmB,iBAAiB,CAAC,KAAK,SAAS;QAEzD,OAAO,iBAAiB,mBAAmB,mBAAmB;IAChE;IAEA,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,EAAE,MAAM;IAClE,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,IAAI,CAAC,KAAK,SAAS,EAAE,MAAM;IACrF,MAAM,eAAe,MAAM,MAAM,CAAC,CAAA,OAChC,CAAC,KAAK,SAAS,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,GAAG,IAAI,QACtD,MAAM;IAER,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,QAAQ,IAAI;QAClB,MAAM,WAAW,IAAI,KAAK;QAC1B,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;QAEtC,IAAI,KAAK,YAAY,OAAO,MAAM,YAAY,IAAI,OAAO;QACzD,IAAI,KAAK,YAAY,OAAO,SAAS,YAAY,IAAI,OAAO;QAC5D,OAAO,KAAK,kBAAkB;IAChC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,8OAAC;gDAAI,WAAU;;;;;;;;;;;;kDAEjB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAsK;;;;;;0DAGpL,8OAAC;gDAAE,WAAU;0DAAuD;;;;;;;;;;;;;;;;;;0CAMxE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAAC,WAAU;;0DACpB,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;;oDACb,MAAM,MAAM;oDAAC;;;;;;;;;;;;;kDAIlB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,SAAS;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;4CAAM,QAAQ;wCAAI;wCACvC,UAAU;4CAAE,OAAO;wCAAK;kDAExB,cAAA,8OAAC,yLAAA,CAAA,kBAAe;4CAAC,MAAK;sDACnB,UAAU,uBACT,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,QAAQ,CAAC;gDAAI;gDACpC,SAAS;oDAAE,SAAS;oDAAG,QAAQ;gDAAE;gDACjC,MAAM;oDAAE,SAAS;oDAAG,QAAQ;gDAAI;gDAChC,YAAY;oDAAE,UAAU;gDAAI;0DAE5B,cAAA,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;+CANX;;;;qEASN,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,QAAQ,CAAC;gDAAI;gDACpC,SAAS;oDAAE,SAAS;oDAAG,QAAQ;gDAAE;gDACjC,MAAM;oDAAE,SAAS;oDAAG,QAAQ;gDAAI;gDAChC,YAAY;oDAAE,UAAU;gDAAI;0DAE5B,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;+CANZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAiBpB,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,WAAU;;8CAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,YAAY;wCAAE,OAAO;wCAAM,GAAG,CAAC;oCAAE;oCACjC,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,8OAAC;wDAAE,WAAU;kEAAoD,MAAM,MAAM;;;;;;;;;;;;0DAE/E,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAMvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,YAAY;wCAAE,OAAO;wCAAM,GAAG,CAAC;oCAAE;oCACjC,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,8OAAC;wDAAE,WAAU;kEAAuC;;;;;;;;;;;;0DAEtD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAMvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,YAAY;wCAAE,OAAO;wCAAM,GAAG,CAAC;oCAAE;oCACjC,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,8OAAC;wDAAE,WAAU;kEAAqC;;;;;;;;;;;;0DAEpD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAMtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,YAAY;wCAAE,OAAO;wCAAM,GAAG,CAAC;oCAAE;oCACjC,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,8OAAC;wDAAE,WAAU;kEAAmC;;;;;;;;;;;;0DAElD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO/B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;4BACzB,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsD;;;;;;sDACpE,8OAAC;4CAAK,WAAU;sDACb,MAAM,MAAM,GAAG,IAAI,GAAG,KAAK,KAAK,CAAC,AAAC,iBAAiB,MAAM,MAAM,GAAI,KAAK,CAAC,CAAC,GAAG;;;;;;;;;;;;8CAGlF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,OAAO;4CAAE;4CACpB,SAAS;gDAAE,OAAO,MAAM,MAAM,GAAG,IAAI,GAAG,AAAC,iBAAiB,MAAM,MAAM,GAAI,IAAI,CAAC,CAAC,GAAG;4CAAK;4CACxF,YAAY;gDAAE,UAAU;gDAAG,MAAM;4CAAU;;;;;;sDAE7C,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;sCAKnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;4BACzB,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,aAAY;oDACZ,WAAU;;;;;;;;;;;;sDAKd,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4CACjD,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;wDAAyB,OAAO,SAAS,EAAE;kEACzC,SAAS,IAAI;uDADH,SAAS,EAAE;;;;;;;;;;;sDAO5B,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4CACjD,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAG;;;;;;8DACjB,8OAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,8OAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,8OAAC;oDAAO,OAAM;8DAAM;;;;;;;;;;;;;;;;;;8CAIxB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,SAAS,IAAM,iBAAiB,CAAC;4CACjC,WAAW,CAAC,yDAAyD,EACnE,gBACI,4DACA,iEACJ;4CACF,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;sDAEvB,gBAAgB,UAAU;;;;;;sDAG7B,8OAAC;4CAAI,WAAU;;gDAA2C;gDACpD,cAAc,MAAM;gDAAC;;;;;;;;;;;;;;;;;;;sCAM/B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;4BACzB,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,8OAAC;gDAAG,WAAU;0DAAsD;;;;;;;;;;;;kDAGtE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;gDAC1C,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,IAAI;gDACvD,aAAY;gDACZ,WAAU;;;;;;0DAGZ,8OAAC,yLAAA,CAAA,kBAAe;0DACb,8BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,QAAQ;wDAAG,SAAS;oDAAE;oDACjC,SAAS;wDAAE,QAAQ;wDAAQ,SAAS;oDAAE;oDACtC,MAAM;wDAAE,QAAQ;wDAAG,SAAS;oDAAE;oDAC9B,YAAY;wDAAE,UAAU;oDAAI;oDAC5B,WAAU;;sEAEV,8OAAC;4DACC,OAAO;4DACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4DACjD,aAAY;4DACZ,MAAM;4DACN,WAAU;;;;;;sEAGZ,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEACC,OAAO;oEACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oEAC9C,WAAU;;sFAEV,8OAAC;4EAAO,OAAM;sFAAM;;;;;;sFACpB,8OAAC;4EAAO,OAAM;sFAAS;;;;;;sFACvB,8OAAC;4EAAO,OAAM;sFAAO;;;;;;;;;;;;8EAGvB,8OAAC;oEACC,OAAO;oEACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oEAC9C,WAAU;;sFAEV,8OAAC;4EAAO,OAAM;sFAAG;;;;;;wEAChB,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;gFAAyB,OAAO,SAAS,EAAE;0FACzC,SAAS,IAAI;+EADH,SAAS,EAAE;;;;;;;;;;;8EAM5B,8OAAC;oEACC,MAAK;oEACL,OAAO;oEACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oEAC7C,WAAU;;;;;;;;;;;;;;;;;;;;;;;0DAOpB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wDACZ,SAAS,IAAM,gBAAgB,CAAC;wDAChC,WAAU;wDACV,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,UAAU;4DAAE,OAAO;wDAAK;kEAEvB,eAAe,SAAS;;;;;;kEAG3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wDACZ,SAAS;wDACT,UAAU,CAAC,QAAQ,IAAI;wDACvB,WAAU;wDACV,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,UAAU;4DAAE,OAAO;wDAAK;kEAExB,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUxC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;4BACzB,WAAU;sCAET,cAAc,MAAM,KAAK,kBACxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC;wCAAG,WAAU;kDACX,MAAM,MAAM,KAAK,IAAI,UAAU;;;;;;kDAElC,8OAAC;wCAAE,WAAU;kDACV,MAAM,MAAM,KAAK,IACd,yBACA;;;;;;;;;;;qDAKR,8OAAC,yLAAA,CAAA,kBAAe;0CACb,cAAc,GAAG,CAAC,CAAC,MAAM;oCACxB,MAAM,WAAW,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,QAAQ;oCAC5D,MAAM,YAAY,KAAK,OAAO,IAAI,CAAC,KAAK,SAAS,IAAI,KAAK,OAAO,GAAG,IAAI;oCAExE,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,MAAM;wCACN,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,MAAM;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC3B,YAAY;4CAAE,OAAO,QAAQ;wCAAK;wCAClC,YAAY;4CAAE,OAAO;4CAAM,GAAG,CAAC;wCAAE;wCACjC,WAAW,CAAC,4LAA4L,EACtM,KAAK,SAAS,GAAG,eAAe,GACjC,CAAC,EAAE,YAAY,2BAA2B,IAAI;kDAE/C,cAAA,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oDACZ,SAAS,IAAM,WAAW,KAAK,EAAE;oDACjC,WAAW,CAAC,mGAAmG,EAC7G,KAAK,SAAS,GACV,uGACA,+GACJ;oDACF,YAAY;wDAAE,OAAO;oDAAI;oDACzB,UAAU;wDAAE,OAAO;oDAAI;8DAEvB,cAAA,8OAAC,yLAAA,CAAA,kBAAe;kEACb,KAAK,SAAS,kBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,SAAS;gEAAE,OAAO;gEAAG,QAAQ,CAAC;4DAAI;4DAClC,SAAS;gEAAE,OAAO;gEAAG,QAAQ;4DAAE;4DAC/B,MAAM;gEAAE,OAAO;gEAAG,QAAQ;4DAAI;4DAC9B,YAAY;gEAAE,UAAU;4DAAI;sEAE5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;8DAOzB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAW,CAAC,qCAAqC,EACnD,KAAK,SAAS,GACV,kDACA,iCACJ;sFACC,KAAK,KAAK;;;;;;wEAGZ,KAAK,WAAW,kBACf,8OAAC;4EAAE,WAAW,CAAC,4BAA4B,EACzC,KAAK,SAAS,GACV,kDACA,oCACJ;sFACC,KAAK,WAAW;;;;;;;;;;;;8EAMvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oEACZ,SAAS,IAAM,gBAAgB,KAAK,EAAE;oEACtC,WAAW,CAAC,8BAA8B,EACxC,KAAK,SAAS,GACV,oDACA,mFACJ;oEACF,YAAY;wEAAE,OAAO;oEAAI;oEACzB,UAAU;wEAAE,OAAO;oEAAI;8EAEvB,cAAA,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,SAAS,GAAG,iBAAiB,IAAI;;;;;;;;;;;;;;;;;sEAKtE,8OAAC;4DAAI,WAAU;;8EAEb,8OAAC;oEAAK,WAAW,CAAC,2EAA2E,EAAE,iBAAiB,KAAK,QAAQ,GAAG;;wEAC7H,KAAK,QAAQ,KAAK,SAAS,MAAM,KAAK,QAAQ,KAAK,WAAW,MAAM;wEAAI;;;;;;;gEAI1E,0BACC,8OAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,iBAAiB,SAAS,KAAK;oEAAC;8EAExC,SAAS,IAAI;;;;;;gEAKjB,KAAK,OAAO,kBACX,8OAAC;oEAAK,WAAW,CAAC,0EAA0E,EAC1F,YACI,0GACA,+GACJ;;sFACA,8OAAC,0MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;wEACnB,WAAW,KAAK,OAAO;;;;;;;8EAK5B,8OAAC;oEAAK,WAAU;;sFACd,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAChB,WAAW,KAAK,SAAS;;;;;;;;;;;;;;;;;;;8DAMhC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4DACZ,SAAS,IAAM,eAAe,gBAAgB,KAAK,EAAE,GAAG,OAAO,KAAK,EAAE;4DACtE,WAAU;4DACV,YAAY;gEAAE,OAAO;4DAAI;4DACzB,UAAU;gEAAE,OAAO;4DAAI;sEAEvB,cAAA,8OAAC,kMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;sEAGnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4DACZ,SAAS,IAAM,WAAW,KAAK,EAAE;4DACjC,WAAU;4DACV,YAAY;gEAAE,OAAO;4DAAI;4DACzB,UAAU;gEAAE,OAAO;4DAAI;sEAEvB,cAAA,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;uCAjInB,KAAK,EAAE;;;;;gCAuIlB;;;;;;;;;;;;;;;;;;;;;;0BAQV,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,8OAAC;wCAAK,WAAU;kDAAqK;;;;;;;;;;;;0CAIvL,8OAAC;gCAAE,WAAU;0CAAgD;;;;;;0CAG7D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAK;;;;;;kDACN,8OAAC;kDAAK;;;;;;kDACN,8OAAC;kDAAK;;;;;;kDACN,8OAAC;kDAAK;;;;;;kDACN,8OAAC;kDAAK;;;;;;kDACN,8OAAC;kDAAK;;;;;;kDACN,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB", "debugId": null}}]}