'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Plus,
  Moon,
  Sun,
  Check,
  Trash2,
  Edit2,
  Calendar,
  Tag,
  Filter,
  Search,
  Star,
  Clock,
  AlertCircle,
  CheckCircle2,
  Circle,
  MoreHorizontal
} from 'lucide-react';
import { useThemeStore } from '@/store/themeStore';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';

interface Todo {
  id: string;
  title: string;
  description?: string;
  completed: boolean;
  priority: 'low' | 'medium' | 'high';
  category: string;
  tags: string[];
  dueDate?: Date;
  createdAt: Date;
  important: boolean;
}

interface Category {
  id: string;
  name: string;
  color: string;
}

const defaultCategories: Category[] = [
  { id: '1', name: 'Personal', color: '#3B82F6' },
  { id: '2', name: 'Work', color: '#EF4444' },
  { id: '3', name: 'Shopping', color: '#10B981' },
  { id: '4', name: 'Health', color: '#F59E0B' },
];

export default function Home() {
  const { theme, toggleTheme } = useThemeStore();
  const [todos, setTodos] = useState<Todo[]>([]);
  const [categories] = useState<Category[]>(defaultCategories);
  const [newTodo, setNewTodo] = useState('');
  const [newDescription, setNewDescription] = useState('');
  const [newPriority, setNewPriority] = useState<'low' | 'medium' | 'high'>('medium');
  const [newCategory, setNewCategory] = useState('');
  const [newDueDate, setNewDueDate] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('');
  const [filterPriority, setFilterPriority] = useState('');
  const [showCompleted, setShowCompleted] = useState(true);
  const [isAddingTodo, setIsAddingTodo] = useState(false);
  const [editingTodo, setEditingTodo] = useState<string | null>(null);

  // Load todos from localStorage
  useEffect(() => {
    const savedTodos = localStorage.getItem('todos');
    if (savedTodos) {
      const parsedTodos = JSON.parse(savedTodos).map((todo: any) => ({
        ...todo,
        createdAt: new Date(todo.createdAt),
        dueDate: todo.dueDate ? new Date(todo.dueDate) : undefined,
      }));
      setTodos(parsedTodos);
    }
  }, []);

  // Save todos to localStorage
  useEffect(() => {
    localStorage.setItem('todos', JSON.stringify(todos));
  }, [todos]);

  const addTodo = () => {
    if (newTodo.trim()) {
      const todo: Todo = {
        id: Date.now().toString(),
        title: newTodo.trim(),
        description: newDescription.trim() || undefined,
        completed: false,
        priority: newPriority,
        category: newCategory,
        tags: [],
        dueDate: newDueDate ? new Date(newDueDate) : undefined,
        createdAt: new Date(),
        important: false,
      };
      setTodos([todo, ...todos]);
      setNewTodo('');
      setNewDescription('');
      setNewPriority('medium');
      setNewCategory('');
      setNewDueDate('');
      setIsAddingTodo(false);
    }
  };

  const toggleTodo = (id: string) => {
    setTodos(todos.map(todo =>
      todo.id === id ? { ...todo, completed: !todo.completed } : todo
    ));
  };

  const toggleImportant = (id: string) => {
    setTodos(todos.map(todo =>
      todo.id === id ? { ...todo, important: !todo.important } : todo
    ));
  };

  const deleteTodo = (id: string) => {
    setTodos(todos.filter(todo => todo.id !== id));
  };

  const updateTodo = (id: string, updates: Partial<Todo>) => {
    setTodos(todos.map(todo =>
      todo.id === id ? { ...todo, ...updates } : todo
    ));
  };

  // Filter and search todos
  const filteredTodos = todos.filter(todo => {
    const matchesSearch = todo.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         todo.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = !filterCategory || todo.category === filterCategory;
    const matchesPriority = !filterPriority || todo.priority === filterPriority;
    const matchesCompleted = showCompleted || !todo.completed;

    return matchesSearch && matchesCategory && matchesPriority && matchesCompleted;
  });

  const completedCount = todos.filter(todo => todo.completed).length;
  const importantCount = todos.filter(todo => todo.important && !todo.completed).length;
  const overdueCount = todos.filter(todo =>
    !todo.completed && todo.dueDate && todo.dueDate < new Date()
  ).length;

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:text-yellow-400 dark:bg-yellow-900/20 dark:border-yellow-800';
      case 'low': return 'text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800';
      default: return 'text-gray-600 bg-gray-50 border-gray-200 dark:text-gray-400 dark:bg-gray-800/20 dark:border-gray-700';
    }
  };

  const formatDate = (date: Date) => {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    if (date.toDateString() === today.toDateString()) return 'Today';
    if (date.toDateString() === tomorrow.toDateString()) return 'Tomorrow';
    return date.toLocaleDateString();
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="flex items-center gap-3"
            >
              <div className="relative">
                <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center shadow-lg">
                  <CheckCircle2 className="h-6 w-6 text-primary-foreground" />
                </div>
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-foreground">TodoMaster</h1>
                <p className="text-sm text-muted-foreground">让生活更有序，让梦想更清晰</p>
              </div>
            </motion.div>

            <div className="flex items-center gap-3">
              <Badge variant="secondary" className="hidden md:flex">
                {todos.length} 个任务
              </Badge>

              <Button
                variant="outline"
                size="icon"
                onClick={toggleTheme}
                className="relative"
              >
                <AnimatePresence mode="wait">
                  {theme === 'dark' ? (
                    <motion.div
                      key="sun"
                      initial={{ opacity: 0, rotate: -90 }}
                      animate={{ opacity: 1, rotate: 0 }}
                      exit={{ opacity: 0, rotate: 90 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Sun className="h-4 w-4" />
                    </motion.div>
                  ) : (
                    <motion.div
                      key="moon"
                      initial={{ opacity: 0, rotate: -90 }}
                      animate={{ opacity: 1, rotate: 0 }}
                      exit={{ opacity: 0, rotate: 90 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Moon className="h-4 w-4" />
                    </motion.div>
                  )}
                </AnimatePresence>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="space-y-6">
          {/* Stats Dashboard */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
          >
            {/* Total Tasks */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">总任务</p>
                    <p className="text-2xl font-bold">{todos.length}</p>
                  </div>
                  <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                    <Clock className="h-4 w-4 text-primary-foreground" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Completed Tasks */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">已完成</p>
                    <p className="text-2xl font-bold text-green-600">{completedCount}</p>
                  </div>
                  <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                    <CheckCircle2 className="h-4 w-4 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Important Tasks */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">重要任务</p>
                    <p className="text-2xl font-bold text-amber-600">{importantCount}</p>
                  </div>
                  <div className="w-8 h-8 bg-amber-500 rounded-lg flex items-center justify-center">
                    <Star className="h-4 w-4 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Overdue Tasks */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">已逾期</p>
                    <p className="text-2xl font-bold text-red-600">{overdueCount}</p>
                  </div>
                  <div className="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center">
                    <AlertCircle className="h-4 w-4 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Progress Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>今日进度</CardTitle>
                  <Badge variant="secondary">
                    {todos.length > 0 ? `${Math.round((completedCount / todos.length) * 100)}%` : '0%'}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <Progress
                  value={todos.length > 0 ? (completedCount / todos.length) * 100 : 0}
                  className="w-full"
                />
                <p className="text-sm text-muted-foreground mt-2">
                  {completedCount} / {todos.length} 任务已完成
                </p>
              </CardContent>
            </Card>
          </motion.div>

          {/* Search and Filters */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Filter className="h-5 w-5" />
                  搜索和筛选
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* Search */}
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="text"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      placeholder="搜索任务..."
                      className="pl-10"
                    />
                  </div>

                  {/* Category Filter */}
                  <Select value={filterCategory} onValueChange={setFilterCategory}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择分类" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">所有分类</SelectItem>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  {/* Priority Filter */}
                  <Select value={filterPriority} onValueChange={setFilterPriority}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择优先级" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">所有优先级</SelectItem>
                      <SelectItem value="high">高优先级</SelectItem>
                      <SelectItem value="medium">中优先级</SelectItem>
                      <SelectItem value="low">低优先级</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-between">
                  <Button
                    variant={showCompleted ? "default" : "outline"}
                    onClick={() => setShowCompleted(!showCompleted)}
                    size="sm"
                  >
                    {showCompleted ? '隐藏已完成' : '显示已完成'}
                  </Button>

                  <Badge variant="secondary">
                    找到 {filteredTodos.length} 个任务
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Add Todo */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Plus className="h-5 w-5" />
                  添加新任务
                </CardTitle>
                <CardDescription>
                  创建一个新的任务来保持高效和有序
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Input
                  type="text"
                  value={newTodo}
                  onChange={(e) => setNewTodo(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && !e.shiftKey && addTodo()}
                  placeholder="输入任务标题..."
                />

                <AnimatePresence>
                  {isAddingTodo && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="space-y-4"
                    >
                      <Textarea
                        value={newDescription}
                        onChange={(e) => setNewDescription(e.target.value)}
                        placeholder="任务描述（可选）..."
                        rows={3}
                      />

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Select value={newPriority} onValueChange={(value: 'low' | 'medium' | 'high') => setNewPriority(value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="选择优先级" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="low">低优先级</SelectItem>
                            <SelectItem value="medium">中优先级</SelectItem>
                            <SelectItem value="high">高优先级</SelectItem>
                          </SelectContent>
                        </Select>

                        <Select value={newCategory} onValueChange={setNewCategory}>
                          <SelectTrigger>
                            <SelectValue placeholder="选择分类" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="">无分类</SelectItem>
                            {categories.map((category) => (
                              <SelectItem key={category.id} value={category.id}>
                                {category.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>

                        <Input
                          type="datetime-local"
                          value={newDueDate}
                          onChange={(e) => setNewDueDate(e.target.value)}
                        />
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>

                <div className="flex items-center gap-3">
                  <Button
                    variant="outline"
                    onClick={() => setIsAddingTodo(!isAddingTodo)}
                    size="sm"
                  >
                    {isAddingTodo ? '简单模式' : '详细模式'}
                  </Button>

                  <Button
                    onClick={addTodo}
                    disabled={!newTodo.trim()}
                    className="flex-1"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    添加任务
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Todo List */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="space-y-4"
          >
            {filteredTodos.length === 0 ? (
              <Card>
                <CardContent className="p-12 text-center">
                  <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                    <CheckCircle2 className="h-8 w-8 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">
                    {todos.length === 0 ? '还没有任务' : '没有匹配的任务'}
                  </h3>
                  <p className="text-muted-foreground max-w-md mx-auto">
                    {todos.length === 0
                      ? '开始添加您的第一个任务，让生活更有条理！'
                      : '尝试调整搜索条件或筛选器来找到您要的任务。'
                    }
                  </p>
                </CardContent>
              </Card>
            ) : (
              <AnimatePresence>
                {filteredTodos.map((todo, index) => {
                  const category = categories.find(c => c.id === todo.category);
                  const isOverdue = todo.dueDate && !todo.completed && todo.dueDate < new Date();

                  return (
                    <motion.div
                      key={todo.id}
                      layout
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ delay: index * 0.05 }}
                    >
                      <Card className={`group hover:shadow-md transition-all duration-200 ${
                        todo.completed ? 'opacity-75' : ''
                      } ${isOverdue ? 'border-red-200 dark:border-red-800' : ''}`}>
                        <CardContent className="p-6">
                          <div className="flex items-start gap-4">
                            {/* Checkbox */}
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => toggleTodo(todo.id)}
                              className={`w-6 h-6 rounded-full p-0 ${
                                todo.completed
                                  ? 'bg-green-500 hover:bg-green-600 text-white'
                                  : 'border-2 border-muted-foreground hover:border-primary'
                              }`}
                            >
                              <AnimatePresence>
                                {todo.completed ? (
                                  <motion.div
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1 }}
                                    exit={{ scale: 0 }}
                                  >
                                    <Check className="h-3 w-3" />
                                  </motion.div>
                                ) : (
                                  <Circle className="h-3 w-3" />
                                )}
                              </AnimatePresence>
                            </Button>

                            {/* Content */}
                            <div className="flex-1 min-w-0">
                              <div className="flex items-start justify-between gap-3">
                                <div className="flex-1">
                                  <h4 className={`font-semibold ${
                                    todo.completed ? 'line-through text-muted-foreground' : ''
                                  }`}>
                                    {todo.title}
                                  </h4>

                                  {todo.description && (
                                    <p className={`text-sm mt-1 ${
                                      todo.completed ? 'line-through text-muted-foreground' : 'text-muted-foreground'
                                    }`}>
                                      {todo.description}
                                    </p>
                                  )}
                                </div>

                                <div className="flex items-center gap-1">
                                  {/* Important Star */}
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => toggleImportant(todo.id)}
                                    className={`w-8 h-8 ${
                                      todo.important ? 'text-amber-500 hover:text-amber-600' : 'text-muted-foreground'
                                    }`}
                                  >
                                    <Star className={`h-4 w-4 ${todo.important ? 'fill-current' : ''}`} />
                                  </Button>

                                  {/* Actions Menu */}
                                  <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                      <Button variant="ghost" size="icon" className="w-8 h-8 opacity-0 group-hover:opacity-100 transition-opacity">
                                        <MoreHorizontal className="h-4 w-4" />
                                      </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                      <DropdownMenuItem onClick={() => setEditingTodo(editingTodo === todo.id ? null : todo.id)}>
                                        <Edit2 className="h-4 w-4 mr-2" />
                                        编辑
                                      </DropdownMenuItem>
                                      <DropdownMenuItem onClick={() => deleteTodo(todo.id)} className="text-red-600">
                                        <Trash2 className="h-4 w-4 mr-2" />
                                        删除
                                      </DropdownMenuItem>
                                    </DropdownMenuContent>
                                  </DropdownMenu>
                                </div>
                              </div>

                              {/* Metadata */}
                              <div className="flex flex-wrap items-center gap-2 mt-3">
                                {/* Priority */}
                                <Badge variant={
                                  todo.priority === 'high' ? 'destructive' :
                                  todo.priority === 'medium' ? 'default' : 'secondary'
                                }>
                                  {todo.priority === 'high' ? '高' : todo.priority === 'medium' ? '中' : '低'}优先级
                                </Badge>

                                {/* Category */}
                                {category && (
                                  <Badge variant="outline" style={{ borderColor: category.color, color: category.color }}>
                                    {category.name}
                                  </Badge>
                                )}

                                {/* Due Date */}
                                {todo.dueDate && (
                                  <Badge variant={isOverdue ? 'destructive' : 'outline'}>
                                    <Calendar className="h-3 w-3 mr-1" />
                                    {formatDate(todo.dueDate)}
                                  </Badge>
                                )}

                                {/* Created Date */}
                                <Badge variant="secondary">
                                  <Clock className="h-3 w-3 mr-1" />
                                  {formatDate(todo.createdAt)}
                                </Badge>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  );
                })}
              </AnimatePresence>
            )}
          </motion.div>
        </div>
      </main>

      {/* Footer */}
      <footer className="mt-16 border-t bg-muted/50">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <div className="flex items-center justify-center gap-2 mb-4">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <CheckCircle2 className="h-4 w-4 text-primary-foreground" />
              </div>
              <span className="text-lg font-bold">TodoMaster</span>
            </div>
            <p className="text-sm text-muted-foreground mb-4">
              让每一天都充满成就感 ✨
            </p>
            <div className="flex items-center justify-center gap-4 text-xs text-muted-foreground">
              <span>Next.js 15</span>
              <Separator orientation="vertical" className="h-3" />
              <span>TypeScript</span>
              <Separator orientation="vertical" className="h-3" />
              <span>shadcn/ui</span>
              <Separator orientation="vertical" className="h-3" />
              <span>Framer Motion</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
