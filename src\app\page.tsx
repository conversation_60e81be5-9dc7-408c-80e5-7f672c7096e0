'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Plus,
  Moon,
  Sun,
  Check,
  Trash2,
  Edit2,
  Calendar,
  Tag,
  Filter,
  Search,
  Star,
  Clock,
  AlertCircle
} from 'lucide-react';
import { useThemeStore } from '@/store/themeStore';

interface Todo {
  id: string;
  title: string;
  description?: string;
  completed: boolean;
  priority: 'low' | 'medium' | 'high';
  category: string;
  tags: string[];
  dueDate?: Date;
  createdAt: Date;
  important: boolean;
}

interface Category {
  id: string;
  name: string;
  color: string;
}

const defaultCategories: Category[] = [
  { id: '1', name: 'Personal', color: '#3B82F6' },
  { id: '2', name: 'Work', color: '#EF4444' },
  { id: '3', name: 'Shopping', color: '#10B981' },
  { id: '4', name: 'Health', color: '#F59E0B' },
];

export default function Home() {
  const { theme, toggleTheme } = useThemeStore();
  const [todos, setTodos] = useState<Todo[]>([]);
  const [categories] = useState<Category[]>(defaultCategories);
  const [newTodo, setNewTodo] = useState('');
  const [newDescription, setNewDescription] = useState('');
  const [newPriority, setNewPriority] = useState<'low' | 'medium' | 'high'>('medium');
  const [newCategory, setNewCategory] = useState('');
  const [newDueDate, setNewDueDate] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('');
  const [filterPriority, setFilterPriority] = useState('');
  const [showCompleted, setShowCompleted] = useState(true);
  const [isAddingTodo, setIsAddingTodo] = useState(false);
  const [editingTodo, setEditingTodo] = useState<string | null>(null);

  // Load todos from localStorage
  useEffect(() => {
    const savedTodos = localStorage.getItem('todos');
    if (savedTodos) {
      const parsedTodos = JSON.parse(savedTodos).map((todo: any) => ({
        ...todo,
        createdAt: new Date(todo.createdAt),
        dueDate: todo.dueDate ? new Date(todo.dueDate) : undefined,
      }));
      setTodos(parsedTodos);
    }
  }, []);

  // Save todos to localStorage
  useEffect(() => {
    localStorage.setItem('todos', JSON.stringify(todos));
  }, [todos]);

  const addTodo = () => {
    if (newTodo.trim()) {
      const todo: Todo = {
        id: Date.now().toString(),
        title: newTodo.trim(),
        description: newDescription.trim() || undefined,
        completed: false,
        priority: newPriority,
        category: newCategory,
        tags: [],
        dueDate: newDueDate ? new Date(newDueDate) : undefined,
        createdAt: new Date(),
        important: false,
      };
      setTodos([todo, ...todos]);
      setNewTodo('');
      setNewDescription('');
      setNewPriority('medium');
      setNewCategory('');
      setNewDueDate('');
      setIsAddingTodo(false);
    }
  };

  const toggleTodo = (id: string) => {
    setTodos(todos.map(todo =>
      todo.id === id ? { ...todo, completed: !todo.completed } : todo
    ));
  };

  const toggleImportant = (id: string) => {
    setTodos(todos.map(todo =>
      todo.id === id ? { ...todo, important: !todo.important } : todo
    ));
  };

  const deleteTodo = (id: string) => {
    setTodos(todos.filter(todo => todo.id !== id));
  };

  const updateTodo = (id: string, updates: Partial<Todo>) => {
    setTodos(todos.map(todo =>
      todo.id === id ? { ...todo, ...updates } : todo
    ));
  };

  // Filter and search todos
  const filteredTodos = todos.filter(todo => {
    const matchesSearch = todo.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         todo.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = !filterCategory || todo.category === filterCategory;
    const matchesPriority = !filterPriority || todo.priority === filterPriority;
    const matchesCompleted = showCompleted || !todo.completed;

    return matchesSearch && matchesCategory && matchesPriority && matchesCompleted;
  });

  const completedCount = todos.filter(todo => todo.completed).length;
  const importantCount = todos.filter(todo => todo.important && !todo.completed).length;
  const overdueCount = todos.filter(todo =>
    !todo.completed && todo.dueDate && todo.dueDate < new Date()
  ).length;

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:text-yellow-400 dark:bg-yellow-900/20 dark:border-yellow-800';
      case 'low': return 'text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800';
      default: return 'text-gray-600 bg-gray-50 border-gray-200 dark:text-gray-400 dark:bg-gray-800/20 dark:border-gray-700';
    }
  };

  const formatDate = (date: Date) => {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    if (date.toDateString() === today.toDateString()) return 'Today';
    if (date.toDateString() === tomorrow.toDateString()) return 'Tomorrow';
    return date.toLocaleDateString();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-blue-900/20 dark:to-indigo-900/20 transition-all duration-500">
      {/* Header */}
      <header className="backdrop-blur-xl bg-white/70 dark:bg-gray-900/70 border-b border-white/20 dark:border-gray-700/50 sticky top-0 z-50 shadow-lg shadow-black/5">
        <div className="max-w-6xl mx-auto px-6 py-6">
          <div className="flex items-center justify-between">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="flex items-center gap-4"
            >
              <div className="relative">
                <div className="w-12 h-12 bg-gradient-to-br from-violet-500 via-purple-500 to-blue-500 rounded-2xl flex items-center justify-center shadow-xl shadow-purple-500/25">
                  <span className="text-white font-bold text-xl">✓</span>
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full animate-pulse"></div>
              </div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 via-purple-900 to-violet-900 dark:from-white dark:via-purple-100 dark:to-violet-100 bg-clip-text text-transparent">
                  TodoMaster
                </h1>
                <p className="text-sm text-gray-600 dark:text-gray-400 font-medium">
                  ✨ 让生活更有序，让梦想更清晰
                </p>
              </div>
            </motion.div>

            <div className="flex items-center gap-3">
              <motion.div className="hidden md:flex items-center gap-2 px-4 py-2 bg-white/50 dark:bg-gray-800/50 rounded-full backdrop-blur-sm border border-white/20 dark:border-gray-700/50">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {todos.length} 个任务
                </span>
              </motion.div>

              <motion.button
                onClick={toggleTheme}
                className="p-3 rounded-2xl bg-white/80 dark:bg-gray-800/80 hover:bg-white dark:hover:bg-gray-700 transition-all duration-300 shadow-lg shadow-black/5 border border-white/20 dark:border-gray-700/50 backdrop-blur-sm"
                whileHover={{ scale: 1.05, rotate: 180 }}
                whileTap={{ scale: 0.95 }}
              >
                <AnimatePresence mode="wait">
                  {theme === 'dark' ? (
                    <motion.div
                      key="sun"
                      initial={{ opacity: 0, rotate: -180 }}
                      animate={{ opacity: 1, rotate: 0 }}
                      exit={{ opacity: 0, rotate: 180 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Sun className="h-5 w-5 text-amber-500" />
                    </motion.div>
                  ) : (
                    <motion.div
                      key="moon"
                      initial={{ opacity: 0, rotate: -180 }}
                      animate={{ opacity: 1, rotate: 0 }}
                      exit={{ opacity: 0, rotate: 180 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Moon className="h-5 w-5 text-slate-700" />
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto px-6 py-8">
        <div className="space-y-8">
          {/* Stats Dashboard */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="grid grid-cols-1 md:grid-cols-4 gap-6"
          >
            {/* Total Tasks */}
            <motion.div
              whileHover={{ scale: 1.02, y: -2 }}
              className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-3xl p-6 shadow-xl shadow-black/5 border border-white/20 dark:border-gray-700/50"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">总任务</p>
                  <p className="text-3xl font-bold text-gray-900 dark:text-white">{todos.length}</p>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center shadow-lg shadow-blue-500/25">
                  <Clock className="h-6 w-6 text-white" />
                </div>
              </div>
            </motion.div>

            {/* Completed Tasks */}
            <motion.div
              whileHover={{ scale: 1.02, y: -2 }}
              className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-3xl p-6 shadow-xl shadow-black/5 border border-white/20 dark:border-gray-700/50"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">已完成</p>
                  <p className="text-3xl font-bold text-emerald-600">{completedCount}</p>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-green-500 rounded-2xl flex items-center justify-center shadow-lg shadow-emerald-500/25">
                  <Check className="h-6 w-6 text-white" />
                </div>
              </div>
            </motion.div>

            {/* Important Tasks */}
            <motion.div
              whileHover={{ scale: 1.02, y: -2 }}
              className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-3xl p-6 shadow-xl shadow-black/5 border border-white/20 dark:border-gray-700/50"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">重要任务</p>
                  <p className="text-3xl font-bold text-amber-600">{importantCount}</p>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-amber-500 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg shadow-amber-500/25">
                  <Star className="h-6 w-6 text-white" />
                </div>
              </div>
            </motion.div>

            {/* Overdue Tasks */}
            <motion.div
              whileHover={{ scale: 1.02, y: -2 }}
              className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-3xl p-6 shadow-xl shadow-black/5 border border-white/20 dark:border-gray-700/50"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">已逾期</p>
                  <p className="text-3xl font-bold text-red-600">{overdueCount}</p>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg shadow-red-500/25">
                  <AlertCircle className="h-6 w-6 text-white" />
                </div>
              </div>
            </motion.div>
          </motion.div>

          {/* Progress Bar */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-3xl p-6 shadow-xl shadow-black/5 border border-white/20 dark:border-gray-700/50"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">今日进度</h3>
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {todos.length > 0 ? `${Math.round((completedCount / todos.length) * 100)}%` : '0%'}
              </span>
            </div>
            <div className="relative w-full h-4 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
              <motion.div
                className="absolute top-0 left-0 h-full bg-gradient-to-r from-violet-500 via-purple-500 to-blue-500 rounded-full shadow-lg"
                initial={{ width: 0 }}
                animate={{ width: todos.length > 0 ? `${(completedCount / todos.length) * 100}%` : '0%' }}
                transition={{ duration: 1, ease: "easeOut" }}
              />
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent rounded-full"></div>
            </div>
          </motion.div>

          {/* Search and Filters */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-3xl p-6 shadow-xl shadow-black/5 border border-white/20 dark:border-gray-700/50"
          >
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="搜索任务..."
                  className="w-full pl-12 pr-4 py-3 bg-white/50 dark:bg-gray-700/50 border border-gray-200/50 dark:border-gray-600/50 rounded-2xl focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 backdrop-blur-sm transition-all"
                />
              </div>

              {/* Category Filter */}
              <select
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
                className="px-4 py-3 bg-white/50 dark:bg-gray-700/50 border border-gray-200/50 dark:border-gray-600/50 rounded-2xl focus:outline-none focus:ring-2 focus:ring-violet-500 text-gray-900 dark:text-white backdrop-blur-sm transition-all"
              >
                <option value="">所有分类</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>

              {/* Priority Filter */}
              <select
                value={filterPriority}
                onChange={(e) => setFilterPriority(e.target.value)}
                className="px-4 py-3 bg-white/50 dark:bg-gray-700/50 border border-gray-200/50 dark:border-gray-600/50 rounded-2xl focus:outline-none focus:ring-2 focus:ring-violet-500 text-gray-900 dark:text-white backdrop-blur-sm transition-all"
              >
                <option value="">所有优先级</option>
                <option value="high">高优先级</option>
                <option value="medium">中优先级</option>
                <option value="low">低优先级</option>
              </select>
            </div>

            <div className="flex items-center justify-between mt-4">
              <motion.button
                onClick={() => setShowCompleted(!showCompleted)}
                className={`px-4 py-2 rounded-2xl text-sm font-medium transition-all ${
                  showCompleted
                    ? 'bg-violet-500 text-white shadow-lg shadow-violet-500/25'
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                {showCompleted ? '隐藏已完成' : '显示已完成'}
              </motion.button>

              <div className="text-sm text-gray-600 dark:text-gray-400">
                找到 {filteredTodos.length} 个任务
              </div>
            </div>
          </motion.div>

          {/* Add Todo */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-black/5 border border-white/20 dark:border-gray-700/50 overflow-hidden"
          >
            <div className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-gradient-to-br from-violet-500 to-purple-500 rounded-xl flex items-center justify-center">
                  <Plus className="h-4 w-4 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">添加新任务</h3>
              </div>

              <div className="space-y-4">
                <input
                  type="text"
                  value={newTodo}
                  onChange={(e) => setNewTodo(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && !e.shiftKey && addTodo()}
                  placeholder="输入任务标题..."
                  className="w-full px-4 py-3 bg-white/50 dark:bg-gray-700/50 border border-gray-200/50 dark:border-gray-600/50 rounded-2xl focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 backdrop-blur-sm transition-all"
                />

                <AnimatePresence>
                  {isAddingTodo && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="space-y-4"
                    >
                      <textarea
                        value={newDescription}
                        onChange={(e) => setNewDescription(e.target.value)}
                        placeholder="任务描述（可选）..."
                        rows={3}
                        className="w-full px-4 py-3 bg-white/50 dark:bg-gray-700/50 border border-gray-200/50 dark:border-gray-600/50 rounded-2xl focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 backdrop-blur-sm transition-all resize-none"
                      />

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <select
                          value={newPriority}
                          onChange={(e) => setNewPriority(e.target.value as 'low' | 'medium' | 'high')}
                          className="px-4 py-3 bg-white/50 dark:bg-gray-700/50 border border-gray-200/50 dark:border-gray-600/50 rounded-2xl focus:outline-none focus:ring-2 focus:ring-violet-500 text-gray-900 dark:text-white backdrop-blur-sm transition-all"
                        >
                          <option value="low">低优先级</option>
                          <option value="medium">中优先级</option>
                          <option value="high">高优先级</option>
                        </select>

                        <select
                          value={newCategory}
                          onChange={(e) => setNewCategory(e.target.value)}
                          className="px-4 py-3 bg-white/50 dark:bg-gray-700/50 border border-gray-200/50 dark:border-gray-600/50 rounded-2xl focus:outline-none focus:ring-2 focus:ring-violet-500 text-gray-900 dark:text-white backdrop-blur-sm transition-all"
                        >
                          <option value="">选择分类</option>
                          {categories.map((category) => (
                            <option key={category.id} value={category.id}>
                              {category.name}
                            </option>
                          ))}
                        </select>

                        <input
                          type="datetime-local"
                          value={newDueDate}
                          onChange={(e) => setNewDueDate(e.target.value)}
                          className="px-4 py-3 bg-white/50 dark:bg-gray-700/50 border border-gray-200/50 dark:border-gray-600/50 rounded-2xl focus:outline-none focus:ring-2 focus:ring-violet-500 text-gray-900 dark:text-white backdrop-blur-sm transition-all"
                        />
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>

                <div className="flex items-center gap-3">
                  <motion.button
                    onClick={() => setIsAddingTodo(!isAddingTodo)}
                    className="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-2xl hover:bg-gray-200 dark:hover:bg-gray-600 transition-all text-sm font-medium"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    {isAddingTodo ? '简单模式' : '详细模式'}
                  </motion.button>

                  <motion.button
                    onClick={addTodo}
                    disabled={!newTodo.trim()}
                    className="flex-1 px-6 py-3 bg-gradient-to-r from-violet-500 via-purple-500 to-blue-500 text-white rounded-2xl hover:from-violet-600 hover:via-purple-600 hover:to-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all shadow-lg shadow-violet-500/25 font-medium"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="flex items-center justify-center gap-2">
                      <Plus className="h-5 w-5" />
                      添加任务
                    </div>
                  </motion.button>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Todo List */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="space-y-4"
          >
            {filteredTodos.length === 0 ? (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-3xl p-12 text-center shadow-xl shadow-black/5 border border-white/20 dark:border-gray-700/50"
              >
                <div className="w-20 h-20 bg-gradient-to-br from-violet-500/20 to-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Check className="h-10 w-10 text-violet-500" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                  {todos.length === 0 ? '还没有任务' : '没有匹配的任务'}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
                  {todos.length === 0
                    ? '开始添加您的第一个任务，让生活更有条理！'
                    : '尝试调整搜索条件或筛选器来找到您要的任务。'
                  }
                </p>
              </motion.div>
            ) : (
              <AnimatePresence>
                {filteredTodos.map((todo, index) => {
                  const category = categories.find(c => c.id === todo.category);
                  const isOverdue = todo.dueDate && !todo.completed && todo.dueDate < new Date();

                  return (
                    <motion.div
                      key={todo.id}
                      layout
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ delay: index * 0.05 }}
                      whileHover={{ scale: 1.01, y: -2 }}
                      className={`group bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-3xl p-6 shadow-xl shadow-black/5 border border-white/20 dark:border-gray-700/50 hover:shadow-2xl transition-all duration-300 ${
                        todo.completed ? 'opacity-75' : ''
                      } ${isOverdue ? 'ring-2 ring-red-500/20' : ''}`}
                    >
                      <div className="flex items-start gap-4">
                        {/* Checkbox */}
                        <motion.button
                          onClick={() => toggleTodo(todo.id)}
                          className={`relative w-7 h-7 rounded-2xl border-2 flex items-center justify-center transition-all duration-300 ${
                            todo.completed
                              ? 'bg-gradient-to-br from-emerald-500 to-green-500 border-emerald-500 shadow-lg shadow-emerald-500/25'
                              : 'border-gray-300 dark:border-gray-600 hover:border-violet-500 hover:bg-violet-50 dark:hover:bg-violet-900/20'
                          }`}
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          <AnimatePresence>
                            {todo.completed && (
                              <motion.div
                                initial={{ scale: 0, rotate: -180 }}
                                animate={{ scale: 1, rotate: 0 }}
                                exit={{ scale: 0, rotate: 180 }}
                                transition={{ duration: 0.3 }}
                              >
                                <Check className="h-4 w-4 text-white" />
                              </motion.div>
                            )}
                          </AnimatePresence>
                        </motion.button>

                        {/* Content */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between gap-3">
                            <div className="flex-1">
                              <h4 className={`text-lg font-semibold transition-all ${
                                todo.completed
                                  ? 'line-through text-gray-500 dark:text-gray-400'
                                  : 'text-gray-900 dark:text-white'
                              }`}>
                                {todo.title}
                              </h4>

                              {todo.description && (
                                <p className={`text-sm mt-1 transition-all ${
                                  todo.completed
                                    ? 'line-through text-gray-400 dark:text-gray-500'
                                    : 'text-gray-600 dark:text-gray-400'
                                }`}>
                                  {todo.description}
                                </p>
                              )}
                            </div>

                            {/* Important Star */}
                            <motion.button
                              onClick={() => toggleImportant(todo.id)}
                              className={`p-2 rounded-xl transition-all ${
                                todo.important
                                  ? 'text-amber-500 bg-amber-50 dark:bg-amber-900/20'
                                  : 'text-gray-400 hover:text-amber-500 hover:bg-amber-50 dark:hover:bg-amber-900/20'
                              }`}
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.9 }}
                            >
                              <Star className={`h-4 w-4 ${todo.important ? 'fill-current' : ''}`} />
                            </motion.button>
                          </div>

                          {/* Metadata */}
                          <div className="flex flex-wrap items-center gap-2 mt-3">
                            {/* Priority */}
                            <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${getPriorityColor(todo.priority)}`}>
                              {todo.priority === 'high' ? '高' : todo.priority === 'medium' ? '中' : '低'}优先级
                            </span>

                            {/* Category */}
                            {category && (
                              <span
                                className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium text-white shadow-sm"
                                style={{ backgroundColor: category.color }}
                              >
                                {category.name}
                              </span>
                            )}

                            {/* Due Date */}
                            {todo.dueDate && (
                              <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium ${
                                isOverdue
                                  ? 'text-red-600 bg-red-50 border border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800'
                                  : 'text-blue-600 bg-blue-50 border border-blue-200 dark:text-blue-400 dark:bg-blue-900/20 dark:border-blue-800'
                              }`}>
                                <Calendar className="h-3 w-3" />
                                {formatDate(todo.dueDate)}
                              </span>
                            )}

                            {/* Created Date */}
                            <span className="inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700">
                              <Clock className="h-3 w-3" />
                              {formatDate(todo.createdAt)}
                            </span>
                          </div>
                        </div>

                        {/* Actions */}
                        <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                          <motion.button
                            onClick={() => setEditingTodo(editingTodo === todo.id ? null : todo.id)}
                            className="p-2 rounded-xl text-gray-400 hover:text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all"
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                          >
                            <Edit2 className="h-4 w-4" />
                          </motion.button>

                          <motion.button
                            onClick={() => deleteTodo(todo.id)}
                            className="p-2 rounded-xl text-gray-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 transition-all"
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </motion.button>
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </AnimatePresence>
            )}
          </motion.div>
        </div>
      </main>

      {/* Footer */}
      <footer className="mt-20 border-t border-white/20 dark:border-gray-700/50 bg-white/50 dark:bg-gray-900/50 backdrop-blur-xl">
        <div className="max-w-6xl mx-auto px-6 py-8">
          <div className="text-center">
            <div className="flex items-center justify-center gap-2 mb-4">
              <div className="w-8 h-8 bg-gradient-to-br from-violet-500 via-purple-500 to-blue-500 rounded-xl flex items-center justify-center">
                <span className="text-white font-bold text-sm">✓</span>
              </div>
              <span className="text-lg font-bold bg-gradient-to-r from-gray-900 via-purple-900 to-violet-900 dark:from-white dark:via-purple-100 dark:to-violet-100 bg-clip-text text-transparent">
                TodoMaster
              </span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              让每一天都充满成就感 ✨
            </p>
            <div className="flex items-center justify-center gap-6 text-xs text-gray-500 dark:text-gray-500">
              <span>Next.js 15</span>
              <span>•</span>
              <span>TypeScript</span>
              <span>•</span>
              <span>Tailwind CSS</span>
              <span>•</span>
              <span>Framer Motion</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
